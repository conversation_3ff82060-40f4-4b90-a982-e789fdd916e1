# vidmob-test-automation

This project takes Playwright code api automation.

## Installation

npm install

## Usage

- Run all tests:

export TESTENV=<env>  # choose between dev/stage/prod
export EMAIL=<EMAIL>  # or your email account
export PASSWORD=<password>
export MFA_USER=<EMAIL>  # or your email account with MFA
export MFA_PASSWORD=<password>
export PLUGIN_EMAIL=<EMAIL>  # or your email account
export PLUGIN_PASSWORD=<password>
export ORG_ADMIN_EMAIL=<EMAIL>  # or your email account
export ORG_ADMIN_PASSWORD=<password>

cd api-playwright
npx playwright test --reporter=list,html --retries=2

- Alternative: use local .env file:

source .env
cd api-playwright
npx playwright test --reporter=list,html --retries=2

- Run by tag:

source .env
cd api-playwright
npx playwright test -g "<tag name>" --reporter=list,html --retries=2

Available tag names:

@plugin_api_bff
@apiRegressionTest
@api_auth
@api_sanity
@api_bvt

- Generate allure report

npx allure generate ./allure-results --clean

- Display allure report

npx allure serve ./allure-results

##### Examples running

npx playwright test -g @plugin_api_bff --reporter=list,html --retries=2

##### Staging Environment:

- run analytics smoke (stage/dev only):

TESTENV=dev  # choose between dev/stage
npx playwright test -g @an_smoke

## To start a new test suite in a new folder:

npm init playwright@latest

details here: https://playwright.dev/docs/intro
