import { Page } from 'playwright'
import { CreatePageInstances } from '../../utils/helper'

export class StudOutputs {
	private page: Page
    
	constructor(page: Page) {
		this.page = page
	}

	public async gotToOutputs(page: Page) {
		const {common } = CreatePageInstances(page)  

		await page.getByRole('button', { name: 'Outputs' }).click();
        
        await page.waitForLoadState('load');
        await common.delay(7000)
        await page.setDefaultNavigationTimeout(40000);

	}
}