import { Page } from 'playwright'
import { CreatePageInstances } from '../../utils/helper'

export class StudBrief {
	private page: Page
    
	constructor(page: Page) {
		this.page = page
	}

	public async gotToBrief(page: Page) {
		const {common } = CreatePageInstances(page)  

		await page.getByRole('button', { name: 'Brief' }).click();
        
        await page.waitForLoadState('load');
        await common.delay(7000)
        await page.setDefaultNavigationTimeout(40000);

	}
}