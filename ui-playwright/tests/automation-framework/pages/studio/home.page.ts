import { Page, expect } from 'playwright/test'
import { CreatePageInstances } from '../../utils/helper'
import { StudHomeModel } from '../../model/studio/home.model'

export class StudHome {
	private page: Page
    
	constructor(page: Page) {
		this.page = page
	}

	public async gotToStudioPage(page: Page) {
		const {common } = CreatePageInstances(page)  

		const currentUrl = await page.url();
        await page.goto(currentUrl+'projects/active');
        
        await page.waitForLoadState('load');
        await common.delay(7000)
        await page.setDefaultNavigationTimeout(40000);

	}


	public async gotToProject(page: Page, studHomeModel: StudHomeModel ) {
		const {common } = CreatePageInstances(page)  
		
		await page.locator('.search-icon').first().click();
		await common.delay(500);
  		await page.getByRole('textbox', { name: 'Search' }).fill(studHomeModel.projectName);
  		await common.delay(500);
		await page.getByRole('textbox', { name: 'Search' }).press('Enter');
		await common.delay(500);
  		await page.getByRole('link', { name: studHomeModel.projectName }).click();

		await common.delay(4000);

		try{
			const element = await page.waitForSelector('button:has-text("Sounds good, let\'s get")',{ timeout: 3000 });
			if (element) {
				await element.click();
				await common.delay(2000);
			} else {
				console.log('You’ve started a new project modal not displayed.');
			}
		}catch (error){
			console.log('Project already started.')
			
		}
		await page.waitForLoadState('load');
        await page.setDefaultNavigationTimeout(40000);

		await expect(page.getByRole('link', { name: 'Projects' })).toBeVisible({ timeout:30000 });
	}
}