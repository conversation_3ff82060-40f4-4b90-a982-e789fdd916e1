/* eslint-disable @typescript-eslint/no-unused-vars */
import { Page } from 'playwright'
import { expect } from '@playwright/test'
import { CreatePageInstances } from '../../utils/helper'
import { AnReportModel } from '../../model/analytics/anReport.model'

export class AnalyticsReport {
	private page: Page
	
	constructor(page: Page) {
		this.page = page
	}

	public async goToReport(page: Page) {
        const {common } = CreatePageInstances(page)  
        const env =process.env.TESTENV;
		if (env != "prod"){
			await page.goto('https://acs-'+env+'.vidmob.com/creativeIntelligence/savedReports');
		}else{
			await page.goto('https://acs.vidmob.com/creativeIntelligence/savedReports');
		}
		
		await page.waitForLoadState('load');
		await common.delay(7000)
		await page.setDefaultNavigationTimeout(40000);
        await page.waitForSelector('h5:has-text("Reports")', { timeout: 40000 });

    }

	public async createAnReport(page: Page, reportModel: AnReportModel) {
		const {common } = CreatePageInstances(page)  

		await page.getByRole('button', { name: 'Create report' }).click();
		await common.delay(1000)

		//click on report type
		await page.getByRole('heading', { name: reportModel.type}).click();
		await common.delay(1000)

  		await page.getByRole('button', { name: 'Create' }).click();
        await common.delay(5000)

        //enter report name
        const timestamp = new Date().getTime();
		const reportName = 'Test automation '+timestamp
		await page.getByPlaceholder('Untitled').fill(reportName);
		await common.delay(3000)
		await page.waitForLoadState('load');

		await page.getByRole('button', { name: 'Save report' }).click({ timeout:35000 });
		await common.delay(3000)
		await page.getByRole('button', { name: 'Save' }).click();
    	await common.delay(5000)
        try{
			const textelement = await page.inputValue('#header-title-field')
			//await expect(textelement).toContain(reportName);
			await expect(textelement).toBe(reportName);
		}catch (error){
			console.log('Error on create report '+reportModel.type, error)
			throw error;
		}
	}

	public async updateAnReport(page: Page, reportModel: AnReportModel) {
		const {common } = CreatePageInstances(page)  
		const timestamp = new Date().getTime();
		const reportName = 'Test automation updated '+timestamp

		await page.click('.report-name-column-cell')
		
		await page.waitForLoadState('load');
		await common.delay(3000)

		await page.click('#header-title-field')
		
		await page.getByPlaceholder('Untitled').fill(reportName);
		await common.delay(1000)
  		await page.getByRole('button', { name: 'Update report' }).click();

		const element = await page.getByText('By saving this report other'); 
		if (await element.isVisible()) {
			await page.getByRole('button', { name: 'Save' }).click();  
			await common.delay(1000)
		} else {
			console.log('Others report dialog not displayed');
		}
		try{
			const modalLocator = page.getByText('Success! Report saved.');
			await expect(modalLocator).toBeVisible({ timeout: 7000 });
			
		}catch (error){
			console.log('Error on update report ', error)
			throw error;
		}

	}	

	public async createInsight(page: Page, reportModel: AnReportModel) {
		const {common } = CreatePageInstances(page)  

		await page.click('.report-name-column-cell')
   		await common.delay(3000)

		await page.getByLabel('Insight').click();
		await common.delay(500)
		await page.getByRole('textbox').nth(2).fill('This is test');
		await common.delay(500)
		await page.getByRole('textbox').nth(3).fill('This is test automation recommendation');
		await common.delay(500)
		await page.getByRole('button', { name: 'Select category' }).click();
		await common.delay(1000)
		await page.locator('li').filter({ hasText: 'Color' }).locator('label div').click();
		await common.delay(500)
		await page.getByRole('textbox').nth(3).click();
		await common.delay(500)
		await page.getByRole('button', { name: 'Publish' }).click();

		try{
			const modalLocator = page.getByText('Your insight is published in the Insights Library.');
			await expect(modalLocator).toBeVisible({ timeout: 7000 });
			
		}catch (error){
			console.log('Error on create insight', error)
			throw error;
		}
	}	

	public async renameByReports(page: Page, reportModel: AnReportModel) {
		
		const {common } = CreatePageInstances(page)  

		await page.locator('button[aria-label="more"]').nth(0).click(); //click in first report in the list
		await common.delay(1000)
		
		await page.getByRole('menuitem', { name: 'Rename' }).click();
		await common.delay(1000)
		
		await page.fill('//html/body/div[8]/div[3]/div/div[1]/div[1]/div/input', 'Report Renamed');
		await common.delay(1000)
  		await page.getByRole('button', { name: 'Rename' }).click();
		try{
			const modalLocator = page.getByText('Report updated successfully.');
			await expect(modalLocator).toBeVisible({ timeout: 7000 });
			
		}catch (error){
			console.log('Error on rename report ', error)
			throw error;
		}

	}	

	//no error raised
	public async deleteReportTeardown(page: Page, reportModel: AnReportModel) {
		const {common } = CreatePageInstances(page)  

		await page.getByRole('row', { name: reportModel.reportName }).getByLabel('more').click();
  		await page.getByRole('menuitem', { name: 'Delete' }).click();
		await page.getByRole('button', { name: 'Delete' }).click();
  		
		try{
			const modalLocator = page.getByText('Report deleted successfully.');
			await expect(modalLocator).toBeVisible({ timeout: 7000 });
			
		}catch (error){
			console.log('Error on delete report ', error)
		}

	}	

	public async duplicateReport(page: Page, reportModel: AnReportModel) {
		const {common } = CreatePageInstances(page)  

		await page.locator('button[aria-label="more"]').nth(0).click(); //click in first report in the list
		await common.delay(1000)
		await page.getByRole('menuitem', { name: 'Duplicate' }).click();
		try{
			const modalLocator = page.getByText('Report duplicated successfully.');
			await expect(modalLocator).toBeVisible({ timeout: 15000 });
			
		}catch (error){
			console.log('Error on duplicate report ', error)
			throw error;
		}

	}	
}
