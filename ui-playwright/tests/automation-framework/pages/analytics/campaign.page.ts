/* eslint-disable @typescript-eslint/no-unused-vars */
import { Page } from 'playwright'
import { CreatePageInstances } from '../../utils/helper'
import { AnCampaignModel } from '../../model/analytics/anCampaign.model'

export class AnCampaign {
	private page: Page
    dateRange: string
	
	constructor(page: Page) {
		this.page = page
	}

	public async gotToAnalyticsPage(page: Page) {
		const {common } = CreatePageInstances(page)  
        await common.delay(7000)

		const currentUrl = await page.url();
        await page.goto(currentUrl+'creativeIntelligence/creativeManager');

        await page.waitForLoadState('load');
        await common.delay(7000)
        await page.setDefaultNavigationTimeout(40000);
        await page.waitForSelector('div.title span:has-text("Analytics")', { timeout: 45000 });

	}

    public async selectDateRange(page: Page, anCampaign: AnCampaignModel) {
        const {common } = CreatePageInstances(page)  
        
        await page.click('//html[1]/body[1]/div[1]/div[1]/div[2]/main[1]/div[1]/main[1]/div[1]/div[1]/div[2]/div[1]/button[1]'); 
        await common.delay(3000)
        
        await page.waitForSelector('div:has-text("Select Date Range")'); //check calendar opening
        
        await page.click('span:has-text("'+anCampaign.dateRange+'")'); //click label
        await common.delay(3000)
        await page.waitForLoadState('load');
        await page.waitForLoadState('domcontentloaded');
        
        
        await page.click('button:has-text("OK")') // Click "OK" button
        await common.delay(3000)
        
        console.log('Finished date range selection')
    }

    public async selectAccountFilter(page: Page, anCampaign: AnCampaignModel) {
		const {common } = CreatePageInstances(page)  

        await page.locator('#analytics-filter-v2-scope-section').getByRole('button', { name: '-select an option-' }).click();
        await common.delay(1000)
        await page.waitForLoadState('load');
        
        
        await page.locator('//*[@id="dropdown-menu"]/div[3]/ul/div/div[2]/div[1]/li/div/div/span/input').click(); //select all
        
        await common.delay(1000)

        await page.keyboard.press('Escape'); //click outside area
        await common.delay(2000)
	}

    public async selectWorkspaceFilter(page: Page, anCampaign: AnCampaignModel) {
        const {common } = CreatePageInstances(page)  
        
        await page.locator('fieldset').filter({ hasText: 'Workspace name' }).getByRole('button').click();
        await common.delay(1000)
        await page.waitForLoadState('load');
        
        
        await page.click('//*[@id="dropdown-menu"]/div[3]/ul/div/div[2]/div[1]/li/div/div/span/input') //select all workspace
        await common.delay(500)

        await page.keyboard.press('Escape'); //click outside workspace area
        await common.delay(2000)
        
    }

    public async selectChannelFilter(page: Page, anCampaign: AnCampaignModel) {
        const {common } = CreatePageInstances(page)  
        
        await page.locator('fieldset').filter({ hasText: 'Channel-select an option-' }).getByRole('button').click();
        await common.delay(1000)
        await page.waitForLoadState('load');

        await page.getByLabel(anCampaign.channel, { exact: true }).click();
        
        await common.delay(2000)
        
    }

    public async selectMetric(page: Page, anCampaign: AnCampaignModel) {
        const {common } = CreatePageInstances(page)  
        
        await page.locator('#analytics-filter-v2-metrics-section').getByRole('button', { name: '-select an option-' }).click();
        await common.delay(1000)
        await page.waitForLoadState('load');
        await page.getByLabel('View Through to '+anCampaign.metric).click();
        
        await page.getByRole('button', { name: 'Apply' }).click();
        await common.delay(2000)
        
    }
}