import { Page } from 'playwright'
import { expect } from '@playwright/test'
import { CreatePageInstances } from '../../utils/helper'
import { ScoCriteriaModel } from '../../model/scoring/scoCriteria.model'

export class ScoCriteria {
	private page: Page
	
	constructor(page: Page) {
		this.page = page
	}

	public async goToCriteria(page: Page) {
        const {common } = CreatePageInstances(page)  
        const currentUrl = await page.url();
        await page.goto(currentUrl+'creativeIntelligence/creative-scoring/criteria-management');
		
		await page.waitForLoadState('load');
		await common.delay(7000)
		await page.setDefaultNavigationTimeout(40000);
		
		const modal = await page.locator('#modal-compliance-create-best-practice-criteria'); 
		
		if (await modal.isVisible()) {
			await page.locator('#modal-compliance-create-best-practice-criteria').getByRole('img').first().click();
			await common.delay(1000)
		} else {
			console.log('Modal not visible');
		}

		await common.delay(1000)
        await page.waitForSelector('h5:has-text("Criteria")', { timeout: 40000 });
    }

	public async addCriteria(page: Page, criteriaModel: ScoCriteriaModel) {
        const {common } = CreatePageInstances(page)  

		await page.getByRole('button', { name: 'Add criteria' }).click();
  		await page.click('body > div.MuiDialog-root.MuiModal-root.css-126xj0f > div.MuiDialog-container.MuiDialog-scrollPaper.css-ekeie0 > div > div.MuiDialogContent-root.css-1al0qnz > div.MuiFormControl-root.css-11rrq3l > div:nth-child(2) > div');
		await common.delay(500)
		
		const channel = criteriaModel.channel+' '+criteriaModel.channel //workaound channel duplicated in html

		await page.getByRole('button', { name: channel , exact: true }).click(); //select channel
		await common.delay(500)
		await page.getByRole('button', { name: criteriaModel.channel, exact: true }).click();
		await common.delay(1000)

		await page.keyboard.press('Escape'); //click outside workspace area


		await page.click('body > div.MuiDialog-root.MuiModal-root.css-126xj0f > div.MuiDialog-container.MuiDialog-scrollPaper.css-ekeie0 > div > div.MuiDialogContent-root.css-1al0qnz > div.drop-down-criteria-wrapper.MuiBox-root.css-i3pbo > div > div'); //select criteria
		await common.delay(500)

		await page.getByRole('option', { name: criteriaModel.criteria }).click();
		await common.delay(1000)

		
		const element = await page.locator('#modal-compliance-create-criteria'); 

		if (await element.isVisible()) {
			await element.click();
			//await page.getByLabel('custom name').click();
			await page.getByRole('option', { name: criteriaModel.criteriaName }).click();
			await common.delay(1000)
		} else {
			console.log('Criteria Name element is not visible');
		}

		await page.getByPlaceholder('Example: Perfect slumber with').fill('This is test');
		await common.delay(1000)

		await page.getByLabel(criteriaModel.creativeType).check();
		await common.delay(1000)

		await page.locator('body > div.MuiDialog-root.MuiModal-root.css-126xj0f > div.MuiDialog-container.MuiDialog-scrollPaper.css-ekeie0 > div > div.MuiDialogContent-root.css-1al0qnz > div.MuiFormControl-root.css-1nvk1s4 > div.MuiFormGroup-root.MuiFormGroup-row.css-smv1ps > label:nth-child(2) > span.MuiTypography-root.MuiTypography-body1.MuiFormControlLabel-label.css-18yr27b').filter({ hasText: criteriaModel.consideration }).check();
		await common.delay(2000)

		await page.getByRole('button', { name: 'Save' }).click();

		await common.delay(3000)
		const textelement = await page.textContent('#app-root > div.MuiSnackbar-root.MuiSnackbar-anchorOriginBottomRight.css-xe3ssy > div > div.MuiAlert-message.css-1xsto0d > div > p')
		await expect(textelement).toContain('Criteria successfully added');
    }
}
