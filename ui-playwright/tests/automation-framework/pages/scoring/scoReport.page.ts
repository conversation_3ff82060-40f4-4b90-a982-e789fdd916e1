/* eslint-disable @typescript-eslint/no-unused-vars */
import { Page } from 'playwright'
import { expect } from '@playwright/test'
import { CreatePageInstances } from '../../utils/helper'
import { ScoReportModel } from '../../model/scoring/scoReport.model'

export class ScoringReport {
	private page: Page

	constructor(page: Page) {
		this.page = page
	}

	public async goToReports(page: Page) {
        const { common } = CreatePageInstances(page)
        const env = process.env.TESTENV;
		if (env != "prod"){
			await page.goto('https://acs-'+env+'.vidmob.com/creativeIntelligence/creative-scoring/reports');
		} else {
			await page.goto('https://acs.vidmob.com/creativeIntelligence/creative-scoring/reports');
		}

		await page.waitForLoadState('load');
		await common.delay(7000)
		await page.setDefaultNavigationTimeout(40000);
        await page.waitForSelector('h5:has-text("Reports")', { timeout: 40000 });

    }

	public async createScoReport(page: Page, reportModel:ScoReportModel) {
		const { common } = CreatePageInstances(page)

		await page.getByRole('button', { name: 'Create report' }).click();
		await common.delay(1000)

		//click on report type
		await page.getByRole('heading', { name: reportModel.type, exact: true }).click();
		await common.delay(7000)
		await page.waitForLoadState('load');

		await page.getByRole('button', { name: 'Create' }).click();
		await common.delay(15000)

		//enter report name
        const timestamp = new Date().getTime();
		const reportName = 'Test automation '+timestamp
		await common.delay(5000)
		await page.getByPlaceholder('Add report name').fill(reportName);
  		await common.delay(3000)

		await page.getByRole('button', { name: 'Save' }).click({ timeout:35000 });

		try {
			const modalLocator = page.getByText('Success! Report saved.');
			await expect(modalLocator).toBeVisible({ timeout: 15000 });
		} catch (error) {
			console.log('Error on create report '+reportModel.type, error)
			throw error;
		}
	}

	public async updateAnReport(page: Page) {
		const { common } = CreatePageInstances(page)
		const timestamp = new Date().getTime();
		const reportName = 'Test automation updated '+ timestamp

		await page.click('.report-name-column-cell')

		await page.waitForLoadState('load');
		await common.delay(3000)

		await page.getByPlaceholder('Add report name').fill(reportName);
  		await page.getByRole('button', { name: 'Save' }).click();

		try {
			const modalLocator = page.getByText('Success! Report saved.');
			await expect(modalLocator).toBeVisible({ timeout: 7000 });

		} catch (error) {
			console.log('Error on update report ', error)
			throw error;
		}
	}


	public async renameByReports(page: Page) {

		const {common } = CreatePageInstances(page)

		await page.locator('button[aria-label="more"]').nth(0).click(); //click in first report in the list
		await common.delay(1000)

		await page.getByRole('menuitem', { name: 'Rename' }).click();
		await common.delay(1000)
		await page.fill('//html/body/div[8]/div[3]/div/div[1]/div/div[1]/div/textarea[1]', 'Report Renamed');
		await common.delay(1000)
		await page.getByLabel('Rename report').getByText('Report renamed').click();
  		await page.getByRole('button', { name: 'Save' }).click();

  		try {
			const text = await page.textContent('//*[@id="compliance-content-area"]/div[3]/div[1]/div[1]/div[2]/div/div/div[1]/div[1]/div/p')
			const modalLocator = page.getByText('Report updated successfully.');
			await expect(text).toBe("Report Renamed");

		} catch (error) {
			console.log('Error on rename report ', error)
			throw error;
		}
	}

	//no error raised
	public async deleteReportTeardown(page: Page, reportModel: ScoReportModel) {
		const { common } = CreatePageInstances(page)

		await page.getByRole('row', { name: reportModel.reportName }).getByLabel('more').click();
  		await page.getByRole('menuitem', { name: 'Delete' }).click();
		await page.getByRole('button', { name: 'Delete' }).click();

		try {
			const modalLocator = page.getByText('Report deleted successfully.');
			await expect(modalLocator).toBeVisible({ timeout: 7000 });

		} catch (error) {
			console.log('Error on delete report ', error)
		}

	}

	public async duplicateReport(page: Page) {
		const {common } = CreatePageInstances(page)

		await page.locator('button[aria-label="more"]').nth(0).click(); //click in first report in the list
		const reportTitle = await page.locator('button[aria-label="more"]').nth(0)
		console.log(reportTitle);
		await common.delay(1000)
		await page.getByRole('menuitem', { name: 'Duplicate' }).click();
		try {
			const reportCopyText = await page.locator('button[aria-label="more"]').nth(0).innerText()
			await expect(reportCopyText).toBe(reportTitle + "Copy(1)");

		} catch (error) {
			console.log('Error on duplicate report ', error)
			throw error;
		}
	}
}
