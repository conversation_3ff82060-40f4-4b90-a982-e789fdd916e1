import { Page } from 'playwright'
import { CreatePageInstances } from '../../utils/helper'

export class PreFlightChecks {
	private page: Page

	constructor(page: Page) {
		this.page = page
	}

	public async goToPreFlightChecks(page: Page) {
        const { common } = CreatePageInstances(page)
        const currentUrl = await page.url();
        await page.goto(currentUrl+'creativeIntelligence/creative-scoring/scorecards-landing?types=PRE_FLIGHT');

		await page.waitForLoadState('load');
		await common.delay(7000)
		await page.setDefaultNavigationTimeout(40000);
        await page.waitForSelector('h5:has-text("Pre-flight")', { timeout: 40000 });
    }

}
