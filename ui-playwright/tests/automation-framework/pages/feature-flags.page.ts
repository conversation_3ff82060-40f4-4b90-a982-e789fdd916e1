/* eslint-disable @typescript-eslint/no-unused-vars */
import { Page } from 'playwright'
import { CreatePageInstances } from '../utils/helper'
import { FeatureFlagsModel } from '../model/feature-flags.model'
import * as dotenv from 'dotenv'

export class FeatureFlags {
	private page: Page
	
	constructor(page: Page) {
		this.page = page
	}

	public async setFeatureFlag(page: Page, featureFlag: FeatureFlagsModel) {
		const { getURL, common } = CreatePageInstances(page)  

		const env: string = process.env.TESTENV || '';
        const baseURL = getURL.returnURL('flag-'+env) || '';

		await page.goto(baseURL);

		try{
			const timeoutValue = 2000; // Timeout in milliseconds

			await page.getByRole('row', { name: featureFlag.flag + ' Disabled' })
					  .locator('span')
					  .first()
					  .click({ timeout: timeoutValue });
	
			await common.delay(500);
		}catch (error){
			console.log('Feature flag is already enabled!', error)
		}
	}
}
