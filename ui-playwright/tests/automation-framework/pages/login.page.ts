import { Page } from 'playwright'
import { CreatePageInstances } from '../utils/helper'
import { LoginModel } from '../model/login.model'

export class Login {
	private page: Page
	
	constructor(page: Page) {
		this.page = page
	}

	public async acsLogin(page: Page, login: LoginModel) {
		const { getURL, common } = CreatePageInstances(page)  

		const env: string = process.env.TESTENV || '';
        const baseURL = getURL.returnURL('acs-'+env) || '';

		await page.goto(baseURL);
		await page.waitForLoadState('load');
		await page.setDefaultNavigationTimeout(40000);
    
        await page.fill('input[type="email"]',login.username);
        await page.getByRole('button', { name: 'Log in' }).click();
		await common.delay(2000);

    
        await page.fill('input[type="password"]',login.password);
        await common.delay(2000);
        await page.getByRole('button', { name: 'Log in' }).click();
        
        await page.waitForLoadState('load');
		await common.delay(7000)
	}
}
