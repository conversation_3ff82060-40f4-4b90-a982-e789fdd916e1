import { Page } from 'playwright/test'
import { CreatePageInstances } from '../../utils/helper'

export class PlatAssetsLocker {
	private page: Page
    dateRange: string
	
	constructor(page: Page) {
		this.page = page
	}

	public async gotToAssets(page: Page) {
		const {common } = CreatePageInstances(page)  
        const currentUrl = await page.url();
        await page.goto(currentUrl+'partner/asset-locker');
		
		await page.waitForLoadState('load');
		await common.delay(7000)
		await page.setDefaultNavigationTimeout(40000);

		await page.waitForSelector('h5:has-text("Asset locker")', { timeout: 40000 });

	}
}