import { Page } from 'playwright'
import { CreatePageInstances } from '../utils/helper'
import { AcsModel } from '../model/acs.model'

export class Acs {
	private page: Page
	
	constructor(page: Page) {
		this.page = page
	}

	public async selectOrganization(page: Page, org: AcsModel) {
		const {common } = CreatePageInstances(page)  
		
		//await page.goto(baseURL);
		await common.delay(5000)
		//await page.click(org.organization);
		await page.getByRole('heading', { name: org.organization }).click();
        await common.delay(500)

		const nextBtn = await page.isVisible('button:has-text("Next")');

		if(nextBtn){
			await page.getByRole('button', { name: 'Next' }).click();
		}else{
			await page.getByRole('button', { name: 'Switch' }).click();
		}
		await page.waitForLoadState('load');
		await common.delay(7000)
		await page.setDefaultNavigationTimeout(40000);
        
	}
}
