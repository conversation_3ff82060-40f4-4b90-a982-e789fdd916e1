import { Page } from '@playwright/test';
import { GetURL } from './getURL'
import { Common } from './common'
import { Login } from '../pages/login.page'
import { AnCampaign } from '../pages/analytics/campaign.page'
import { Acs } from '../pages/acs.page'
import {FeatureFlags} from '../pages/feature-flags.page'
import {AnalyticsReport} from '../pages/analytics/report.page'
import {ScoCriteria} from '../pages/scoring/criteria.page'
import {PreFlightChecks} from '../pages/scoring/scorecards.page'
import {ScoringReport} from '../pages/scoring/scoReport.page'

import {StudAssets} from '../pages/studio/assets.page'
import {StudBrief} from '../pages/studio/brief.page'
import {StudHome} from '../pages/studio/home.page'
import {StudNotes} from '../pages/studio/notes.page'
import {StudOutputs} from '../pages/studio/outputs.page'
import {StudProjectTeam} from '../pages/studio/projectTeam.page'

export function CreatePageInstances(page: Page) {
	return {
		getURL: new GetURL(page),
		common: new Common(page),
		login: new Login(page),
		anCampaign: new AnCampaign(page),
		acs: new Acs(page),
		featureFlags: new FeatureFlags(page),
		//Scoring
		anReport: new AnalyticsReport(page),
		scoCriteria: new ScoCriteria(page),
		scorecards: new PreFlightChecks(page),
		scoReport: new ScoringReport(page),

		//Studio
		studAssets: new StudAssets(page),
		studBrief: new StudBrief(page),
		studHome: new StudHome(page),
		studNotes: new StudNotes(page),
		studOutputs: new StudOutputs(page),
		studProjectTeam: new StudProjectTeam(page),

	}
}
