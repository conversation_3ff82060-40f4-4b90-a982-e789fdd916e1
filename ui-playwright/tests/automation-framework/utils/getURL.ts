import { Page } from '@playwright/test'

export class GetURL {
	private page: Page

	constructor(page: Page) {
		this.page = page
	}

	public returnURL(key: string) {
		switch (key) {
			case 'acs-dev':
				return 'https://acs-dev.vidmob.com/'
			case 'acs-stage':
				return 'https://acs-stage.vidmob.com/'
			case 'acs-prod':
				return 'https://acs.vidmob.com/'				
			case 'org-stage':
				return 'https://acs-stage.vidmob.com/organizations/'
			case 'org-dev':
				return 'https://acs-dev.vidmob.com/organizations/'
			case 'org-prod':
				return 'https://acs.vidmob.com/organizations/'
			case 'flag-stage':
				return 'https://acs-stage.vidmob.com/__featureflags'
			case 'flag-dev':
				return 'https://acs-dev.vidmob.com/__featureflags'
		}
	}
}
