export class AnCampaignModel {
	account: string
    dateRange: string
	workspace: string
	channel: string
	metric: string

	private constructor(account,dateRange,workspace,channel, metric) {
        this.account = account
        this.dateRange = dateRange
		this.workspace = workspace
		this.channel = channel
		this.metric = metric
	}

	public static of(account: string, dateRange: string, workspace: string, channel: string, metric: string): AnCampaignModel {
		return new AnCampaignModel(account, dateRange, workspace, channel, metric)
	}
}
