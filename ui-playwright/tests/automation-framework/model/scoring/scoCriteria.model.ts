export class ScoCriteriaModel {
	channel: string
	criteria: string
	consideration: string
	criteriaName: string
	creativeType: string
	
	private constructor(channel, criteria, consideration,criteriaName,creativeType) {
		this.channel = channel
		this.criteria = criteria
		this.consideration = consideration
		this.criteriaName = criteriaName
		this.creativeType = creativeType
    
	}

	public static of(channel: string, criteria: string, consideration:string, criteriaName:string, creativeType:string): ScoCriteriaModel {
		return new ScoCriteriaModel(channel, criteria, consideration,criteriaName,creativeType)
	}
}
