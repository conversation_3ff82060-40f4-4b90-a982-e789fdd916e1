import { test, expect } from '@playwright/test';
import * as wsFeeder from '../../automation-framework/feeder/workspace.json'
import { CreatePageInstances } from '../../automation-framework/utils/helper'
import { AcsModel, LoginModel } from '../../automation-framework/model'
import { AnReportModel } from '../../automation-framework/model/analytics/anReport.model'

const env = process.env.TESTENV || "";
const email = process.env.EMAIL || '';
const password= process.env.PASSWORD || '';
const organization = wsFeeder[env].vidMob

type getAn = {
    orgLocator: string;
    testScenario: string;
    channel: string;
    metric: string;
    username:string;
    password: string;
  };
  
  const an: getAn[] = [];
  an.push({ orgLocator:organization, testScenario: "Element impact", channel: "Facebook", metric: '25%', username: email, password: password });
  an.push({ orgLocator:organization, testScenario: "Media impact", channel: "Facebook", metric: '25%', username: email, password: password });
  
an.forEach((scenario) => {

    test('ANALYTICS ACS - Verify create report is available '+scenario.testScenario+' impact - '+scenario.channel+'  -- @acs_sanity @an_sanity', async ({ page }) => {
      test.slow()
      const {common, login, acs, anReport} = CreatePageInstances(page)  
      const loginModel: LoginModel = LoginModel.of(scenario.username,scenario.password)
      const acsModel: AcsModel = AcsModel.of(scenario.orgLocator)
      
      await login.acsLogin(page,loginModel);

      await acs.selectOrganization(page,acsModel);

      await anReport.goToReport(page);

      await common.delay(3000);

      await expect(page.locator('button').filter({ hasText: 'Create report' }).first()).toBeVisible({ timeout:45000 });//verify create is visible

      await page.getByRole('button', { name: 'Create report' }).first().click();
      await common.delay(3000);
      
      await expect(page.getByLabel('Create a new report').getByText('Create a new report')).toBeVisible({ timeout:45000 });
  });
})

const ansanity: getAn[] = [];
ansanity.push({ orgLocator:organization, testScenario: "Element impact", channel: "Facebook", metric: '25%', username: email, password: password });  
ansanity.forEach((scenario) => {

    test('ANALYTICS ACS - Verify if elements in Campaign page is displayed  -- @acs_sanity @an_sanity @an_regression', async ({ page }) => {
      test.slow()
      const {login , anCampaign, acs} = CreatePageInstances(page)  
      const loginModel: LoginModel = LoginModel.of(scenario.username,scenario.password)
      const acsModel: AcsModel = AcsModel.of(scenario.orgLocator)
      
      await login.acsLogin(page,loginModel);
  
      await acs.selectOrganization(page,acsModel);
  
      await anCampaign.gotToAnalyticsPage(page);
  
      await expect(page.locator('h5')).toBeVisible({ timeout:45000 });
      await expect(page.getByLabel('Download')).toBeVisible();
      await expect(page.getByRole('heading', { name: 'Filters', exact: true })).toBeVisible();
      await expect(page.locator('#analytics-filter-v2-scope-section')).toContainText('Scope');
      await expect(page.getByRole('heading', { name: 'Workspace name' })).toBeVisible();
      await expect(page.getByRole('heading', { name: 'Channel' })).toBeVisible();
      await expect(page.getByRole('button', { name: 'Metric' })).toBeVisible();
      await expect(page.getByRole('button', { name: 'Advanced Filters' })).toBeVisible();
  
  });    
  
  test('ANALYTICS ACS - Update '+scenario.testScenario+' impact - '+scenario.channel+'  -- @acs_sanity @an_sanity', async ({ page }) => {
    test.skip(env != "stage"); //skip due to test data mass
    test.slow()
    const {login, acs, anReport} = CreatePageInstances(page)  
    const loginModel: LoginModel = LoginModel.of(scenario.username,scenario.password)
    const acsModel: AcsModel = AcsModel.of(scenario.orgLocator)
    const anReportModel: AnReportModel = AnReportModel.of(scenario.testScenario,"")
    
    await login.acsLogin(page,loginModel);
  
    await acs.selectOrganization(page,acsModel);
  
    await anReport.goToReport(page);
  
    await anReport.updateAnReport(page,anReportModel);
  
    await expect(page.getByRole('button', { name: 'Update report' })).toBeVisible({ timeout:45000 });
  
  });
  
  test('ANALYTICS ACS - Save & Reopen '+scenario.testScenario+' impact - '+scenario.channel+'  -- @acs_sanity @an_sanity', async ({ page }) => {
    test.skip(); //skip due instability from pipe
    test.slow()
    const {common, login, acs, anReport} = CreatePageInstances(page)  
    const loginModel: LoginModel = LoginModel.of(scenario.username,scenario.password)
    const acsModel: AcsModel = AcsModel.of(scenario.orgLocator)
    const anReportModel: AnReportModel = AnReportModel.of(scenario.testScenario,"")
    
    await login.acsLogin(page,loginModel);
  
    await acs.selectOrganization(page,acsModel);
  
    await anReport.goToReport(page);
  
    await anReport.updateAnReport(page,anReportModel); //save
    await common.delay(2000)

    await page.getByRole('listitem').getByRole('link', { name: 'Reports' }).click(); //close
    await common.delay(5000)

    await expect(page.locator('h5')).toContainText('Reports');

    await page.click('.report-name-column-cell')
    await common.delay(3000)

    await expect(page.locator('span').filter({ hasText: 'Update report' })).toBeVisible({ timeout:45000 });
    
  });

  test('ANALYTICS ACS - Download '+scenario.testScenario+' impact - '+scenario.channel+'  -- @acs_sanity  @an_sanity', async ({ page }) => {
    test.skip()//till get stability
    test.slow()
    const {common, login, acs, anReport} = CreatePageInstances(page)  
    const loginModel: LoginModel = LoginModel.of(scenario.username,scenario.password)
    const acsModel: AcsModel = AcsModel.of(scenario.orgLocator)
    
    await login.acsLogin(page,loginModel);
  
    await acs.selectOrganization(page,acsModel);
  
    await anReport.goToReport(page);
  
    await common.delay(3000);
  
    await page.click('.report-name-column-cell'); //click on first report in the list

    await page.getByLabel('Download').click();
    
    const modalLocator = page.getByText('This report was successfully downloaded.');
		await expect(modalLocator).toBeVisible({ timeout: 7000 });
    
  });

  test('ANALYTICS ACS - Create and Save Insight '+scenario.testScenario+' impact - '+scenario.channel+'  -- @acs_sanity @an_sanity', async ({ page }) => {
      
    test.skip()//feature under updating
    test.slow()
    const {login, acs, anReport} = CreatePageInstances(page)  
    const loginModel: LoginModel = LoginModel.of(scenario.username,scenario.password)
    const acsModel: AcsModel = AcsModel.of(scenario.orgLocator)
    const anReportModel: AnReportModel = AnReportModel.of(scenario.testScenario,"")
    
    await login.acsLogin(page,loginModel);
  
    await acs.selectOrganization(page,acsModel);
  
    await anReport.goToReport(page);
  
    await anReport.createInsight(page,anReportModel);
    
  });

  test('ANALYTICS ACS - Duplicate Report '+scenario.testScenario+' impact - '+scenario.channel+'  -- @acs_sanity @an_sanity', async ({ page }) => {
    test.skip(env != "stage"); //skip due to test data mass
    test.slow()

    const {login , anCampaign, acs, anReport} = CreatePageInstances(page)  
    const loginModel: LoginModel = LoginModel.of(scenario.username,scenario.password)
    const acsModel: AcsModel = AcsModel.of(scenario.orgLocator)
    const anReportModel: AnReportModel = AnReportModel.of(scenario.testScenario,"")
    
    await login.acsLogin(page,loginModel);
  
    await acs.selectOrganization(page,acsModel);
  
    await anCampaign.gotToAnalyticsPage(page);
  
    await anReport.goToReport(page);

    await anReport.duplicateReport(page,anReportModel)
    
  });

  test('ANALYTICS ACS - Rename Report '+scenario.testScenario+' impact - '+scenario.channel+'  -- @acs_sanity @an_sanity', async ({ page }) => {
    test.skip(env == "prod"); //skip due to test data mass
    test.slow()
    const {login, acs, anReport} = CreatePageInstances(page)  
    const loginModel: LoginModel = LoginModel.of(scenario.username,scenario.password)
    const acsModel: AcsModel = AcsModel.of(scenario.orgLocator)
    const anReportModel: AnReportModel = AnReportModel.of(scenario.testScenario,"")
    
    await login.acsLogin(page,loginModel);
  
    await acs.selectOrganization(page,acsModel);
  
    await anReport.goToReport(page);
    
    await anReport.renameByReports(page,anReportModel);
    
  });
})
