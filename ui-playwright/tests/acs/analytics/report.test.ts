import { test } from '@playwright/test';
// @ts-ignore
import * as anFeeder from '../../automation-framework/feeder/analytics.json'
// @ts-ignore
import * as wsFeeder from '../../automation-framework/feeder/workspace.json'
import { CreatePageInstances } from '../../automation-framework/utils/helper'
import { AcsModel, LoginModel } from '../../automation-framework/model/'
import { AnCampaignModel } from '../../automation-framework/model/analytics/anCampaign.model'
import { AnReportModel } from '../../automation-framework/model/analytics/anReport.model'

const env = process.env.TESTENV || "";
const email = process.env.EMAIL || '';
const password= process.env.PASSWORD || '';
const organization = wsFeeder[env].vidMob

type getAn = {
    orgLocator: string;
    testScenario: string;
    channel: string;
    metric: string;
  };

  const an: getAn[] = [];

  an.push({ orgLocator:organization, testScenario: "Element impact", channel: "Facebook", metric: anFeeder.metricLocator.twentyfive });
   /* an.push({ orgLocator:organization, testScenario: "Element impact", channel: "Facebook Pages" });
    an.push({ orgLocator:organization, testScenario: "Element impact impact", channel: "Instagram" });
    an.push({ orgLocator:organization, testScenario: "Element impact", channel: "Instagram Pages" });
    an.push({ orgLocator:organization, testScenario: "Element impact", channel: "Snapchat" });
    an.push({ orgLocator:organization, testScenario: "Element impact", channel: "Pinterest" });
    an.push({ orgLocator:organization, testScenario: "Element impact", channel: "Twitter" });
    an.push({ orgLocator:organization, testScenario: "Element impact", channel: "Google Ads" });
    an.push({ orgLocator:organization, testScenario: "Element impact", channel: "DV360" });*/
    an.push({ orgLocator:organization, testScenario: "Media impact", channel: "Facebook", metric: anFeeder.metricLocator.twentyfive });
    /*an.push({ orgLocator:organization, testScenario: "Media impact", channel: "Facebook Pages" });
    an.push({ orgLocator:organization, testScenario: "Media impact", channel: "Instagram" });
    an.push({ orgLocator:organization, testScenario: "Media impact", channel: "Instagram Pages" });
    an.push({ orgLocator:organization, testScenario: "Media impact", channel: "Snapchat" });
    an.push({ orgLocator:organization, testScenario: "Media impact", channel: "Pinterest" });
    an.push({ orgLocator:organization, testScenario: "Media impact", channel: "Twitter" });
    an.push({ orgLocator:organization, testScenario: "Media impact", channel: "Google Ads" });
    an.push({ orgLocator:organization, testScenario: "Media impact", channel: "DV360" });*/


an.forEach((scenario) => {
  test('ANALYTICS ACS - Create '+scenario.testScenario+' impact - '+scenario.channel+'  -- @acs_functional', async ({ page }) => {
    test.slow()
    test.setTimeout(180000)
    const {common, login , anCampaign, acs, anReport} = CreatePageInstances(page)
    const loginModel: LoginModel = LoginModel.of(email,password)
    const anModel: AnCampaignModel = AnCampaignModel.of("Select all",anFeeder.campaigns.dateRange.previousYear, "Select All",'Facebook', scenario.metric ) //account, dateRange, workspace, channel, metric
    const acsModel: AcsModel = AcsModel.of(scenario.orgLocator)
    const anReportModel: AnReportModel = AnReportModel.of(scenario.testScenario,"")

    await login.acsLogin(page,loginModel);

    await acs.selectOrganization(page,acsModel);

    await anCampaign.gotToAnalyticsPage(page);

    await anCampaign.selectDateRange(page,anModel);

    await anCampaign.selectWorkspaceFilter(page,anModel);

    await anCampaign.selectChannelFilter(page,anModel);

    await anCampaign.selectAccountFilter(page,anModel);

    await anCampaign.selectMetric(page,anModel);

    await common.delay(15000);

    await anReport.goToReport(page);

    await common.delay(3000);

    await anReport.createAnReport(page,anReportModel);

});
})
