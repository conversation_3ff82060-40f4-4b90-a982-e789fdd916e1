/* eslint-disable @typescript-eslint/no-unused-vars */
import { test } from '@playwright/test';
import * as wsFeeder from '../../automation-framework/feeder/workspace.json'
import { CreatePageInstances } from '../../automation-framework/utils/helper'
import { AcsModel, LoginModel } from '../../automation-framework/model/'
import { StudHomeModel } from '../../automation-framework/model/studio/home.model'

const env = process.env.TESTENV || "";
const email = process.env.EMAIL || '';
const password= process.env.PASSWORD || '';
const organization = wsFeeder[env].vidMob
const projectName = wsFeeder[env].studioProjectName;
const projectId = wsFeeder[env].studioProjectId;

type getStud = {
    orgLocator: string;
    username:string;
    password: string;
    projectName: string;
    projectId: string;
  };
  
  const stud: getStud[] = [];
  
 stud.push({ orgLocator:organization, projectName: projectName, username: email, password: password,projectId: projectId  });
  
  stud.forEach((scenario) => {
   test('STUDIO ACS - verify all feedback elements in output drafts  -- @acs_functional @stud_functional', async ({ page }) => {
      test.slow()
      const {common, login , acs, studHome} = CreatePageInstances(page)  
      const loginModel: LoginModel = LoginModel.of(scenario.username,scenario.password)
      const acsModel: AcsModel = AcsModel.of(scenario.orgLocator)
      const studHomeModel: StudHomeModel = StudHomeModel.of(scenario.projectName, scenario.projectId )
   
      await login.acsLogin(page,loginModel);

      await acs.selectOrganization(page,acsModel);

      await studHome.gotToStudioPage(page);


   });    
})