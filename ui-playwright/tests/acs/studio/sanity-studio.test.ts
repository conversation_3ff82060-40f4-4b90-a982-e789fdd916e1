/* eslint-disable @typescript-eslint/no-unused-vars */
import { test, expect } from '@playwright/test';
import * as wsFeeder from '../../automation-framework/feeder/workspace.json'
import { CreatePageInstances } from '../../automation-framework/utils/helper'
import { AcsModel, LoginModel } from '../../automation-framework/model'
import { StudHomeModel } from '../../automation-framework/model/studio/home.model'

const env = process.env.TESTENV || "";
const email = process.env.EMAIL || '';
const password= process.env.PASSWORD || '';
const organization = wsFeeder[env].vidMob;
const projectName = wsFeeder[env].studioProjectName;
const projectId = wsFeeder[env].studioProjectId;

type getStud = {
    orgLocator: string;
    username:string;
    password: string;
    projectName: string;
    projectId: string;
  };
  
const stud: getStud[] = [];
  
stud.push({ orgLocator:organization, projectName: projectName, username: email, password: password, projectId: projectId });
  
stud.forEach((scenario) => {
   test('STUDIO ACS - Verify elements in home of projects  -- @acs_sanity @stud_sanity', async ({ page }) => {
      test.slow()
      test.setTimeout(200000)

      const {common, login , acs, studHome} = CreatePageInstances(page)  
      const loginModel: LoginModel = LoginModel.of(scenario.username,scenario.password)
      const acsModel: AcsModel = AcsModel.of(scenario.orgLocator)
      const studHomeModel: StudHomeModel = StudHomeModel.of(scenario.projectName,scenario.projectId)
   
      await login.acsLogin(page,loginModel);

      await acs.selectOrganization(page,acsModel);

      await studHome.gotToStudioPage(page);

      await common.delay(5000);
      await expect(page.getByText('creative studio')).toBeVisible({ timeout:45000 });
      
      await expect(page.locator('#active-project-page')).toContainText('New Project');
      await expect(page.getByRole('list')).toContainText('Active Projects');
      await expect(page.getByRole('list')).toContainText('Completed Projects');
      await expect(page.locator('h5')).toContainText('Active Projects');
      await expect(page.locator('#active-project-page')).toContainText('All Projects');
      
   });    

   test('STUDIO ACS - Verify elements in Brief page  -- @acs_sanity @stud_sanity', async ({ page }) => {
      test.slow()
      test.setTimeout(200000)

      const {common, login , acs, studHome} = CreatePageInstances(page)  
      const loginModel: LoginModel = LoginModel.of(scenario.username,scenario.password)
      const acsModel: AcsModel = AcsModel.of(scenario.orgLocator)
      const studHomeModel: StudHomeModel = StudHomeModel.of(scenario.projectName, scenario.projectId)
   
      await login.acsLogin(page,loginModel);

      await acs.selectOrganization(page,acsModel);

      await studHome.gotToStudioPage(page);

      await studHome.gotToProject(page, studHomeModel);
      await common.delay(3000);

      await expect(page.getByRole('button', { name: 'Share' })).toBeVisible();
      await expect(page.getByRole('link', { name: 'Edit project' })).toBeVisible();
      await expect(page.getByRole('heading', { name: 'Step 1: Complete your brief' })).toBeVisible({ timeout:45000 });

   }); 

   test('STUDIO ACS - Verify Add Assets is available  -- @acs_sanity @stud_sanity', async ({ page }) => {
      test.slow()
      test.setTimeout(200000)

      const {common, login , acs, studHome, studAssets} = CreatePageInstances(page)  
      const loginModel: LoginModel = LoginModel.of(scenario.username,scenario.password)
      const acsModel: AcsModel = AcsModel.of(scenario.orgLocator)
      const studHomeModel: StudHomeModel = StudHomeModel.of(scenario.projectName,scenario.projectId)
   
      await login.acsLogin(page,loginModel);

      await acs.selectOrganization(page,acsModel);

      await studHome.gotToStudioPage(page);

      await studHome.gotToProject(page, studHomeModel);
      await common.delay(3000);

      await studAssets.gotToAssets(page);
      await common.delay(3000);

      await expect(page.getByRole('button', { name: 'Add Assets' })).toBeVisible();
      
   }); 

   test('STUDIO ACS - Verify Outputs page  -- @acs_sanity @stud_sanity', async ({ page }) => {
      test.slow()
      test.setTimeout(200000)

      const {common, login , acs, studHome, studOutputs} = CreatePageInstances(page)  
      const loginModel: LoginModel = LoginModel.of(scenario.username,scenario.password)
      const acsModel: AcsModel = AcsModel.of(scenario.orgLocator)
      const studHomeModel: StudHomeModel = StudHomeModel.of(scenario.projectName,scenario.projectId)
   
      await login.acsLogin(page,loginModel);

      await acs.selectOrganization(page,acsModel);

      await studHome.gotToStudioPage(page);

      await studHome.gotToProject(page, studHomeModel);
      await common.delay(3000);

      await studOutputs.gotToOutputs(page);
      await common.delay(3000);

      const currentUrl = await page.url();
      expect(currentUrl).toContain('outputs');
      
   }); 

   test('STUDIO ACS - Verify Project Team page  -- @acs_sanity @stud_sanity', async ({ page }) => {
      test.slow()
      test.setTimeout(200000)

      const {common, login , acs, studHome, studProjectTeam} = CreatePageInstances(page)  
      const loginModel: LoginModel = LoginModel.of(scenario.username,scenario.password)
      const acsModel: AcsModel = AcsModel.of(scenario.orgLocator)
      const studHomeModel: StudHomeModel = StudHomeModel.of(scenario.projectName,scenario.projectId)
   
      await login.acsLogin(page,loginModel);

      await acs.selectOrganization(page,acsModel);

      await studHome.gotToStudioPage(page);

      await studHome.gotToProject(page, studHomeModel);
      await common.delay(3000);

      await studProjectTeam.gotToProjectTeam(page);
      await common.delay(3000);

      await expect(page.getByRole('button', { name: 'Invite Collaborators' })).toBeVisible({ timeout:35000 });

   }); 

   test('STUDIO ACS - Verify Notes page  -- @acs_sanity @stud_sanity', async ({ page }) => {
      test.slow()
      test.setTimeout(200000)

      const {common, login , acs, studHome, studNotes} = CreatePageInstances(page)  
      const loginModel: LoginModel = LoginModel.of(scenario.username,scenario.password)
      const acsModel: AcsModel = AcsModel.of(scenario.orgLocator)
      const studHomeModel: StudHomeModel = StudHomeModel.of(scenario.projectName,scenario.projectId)
   
      await login.acsLogin(page,loginModel);

      await acs.selectOrganization(page,acsModel);

      await studHome.gotToStudioPage(page);

      await studHome.gotToProject(page, studHomeModel);
      await common.delay(3000);

      await studNotes.gotToNotes(page);
      await common.delay(3000);

      await expect(page.locator('#notes-list')).toContainText('Nothing noteworthy, yet.');

   }); 
})
