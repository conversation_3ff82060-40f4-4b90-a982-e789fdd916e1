import { test, expect } from '@playwright/test';
import * as wsFeeder from '../automation-framework/feeder/workspace.json'
import { CreatePageInstances } from '../automation-framework/utils/helper'
import { AcsModel, LoginModel } from '../automation-framework/model'

const env = process.env.TESTENV || "";
const email = process.env.EMAIL || '';
const password= process.env.PASSWORD || '';
const organization = wsFeeder[env].vidMob;
const mfa_email = process.env.MFA_USER || '';
const mfa_password= process.env.MFA_PASSWORD || '';
const projectName = wsFeeder[env].studioProjectName;
const projectId = wsFeeder[env].studioProjectId;

type getScor = {
    orgLocator: string;
    testScenario: string;
    username:string;
    password: string;
  };
  const scor: getScor[] = [];
  scor.push({ orgLocator:organization, testScenario: "Adoption", username: email, password: password });
  scor.forEach((scenario) => {
    test('SCORING ACS - Verify if elements in Reports page is displayed  -- @acs_smoke @scor_smoke', async ({ page }) => {
        test.slow()
        const {common, login , acs, scoReport} = CreatePageInstances(page)
        const loginModel: LoginModel = LoginModel.of(scenario.username,scenario.password)
        const acsModel: AcsModel = AcsModel.of(scenario.orgLocator)

        await login.acsLogin(page,loginModel);

        await acs.selectOrganization(page,acsModel);

        await scoReport.goToReports(page);

        await common.delay(3000);

        await handleDialog(page);

        await expect(page.getByRole('button', { name: 'Create report' })).toBeVisible({ timeout:45000 });
        await expect(page.locator('h5')).toContainText('Reports');
    });

    test('SCORING ACS - Verify if elements in Pre-Flight Checks page is displayed  -- @acs_smoke @scor_smoke', async ({ page }) => {
        test.slow()
        const {common, login , acs, scorecards} = CreatePageInstances(page)
        const loginModel: LoginModel = LoginModel.of(scenario.username,scenario.password)
        const acsModel: AcsModel = AcsModel.of(scenario.orgLocator)

        await login.acsLogin(page,loginModel);

        await acs.selectOrganization(page,acsModel);

        await scorecards.goToPreFlightChecks(page);

        await common.delay(3000);

        await handleDialog(page);

        await expect(page.getByRole('button', { name: 'New Pre-flight Check' })).toBeVisible({ timeout:45000 });
        await expect(page.locator('h5')).toContainText('Pre-flight');
        await expect(page.locator('#compliance-content-area')).toContainText('Pre-flight Checks');
    });

    test('SCORING ACS - Verify if elements in Criteria page is displayed  -- @acs_smoke @scor_smoke', async ({ page }) => {
        test.slow()
        const {common, login , acs, scorecards} = CreatePageInstances(page)
        const loginModel: LoginModel = LoginModel.of(scenario.username,scenario.password)
        const acsModel: AcsModel = AcsModel.of(scenario.orgLocator)

        await login.acsLogin(page,loginModel);

        await acs.selectOrganization(page,acsModel);

        await scorecards.goToPreFlightChecks(page);

        await handleDialog(page);

        await common.delay(3000);
        await expect(page.locator('h5')).toBeVisible({ timeout:45000 });

    });

    test('SCORING ACS - Verify create report is available for all types '+scenario.testScenario+' -- @acs_smoke @scor_smoke', async ({ page }) => {
            test.slow();
            const {common, login , acs, scoReport} = CreatePageInstances(page);
            const loginModel: LoginModel = LoginModel.of(scenario.username,scenario.password);
            const acsModel: AcsModel = AcsModel.of(scenario.orgLocator);

            await login.acsLogin(page,loginModel);

            await acs.selectOrganization(page,acsModel);

            await scoReport.goToReports(page);

            await common.delay(3000);

            await handleDialog(page);

            await page.getByRole('button', { name: 'Create report' }).click();
            await common.delay(3000);

            await page.waitForLoadState('load');

            await expect(page.locator('.MuiPaper-root').getByText('Create a new report')).toBeVisible({ timeout: 45000 }); //verify choose report type
            await expect(page.locator('#inflight')).toContainText('In-flight Check');
            await expect(page.locator('#adherence')).toContainText('Adherence');
            await expect(page.locator('#impression-adherence')).toContainText('Impressions adherence');
            await expect(page.locator('#adoption')).toContainText('Adoption');
            //await expect(page.locator('#diversity')).toContainText('Diversity');

    });
  })

  type getAn = {
    orgLocator: string;
    testScenario: string;
    channel: string;
    metric: string;
    username:string;
    password: string;
  };

  const an: getAn[] = [];
  an.push({ orgLocator:organization, testScenario: "Element impact", channel: "Facebook", metric: '25%', username: email, password: password });
  an.push({ orgLocator:organization, testScenario: "Media impact", channel: "Facebook", metric: '25%', username: email, password: password });

an.forEach((scenario) => {

    test('ANALYTICS ACS - Verify create report is available '+scenario.testScenario+' impact - '+scenario.channel+'  -- @acs_smoke @an_smoke', async ({ page }) => {
      test.slow()
      const {common, login, acs, anReport} = CreatePageInstances(page)
      const loginModel: LoginModel = LoginModel.of(scenario.username,scenario.password)
      const acsModel: AcsModel = AcsModel.of(scenario.orgLocator)

      await login.acsLogin(page,loginModel);

      await acs.selectOrganization(page,acsModel);

      await anReport.goToReport(page);

      await common.delay(3000);

      await expect(page.locator('button').filter({ hasText: 'Create report' }).first()).toBeVisible({ timeout:45000 });//verify create is visible

      await page.getByRole('button', { name: 'Create report' }).first().click();
      await common.delay(3000);

      await expect(page.getByLabel('Create a new report').getByText('Create a new report')).toBeVisible({ timeout:45000 });
  });
})

const anSmoke: getAn[] = [];
anSmoke.push({ orgLocator:organization, testScenario: "Element impact", channel: "Facebook", metric: '25%', username: email, password: password });
anSmoke.forEach((scenario) => {
    test('ANALYTICS ACS - Verify if elements in Campaign page is displayed  -- @acs_smoke @an_smoke', async ({ page }) => {
      test.skip(env == 'dev') //https://vidmob.atlassian.net/browse/VID-2733
      test.slow()
      const {login , anCampaign, acs} = CreatePageInstances(page)
      const loginModel: LoginModel = LoginModel.of(scenario.username,scenario.password)
      const acsModel: AcsModel = AcsModel.of(scenario.orgLocator)

      await login.acsLogin(page,loginModel);

      await acs.selectOrganization(page,acsModel);

      await anCampaign.gotToAnalyticsPage(page);

      await expect(page.locator('h5')).toBeVisible({ timeout:45000 });
      await expect(page.locator('h5')).toContainText('Creative Manager');

  });
})

test('mfa login - verify 2fa modal -- @acs_smoke', async ({ page }) => {
    test.slow()
    const { login } = CreatePageInstances(page)

    const loginModel: LoginModel = LoginModel.of(mfa_email,mfa_password)
    await login.acsLogin(page,loginModel);

    const appText = await page.isVisible('#modal-two-factor-auth')
	expect(appText).toBeTruthy()
});


type getPlat = {
    orgLocator: string;
    project: string;
    username:string;
    password: string;
    testScenario: string;
    url: string;
  };

  const plat: getPlat[] = [];

plat.push({ orgLocator:organization, project: "UGC Test", username: email, password: password, testScenario:'Workspaces', url:'workspaces' });
plat.push({ orgLocator:organization, project: "UGC Test", username: email, password: password, testScenario:'People', url: 'people' });
plat.push({ orgLocator:organization, project: "UGC Test", username: email, password: password, testScenario:'App store', url:'integrations/app-store' });
plat.push({ orgLocator:organization, project: "UGC Test", username: email, password: password, testScenario:'Creative groups', url: 'user-profile/creative-groups' });
plat.forEach((scenario) => {
   test('PLATFORM ACS - verify each page is properly displayed on navigation:  '+scenario.testScenario+'  -- @plat_smoke @acs_smoke', async ({ page }) => {

      test.slow()
      const {common, login , acs, getURL} = CreatePageInstances(page)
      const loginModel: LoginModel = LoginModel.of(scenario.username,scenario.password)
      const acsModel: AcsModel = AcsModel.of(scenario.orgLocator)

      const env = process.env.TESTENV;
      const baseURL = getURL.returnURL('acs-'+env)

      await login.acsLogin(page,loginModel);

      await acs.selectOrganization(page,acsModel);

      await page.goto(baseURL+scenario.url);

      await common.delay(3500)

      await page.waitForSelector('h5:has-text("'+scenario.testScenario+'")', { timeout: 40000 });

   });
})

type getStud = {
    orgLocator: string;
    username:string;
    password: string;
    projectName: string;
    projectId: string;
  };

const stud: getStud[] = [];

stud.push({ orgLocator:organization, projectName: projectName, username: email, password: password, projectId: projectId });

stud.forEach((scenario) => {
   test('STUDIO ACS - Verify elements in home of projects  -- @acs_smoke @stud_smoke', async ({ page }) => {
      test.slow()
      const {common, login , acs, studHome} = CreatePageInstances(page)
      const loginModel: LoginModel = LoginModel.of(scenario.username,scenario.password)
      const acsModel: AcsModel = AcsModel.of(scenario.orgLocator)

      await login.acsLogin(page,loginModel);

      await acs.selectOrganization(page,acsModel);

      await studHome.gotToStudioPage(page);

      await common.delay(5000);

      await expect(page.getByText('creative studio')).toBeVisible({ timeout:45000 });

      await expect(page.locator('#active-project-page')).toContainText('New Project');
      await expect(page.getByRole('list')).toContainText('Active Projects');
      await expect(page.locator('h5')).toContainText('Active Projects');
      await expect(page.locator('#active-project-page')).toContainText('All Projects');

   });

})

async function handleDialog(page) {
    const proceedButton = page.getByRole('button', { name: 'Proceed to Creative Scoring Reports' });
    const closeButton = page.getByRole('button', { name: 'Close' });

    if (await proceedButton.isVisible({ timeout: 1000 }).catch(() => false)) {
        await proceedButton.click();
        await page.waitForLoadState('load');
    } else if (await closeButton.isVisible({ timeout: 1000 }).catch(() => false)) {
        await closeButton.click();
        await page.waitForLoadState('load');
    }
}

