/* eslint-disable @typescript-eslint/no-unused-vars */
import { test } from '@playwright/test';
import * as wsFeeder from '../../automation-framework/feeder/workspace.json'
import { CreatePageInstances } from '../../automation-framework/utils/helper'
import { AcsModel, LoginModel } from '../../automation-framework/model/'
import { ScoCriteriaModel } from '../../automation-framework/model/scoring/scoCriteria.model'

const env = process.env.TESTENV || "";
const email = process.env.EMAIL || '';
const password= process.env.PASSWORD || '';
const organization = wsFeeder[env].vidMob


type getScor = {
    orgLocator: string;
    testScenario: string;
    username:string;
    password: string;
  };

  
  const scor: getScor[] = [];
  scor.push({ orgLocator:organization, testScenario: "Adoption", username: email, password: password });

  scor.forEach((scenario) => {
    test('SCORING ACS - Add criteria -- @scor_functional @acs_functional', async ({ page }) => {
    test.slow()
    test.setTimeout(175000)
    const {common, login , acs, scoCriteria} = CreatePageInstances(page)  
    const loginModel: LoginModel = LoginModel.of(scenario.username,scenario.password)
    const acsModel: AcsModel = AcsModel.of(scenario.orgLocator)
    const scoCriteriaModel: ScoCriteriaModel = ScoCriteriaModel.of('Facebook','Custom Text Present','Optional','Other','Images')
                                                                    
    await login.acsLogin(page,loginModel);

    await acs.selectOrganization(page,acsModel);
  
    await scoCriteria.goToCriteria(page);

    await scoCriteria.addCriteria(page,scoCriteriaModel);
    
    });
  })