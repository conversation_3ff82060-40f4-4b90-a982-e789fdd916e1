import { test, expect } from '@playwright/test';
import * as wsFeeder from '../../automation-framework/feeder/workspace.json'
import { CreatePageInstances } from '../../automation-framework/utils/helper'
import { AcsModel, LoginModel } from '../../automation-framework/model'
import { ScoReportModel } from '../../automation-framework/model/scoring/scoReport.model'

const env = process.env.TESTENV || "";
const email = process.env.EMAIL || '';
const password= process.env.PASSWORD || '';
const organization = wsFeeder[env].vidMob

type getSco = {
    orgLocator: string;
    testScenario: string;
    username:string;
    password: string;
  };

  const sco: getSco[] = [];
  sco.push({ orgLocator:organization, testScenario: "Adherence",username: email, password: password });
  sco.push({ orgLocator:organization, testScenario: "Impressions adherence", username: email, password: password });
  sco.push({ orgLocator:organization, testScenario: "Adoption", username: email, password: password });
  sco.push({ orgLocator:organization, testScenario: "Diversity", username: email, password: password });

  sco.forEach((scenario) => {

  test('SCORING ACS - Verify report creation successfully '+scenario.testScenario+' -- @scor_functional @acs_functional', async ({ page }) => {
    test.slow()
    test.setTimeout(200000)

    const {common, login , acs, scoReport} = CreatePageInstances(page)
    const loginModel: LoginModel = LoginModel.of(scenario.username,scenario.password)
    const acsModel: AcsModel = AcsModel.of(scenario.orgLocator)
    const scoReportModel: ScoReportModel = ScoReportModel.of(scenario.testScenario,'')

    await login.acsLogin(page,loginModel);

    await acs.selectOrganization(page,acsModel);

    await scoReport.goToReports(page);

    await common.delay(3000);

    await scoReport.createScoReport(page,scoReportModel);

  });
})

const scoReg: getSco[] = [];

scoReg.push({ orgLocator:organization, testScenario: "Adherence",username: email, password: password });

  scoReg.forEach((scenario) => {

  test('SCORING ACS - Verify create report is available for all types -- @scor_functional @acs_functional', async ({ page }) => {
    test.slow()
    test.setTimeout(150000)

    const {common, login , acs, scoReport} = CreatePageInstances(page)
    const loginModel: LoginModel = LoginModel.of(scenario.username,scenario.password)
    const acsModel: AcsModel = AcsModel.of(scenario.orgLocator)

    await login.acsLogin(page,loginModel);

    await acs.selectOrganization(page,acsModel);

    await scoReport.goToReports(page);

    await common.delay(3000);

    await page.getByRole('button', { name: 'Create report' }).click();
    await common.delay(5000)

    await page.waitForLoadState('load');

    await expect(page.getByText('Create a new report')).toBeVisible({ timeout:45000 }); //verify choose report type
    await expect(page.locator('#adherence')).toContainText('Adherence');
    await expect(page.locator('#impression-adherence')).toContainText('Impressions adherence');
    await expect(page.locator('#adoption')).toContainText('Adoption');
    await expect(page.locator('#diversity')).toContainText('Diversity');

    });
  })
