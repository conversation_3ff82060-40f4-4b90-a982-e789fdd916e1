/* eslint-disable @typescript-eslint/no-unused-vars */
import { test, expect } from '@playwright/test';
import * as wsFeeder from '../../automation-framework/feeder/workspace.json'
import { CreatePageInstances } from '../../automation-framework/utils/helper'
import { AcsModel, LoginModel } from '../../automation-framework/model'
import { ScoReportModel } from '../../automation-framework/model/scoring/scoReport.model'
import { ScoCriteriaModel } from '../../automation-framework/model/scoring/scoCriteria.model'

const env = process.env.TESTENV || "";
const email = process.env.EMAIL || '';
const password= process.env.PASSWORD || '';
const organization = wsFeeder[env].vidMob

type getScor = {
    orgLocator: string;
    testScenario: string;
    username:string;
    password: string;
  };

  const scor: getScor[] = [];

  scor.push({ orgLocator:organization, testScenario: "Adoption", username: email, password: password });

  scor.forEach((scenario) => {
  test('SCORING ACS - Verify if elements in Reports page is displayed  -- @acs_sanity @scor_sanity', async ({ page }) => {
    test.slow()
    test.setTimeout(150000)

    const {common, login , acs, scoReport, scorecards} = CreatePageInstances(page)
    const loginModel: LoginModel = LoginModel.of(scenario.username,scenario.password)
    const acsModel: AcsModel = AcsModel.of(scenario.orgLocator)
    const scoReportModel: ScoReportModel = ScoReportModel.of(scenario.testScenario,'')

    await login.acsLogin(page,loginModel);

    await acs.selectOrganization(page,acsModel);

    await scoReport.goToReports(page);

    await common.delay(3000);

    await expect(page.getByRole('button', { name: 'Create report' })).toBeVisible({ timeout:45000 });
    await expect(page.locator('h5')).toContainText('Reports');
  });

  test('SCORING ACS - Verify if elements in Pre-flight Checks page is displayed  -- @acs_sanity @scor_sanity @scor_regression @acs_sanity', async ({ page }) => {
    test.slow()
    test.setTimeout(150000)

    const {common, login , acs, scorecards} = CreatePageInstances(page)
    const loginModel: LoginModel = LoginModel.of(scenario.username,scenario.password)
    const acsModel: AcsModel = AcsModel.of(scenario.orgLocator)

    await login.acsLogin(page,loginModel);

    await acs.selectOrganization(page,acsModel);

    await scorecards.goToPreFlightChecks(page);

    await common.delay(3000);
    await expect(page.getByRole('button', { name: 'New Pre-flight Check' })).toBeVisible({ timeout:45000 });
    await expect(page.locator('h5')).toContainText('Pre-flight');
    await expect(page.locator('#compliance-content-area')).toContainText('Pre-flight Checks');
  });

  test('SCORING ACS - Verify if elements in Criteria page is displayed  -- @acs_sanity @scor_sanity @scor_regression @acs_sanity', async ({ page }) => {
    test.slow()
    test.setTimeout(150000)

    const {common, login , acs, scoReport, scorecards} = CreatePageInstances(page)
    const loginModel: LoginModel = LoginModel.of(scenario.username,scenario.password)
    const acsModel: AcsModel = AcsModel.of(scenario.orgLocator)
    const scoReportModel: ScoReportModel = ScoReportModel.of(scenario.testScenario,'')

    await login.acsLogin(page,loginModel);

    await acs.selectOrganization(page,acsModel);

    await scorecards.goToPreFlightChecks(page);

    await common.delay(3000);
    await expect(page.locator('h5')).toBeVisible({ timeout:45000 });

  });

    test('SCORING ACS - Verify create report is available for all types '+scenario.testScenario+' -- @acs_sanity @scor_sanity @scor_regression @acs_sanity', async ({ page }) => {
        test.slow()
        test.setTimeout(150000)

        const {common, login , acs, scoReport} = CreatePageInstances(page)
        const loginModel: LoginModel = LoginModel.of(scenario.username,scenario.password)
        const acsModel: AcsModel = AcsModel.of(scenario.orgLocator)

        await login.acsLogin(page,loginModel);

        await acs.selectOrganization(page,acsModel);

        await scoReport.goToReports(page);

        await common.delay(3000);

        await page.getByRole('button', { name: 'Create report' }).click();
    	  await common.delay(3000)

		    await page.waitForLoadState('load');

        await expect(page.getByText('Create a new report')).toBeVisible({ timeout:45000 }); //verify choose report type
        await expect(page.locator('#adherence')).toContainText('Adherence');
        await expect(page.locator('#impression-adherence')).toContainText('Impressions adherence');
        await expect(page.locator('#adoption')).toContainText('Adoption');
        //await expect(page.locator('#diversity')).toContainText('Diversity');

  });

  test('SCORING ACS - Verify report creation successfully '+scenario.testScenario+' -- @acs_sanity @scor_sanity @scor_regression @acs_sanity', async ({ page }) => {
    test.slow()
    //till get stability
    test.setTimeout(150000)
    const {common, login , acs, scoReport} = CreatePageInstances(page)
    const loginModel: LoginModel = LoginModel.of(scenario.username,scenario.password)
    const acsModel: AcsModel = AcsModel.of(scenario.orgLocator)
    const scoReportModel: ScoReportModel = ScoReportModel.of(scenario.testScenario,'')

    await login.acsLogin(page,loginModel);

    await acs.selectOrganization(page,acsModel);

    await scoReport.goToReports(page);

    await common.delay(3000);

    await scoReport.createScoReport(page,scoReportModel);

  });

  test('SCORING ACS - Duplicate Report -- @acs_sanity @scor_sanity @scor_regression @acs_sanity', async ({ page }) => {
    test.slow()
    test.skip() //https://vidmob.atlassian.net/browse/VID-1874
    test.setTimeout(150000)
    const {common, login , acs, scoReport} = CreatePageInstances(page)
    const loginModel: LoginModel = LoginModel.of(scenario.username,scenario.password)
    const acsModel: AcsModel = AcsModel.of(scenario.orgLocator)

    await login.acsLogin(page,loginModel);

    await acs.selectOrganization(page,acsModel);

    await scoReport.goToReports(page);

    await common.delay(3000);

    await scoReport.duplicateReport(page);

  });

  test('SCORING ACS - Rename Report -- @acs_sanity @scor_sanity @scor_regression @acs_sanity', async ({ page }) => {
    test.slow()
    //https://vidmob.atlassian.net/browse/VID-1875
    test.setTimeout(150000)
    const {common, login , acs, scoReport} = CreatePageInstances(page)
    const loginModel: LoginModel = LoginModel.of(scenario.username,scenario.password)
    const acsModel: AcsModel = AcsModel.of(scenario.orgLocator)

    await login.acsLogin(page,loginModel);

    await acs.selectOrganization(page,acsModel);

    await scoReport.goToReports(page);

    await common.delay(3000);

    await scoReport.renameByReports(page);

  });

  test('SCORING ACS - Update -- @acs_sanity @scor_sanity @scor_regression', async ({ page }) => {
    test.skip ()//till get stability
    test.setTimeout(150000)
    const {common, login , acs, scoReport} = CreatePageInstances(page)
    const loginModel: LoginModel = LoginModel.of(scenario.username,scenario.password)
    const acsModel: AcsModel = AcsModel.of(scenario.orgLocator)

    await login.acsLogin(page,loginModel);

    await acs.selectOrganization(page,acsModel);

    await scoReport.goToReports(page);

    await scoReport.updateAnReport(page);

  });

  test('SCORING ACS - Verify Add criteria is available -- @acs_sanity @scor_sanity @scor_regression @acs_sanity', async ({ page }) => {
    //It's bussiness rule - needs to identify proper testing data (removed from sanity for now)
    test.slow()
    test.setTimeout(150000)
    const {common, login , acs, scoCriteria} = CreatePageInstances(page)
    const loginModel: LoginModel = LoginModel.of(scenario.username,scenario.password)
    const acsModel: AcsModel = AcsModel.of(scenario.orgLocator)
    const scoCriteriaModel: ScoCriteriaModel = ScoCriteriaModel.of('Facebook','Text (specific)','Optional','Other','Images')

    await login.acsLogin(page,loginModel);

    await acs.selectOrganization(page,acsModel);

    await scoCriteria.goToCriteria(page);

    await page.getByRole('button', { name: 'Add criteria' }).click();
    await common.delay(2000);

    await expect(page.getByRole('heading', { name: 'Add criteria', exact: true })).toBeVisible({ timeout:45000 });

  });

  test('SCORING ACS - Pre-flight Checks navigation -- @acs_sanity @scor_sanity @scor_regression @acs_sanity', async ({ page }) => {
    test.slow()
    test.setTimeout(150000)

    const {common, login , acs, scorecards} = CreatePageInstances(page)
    const loginModel: LoginModel = LoginModel.of(scenario.username,scenario.password)
    const acsModel: AcsModel = AcsModel.of(scenario.orgLocator)

    await login.acsLogin(page,loginModel);

    await acs.selectOrganization(page,acsModel);

    await scorecards.goToPreFlightChecks(page);

    await common.delay(3000);

    await page.getByRole('heading', { name: 'Saved Pre-flight' }).click();
    await common.delay(1000);
    await expect(page.locator('#compliance-content-area')).toContainText('Pre-flight');


    await page.getByRole('heading', { name: 'Saved In-flight' }).click();
    await common.delay(3000);
    await expect(page.locator('#compliance-content-area')).toContainText('In-flight');


    await page.getByRole('heading', { name: 'Ad account' }).click();
    await common.delay(3000);
    await expect(page.locator('#compliance-content-area')).toContainText('Your connected ad accounts');

  });
})
