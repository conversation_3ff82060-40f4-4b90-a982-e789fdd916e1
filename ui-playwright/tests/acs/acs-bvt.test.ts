import { test, expect } from '@playwright/test';
import * as wsFeeder from '../automation-framework/feeder/workspace.json'
import { CreatePageInstances } from '../automation-framework/utils/helper'
import { AcsModel, LoginModel } from '../automation-framework/model/'

const env = process.env.TESTENV || "";
const email = process.env.EMAIL || '';
const password= process.env.PASSWORD || '';
const organization = wsFeeder[env].vidMob

test('verify login page is loaded -- @acs_bvt', async ({ page }) => {
    test.slow()
    const { getURL, common } = CreatePageInstances(page)  
    const baseURL = getURL.returnURL('acs-'+env) || '';
	
    await page.goto(baseURL);
    await page.waitForLoadState('load');
    await common.delay(5000)
    await page.setDefaultNavigationTimeout(40000);

    await expect(page.locator('#app-root')).toContainText('Log in to get started');
    await expect(page.getByRole('link', { name: 'VidMob' })).toBeVisible();

});

test('verify role report login -- @acs_bvt', async ({ page }) => {
    test.slow()
    const {login , acs} = CreatePageInstances(page)  
    const loginModel: LoginModel = LoginModel.of(email,password)
    const acsModel: AcsModel = AcsModel.of(organization)
    
    await login.acsLogin(page,loginModel);

    await acs.selectOrganization(page,acsModel);
    await page.waitForLoadState('load');
    await page.setDefaultNavigationTimeout(40000);

    await expect(page.getByRole('main')).toContainText('Scoring');
    await expect(page.getByRole('main')).toContainText('Analytics');
    await expect(page.getByRole('heading', { name: 'Studio' })).toBeVisible();
    await expect(page.getByRole('heading', { name: 'Organization' })).toBeVisible();
    await expect(page.getByRole('heading', { name: 'Profile' })).toBeVisible();
    await expect(page.getByRole('heading', { name: 'Quick links' })).toBeVisible();
    await expect(page.getByRole('heading', { name: 'Discover VidMob' })).toBeVisible();
   
});

test('verify navigation Scoring -- @acs_bvt', async ({ page }) => {
    test.slow()
    const {login , acs} = CreatePageInstances(page)  
    const loginModel: LoginModel = LoginModel.of(email,password)
    const acsModel: AcsModel = AcsModel.of(organization)

    await login.acsLogin(page,loginModel);
    
    await acs.selectOrganization(page,acsModel);
    await page.waitForLoadState('load');
    await page.setDefaultNavigationTimeout(40000);
    await page.waitForLoadState('load');
    await page.setDefaultNavigationTimeout(40000);

    await page.getByRole('heading', { name: 'Scoring' }).click();
    await page.waitForLoadState('load');
    await page.setDefaultNavigationTimeout(40000);

    await expect(page.getByText('scoring')).toBeVisible();
    await expect(page.getByTestId('top-nav')).toContainText('scoring');
    await expect(page.locator('h5')).toBeVisible();
});

test('verify navigation Analytics -- @acs_bvt', async ({ page }) => {
    test.slow()
    const {login , acs} = CreatePageInstances(page)  
    const loginModel: LoginModel = LoginModel.of(email,password)
    const acsModel: AcsModel = AcsModel.of(organization)

    await login.acsLogin(page,loginModel);
    
    await acs.selectOrganization(page,acsModel);
    await page.waitForLoadState('load');
    await page.setDefaultNavigationTimeout(40000);
    await page.waitForLoadState('load');
    await page.setDefaultNavigationTimeout(40000);

    await page.getByRole('heading', { name: 'Analytics' }).click();
    await page.locator('body').press('Escape');
    await page.waitForLoadState('load');
    await page.setDefaultNavigationTimeout(40000);

    await expect(page.getByTestId('top-nav')).toContainText('analytics');
    //await expect(page.locator('h5')).toBeVisible();
});

test('verify navigation Studio -- @acs_bvt', async ({ page }) => {
    test.slow()
    const {login , acs} = CreatePageInstances(page)  
    const loginModel: LoginModel = LoginModel.of(email,password)
    const acsModel: AcsModel = AcsModel.of(organization)

    await login.acsLogin(page,loginModel);

    await acs.selectOrganization(page,acsModel);
    await page.waitForLoadState('load');
    await page.setDefaultNavigationTimeout(40000);

    await page.getByRole('heading', { name: 'Studio' }).click();
    await page.waitForLoadState('load');
    await page.setDefaultNavigationTimeout(40000);

    await expect(page.getByText('creative studio')).toBeVisible();
    await expect(page.getByTestId('top-nav')).toContainText('studio');
    await expect(page.locator('h5')).toBeVisible();
});