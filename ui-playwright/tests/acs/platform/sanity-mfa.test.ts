import { test, expect } from '@playwright/test';
import { CreatePageInstances } from '../../automation-framework/utils/helper'
import { LoginModel } from '../../automation-framework/model'

const email = process.env.MFA_USER || '';
const password= process.env.MFA_PASSWORD || '';

test('mfa login - verify 2fa modal -- @acs_sanity @acs_mfa', async ({ page }) => {
    test.slow()
    const {login } = CreatePageInstances(page)  
  
    const loginModel: LoginModel = LoginModel.of(email,password)
    await login.acsLogin(page,loginModel);

    const appText = await page.isVisible('#modal-two-factor-auth')
	expect(appText).toBeTruthy()
});