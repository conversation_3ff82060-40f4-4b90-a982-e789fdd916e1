/* eslint-disable @typescript-eslint/no-unused-vars */
import { test } from '@playwright/test';
import * as wsFeeder from '../../automation-framework/feeder/workspace.json'
import { CreatePageInstances } from '../../automation-framework/utils/helper'
import { AcsModel, LoginModel } from '../../automation-framework/model'

const env = process.env.TESTENV || "";
const email = process.env.EMAIL || '';
const password= process.env.PASSWORD || '';
const organization = wsFeeder[env].vidMob

type getStud = {
    orgLocator: string;
    project: string;
    username:string;
    password: string;
    testScenario: string;
    url: string;
  };
  
  const plat: getStud[] = [];
  
plat.push({ orgLocator:organization, project: "UGC Test", username: email, password: password, testScenario:'Workspaces', url:'workspaces' });
plat.push({ orgLocator:organization, project: "UGC Test", username: email, password: password, testScenario:'People', url: 'people' });
plat.push({ orgLocator:organization, project: "UGC Test", username: email, password: password, testScenario:'App store', url:'integrations/app-store' });
//plat.push({ orgLocator:organization, project: "UGC Test", username: email, password: password, testScenario:'Manage', url: 'integrations/manage' });
plat.push({ orgLocator:organization, project: "UGC Test", username: email, password: password, testScenario:'Ad account groups', url: 'user-profile/account-groups' });
plat.push({ orgLocator:organization, project: "UGC Test", username: email, password: password, testScenario:'Creative groups', url: 'user-profile/creative-groups' });
plat.push({ orgLocator:organization, project: "UGC Test", username: email, password: password, testScenario:'Element Sets', url: 'user-profile/tag-groups' });
plat.push({ orgLocator:organization, project: "UGC Test", username: email, password: password, testScenario:'brands', url: 'brands' });
//plat.push({ orgLocator:organization, project: "UGC Test", username: email, password: password, testScenario:'Asset locker', url: 'partner/asset-locker' });  
  plat.forEach((scenario) => {
   test('PLATFORM ACS - verify each page is properly displayed on navigation:  '+scenario.testScenario+'  -- @plat_sanity @acs_sanity', async ({ page }) => {
     
      test.slow()
      test.setTimeout(150000)

      const {common, login , acs, studHome, getURL} = CreatePageInstances(page)  
      const loginModel: LoginModel = LoginModel.of(scenario.username,scenario.password)
      const acsModel: AcsModel = AcsModel.of(scenario.orgLocator)

      const env = process.env.TESTENV; 
      const baseURL = getURL.returnURL('acs-'+env)

      await login.acsLogin(page,loginModel);

      await acs.selectOrganization(page,acsModel);

      await page.goto(baseURL+scenario.url);

      await common.delay(3500)

      await page.waitForSelector('h5:has-text("'+scenario.testScenario+'")', { timeout: 40000 });

   });    
})
