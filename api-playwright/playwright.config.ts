/* eslint-disable @typescript-eslint/no-unused-vars */
import { defineConfig } from "@playwright/test";
import * as dotenv from 'dotenv'

// Local development .env file
try {
  require('dotenv').config({ path: '../.env' })
} catch (error) {
}

// CI environment .env file
try {
  dotenv.config({
    path: `.env.${process.env.TESTENV}`,
    override: true,
  })
} catch (error) {
}

export default defineConfig({
  timeout: 90000, // Increase timeout to 90 seconds so local matches CI environment
  use: {
    extraHTTPHeaders: {
      // We set this header per GitHub guidelines.
      Accept: "application/vnd.github.v3+json",
      // Add authorization token to all requests.
      // Assuming personal access token available in the environment.
      Authorization: `token ${process.env.API_TOKEN}`,
    },
  },
  reporter: [
    ['list'],
    ['allure-playwright'],
    ['html', { open: 'never' }],
    ['json', { outputFile: 'test-results/playwright-results.json' }],
  ],
  ignoreSnapshots: true,
});
