import { test, expect } from "@playwright/test";
import { createPageInstances } from "../../../utils/helper";
import * as workspace from "../../../feeder/workspace.json";
import * as organizationData from "../../../feeder/org.json";

const env = process.env.TESTENV || "";

//Organization
test("API Studio sanity - Get Workspaces Organization @api_sanity @apitest @api_org", async ({
  request,
}) => {
  const { getURL, getLoginToken } = createPageInstances();
  const accessToken = await getLoginToken.getAccessToken(request);
  const env: string = process.env.TESTENV || "";
  const organizationId = workspace[env].organizationId;

  const baseURL: string =
    getURL.returnURL("bff-" + env) +
    "/v1/account-management/organization/" +
    organizationId +
    "/workspace";

  const response = await request.get(baseURL, {
    headers: {
      "Content-Type": "application/json",
      Authorization: "Bearer " + accessToken,
    },
  });

  expect(response.status()).toBe(200);
  const responseBody = await response.json();
  expect(responseBody.status).toBe("OK");
  expect(responseBody.result).not.toBeNull();
});

test("API Studio sanity - Get People Organization @api_sanity @apitest @api_org", async ({
  request,
}) => {
  const { getURL, getLoginToken } = createPageInstances();
  const accessToken = await getLoginToken.getAccessToken(request);
  const env: string = process.env.TESTENV || "";
  const organizationId = workspace[env].organizationId;

  const baseURL: string =
    getURL.returnURL("bff-" + env) +
    "/v1/account-management/organization/" +
    organizationId +
    "/user";

  const response = await request.get(baseURL, {
    headers: {
      "Content-Type": "application/json",
      Authorization: "Bearer " + accessToken,
    },
  });

  expect(response.status()).toBe(200);
  const responseBody = await response.json();
  expect(responseBody.status).toBe("OK");
  expect(responseBody.result).not.toBeNull();
});

//Integration

//Integration / App Store

test("API Studio sanity - Get Manage Integration @api_sanity @apitest @api_org", async ({
  request,
}) => {
  const { getURL, getLoginToken } = createPageInstances();
  const accessToken = await getLoginToken.getAccessToken(request);
  const env: string = process.env.TESTENV || "";
  const organizationId = workspace[env].organizationId;
  const workspaceIds = workspace[env].workspaceIds;

  const baseURL: string =
    getURL.returnURL("bff-" + env) +
    "/v1/account-management/organization/" +
    organizationId +
    "/workspace/" +
    workspaceIds +
    "/ad-account-health";

  const response = await request.get(baseURL, {
    headers: {
      "Content-Type": "application/json",
      Authorization: "Bearer " + accessToken,
    },
  });

  expect(response.status()).toBe(200);
  const responseBody = await response.json();
  expect(responseBody.status).toBe("OK");
  expect(responseBody.result).not.toBeNull();
});

//Data Management
test("API Studio sanity - Get Ad Accounts Groups @api_sanity @apitest @api_org", async ({
  request,
}) => {
  const { getURL, getLoginToken } = createPageInstances();
  const accessToken = await getLoginToken.getAccessToken(request);
  const env: string = process.env.TESTENV || "";
  test.skip(env == "prod");
  const organizationId = workspace[env].organizationId;
  const workspaceIds = workspace[env].workspaceIds;

  const baseURL: string =
    getURL.returnURL("anApiUrl-" + env) +
    "/api/v1/platformAccountGroup?platform=FACEBOOK&extra=accountCount&organizationId=" +
    organizationId +
    "&workspaceId=" +
    workspaceIds;

  const response = await request.get(baseURL, {
    headers: {
      "Content-Type": "application/json",
      Authorization: "Bearer " + accessToken,
    },
  });

  expect(response.status()).toBe(200);
  const responseBody = await response.json();

  expect(responseBody.status).toBe("ok");
  expect(responseBody.result).not.toBeNull();
});

//Data Management / Creative Groups

test("API Studio sanity - Get Element Sets @api_sanity @apitest @api_org", async ({
  request,
}) => {
  const { getURL, getLoginToken } = createPageInstances();
  const accessToken = await getLoginToken.getAccessToken(request);
  const env: string = process.env.TESTENV || "";
  const organizationId = workspace[env].organizationId;

  const baseURL: string =
    getURL.returnURL("bff-" + env) +
    "/v1/custom-element-set/organization/" +
    organizationId +
    "/platform/FACEBOOK?offset=0&perPage=1000";

  const response = await request.get(baseURL, {
    headers: {
      "Content-Type": "application/json",
      Authorization: "Bearer " + accessToken,
    },
  });

  expect(response.status()).toBe(200);
  const responseBody = await response.json();
  expect(responseBody.status).toBe("OK");
  expect(responseBody.result).not.toBeNull();
});

test("API Studio sanity - Get Brands Organization @api_sanity @apitest @api_org", async ({
  request,
}) => {
  const { getURL, getLoginToken } = createPageInstances();
  const accessToken = await getLoginToken.getAccessToken(request);
  const env: string = process.env.TESTENV || "";
  const organizationId = workspace[env].organizationId;

  const baseURL: string =
    getURL.returnURL("bff-" + env) +
    "/v1/account-management/organization/" +
    organizationId +
    "/brand?search=&perPage=20&offset=0";

  const response = await request.get(baseURL, {
    headers: {
      "Content-Type": "application/json",
      Authorization: "Bearer " + accessToken,
    },
  });

  expect(response.status()).toBe(200);
  const responseBody = await response.json();
  expect(responseBody.status).toBe("OK");
  expect(responseBody.result).not.toBeNull();
});

//Asset Locker
test("API Studio sanity - Get Asset Locker Organization @api_sanity @apitest @api_org", async ({
  request,
}) => {
  const { getURL, getLoginToken } = createPageInstances();
  const accessToken = await getLoginToken.getAccessToken(request);
  const env: string = process.env.TESTENV || "";
  test.skip(env == "prod" || env == "dev");
  const folderId = organizationData[env].folderId;
  const workspaceId = workspace[env].workspaceIds;

  const baseURL: string =
    getURL.returnURL("basicApi-" + env) +
    "/VidMob/api/v1/partnerAssetFolder/" +
    folderId +
    "/asset?folderId=" +
    folderId +
    "&partnerId=" +
    workspaceId;

  const response = await request.get(baseURL, {
    headers: {
      "Content-Type": "application/json",
      Authorization: "Bearer " + accessToken,
    },
  });

  expect(response.status()).toBe(200);
  const responseBody = await response.json();
  expect(responseBody.status).toBe("OK");
  expect(responseBody.result).not.toBeNull();
});
