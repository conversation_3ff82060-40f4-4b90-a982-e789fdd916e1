import { test, expect } from "@playwright/test";
import { createPageInstances } from "../../../utils/helper";
import * as workspace from '../../../feeder/workspace.json'
import * as createReportMediaPayload from '../../../payload/analytics/createReportMedia.json';
import * as createReportElementPayload from '../../../payload/analytics/createReportElement.json'
import * as renameReportMediaPayload from '../../../payload/analytics/renameReportMedia.json';
import * as renameReportElementPayload from '../../../payload/analytics/renameReportElement.json'

const env = process.env.TESTENV || "";

//GET
test(
    "API Analytics sanity - Get Load All Reports @api_sanity @apitest @api_an" ,
    async ({ request }) => {
      const { getURL, getLoginToken } = createPageInstances();
      const accessToken = await getLoginToken.getAccessToken(request);      
      const env: string = process.env.TESTENV || "";
      const orgId = workspace[env].organizationId
  
      const baseURL: string =
      getURL.returnURL("bff-" + env) +
      "/v1/analytics-report/organization/"+ orgId + "?enabled=true&searchTerm=&offset=0&perPage=20"
  
      const response = await request.get(baseURL, {
        headers: { 
            'Content-Type': 'application/json',
             Authorization: 'Bearer '+accessToken
          }
      });   
  
      expect(response.status()).toBe(200);
      const responseBody = await response.json();
      expect(responseBody.status).toBe('OK');
      expect(responseBody.result).not.toBeNull();
      expect(responseBody.pagination.perPage).toBe(20);
  
  });
  
  test(
    "API Analytics sanity - Get Load Creative Intelligence @api_sanity @apitest @api_an" ,
    async ({ request }) => {
      const { getURL, getLoginToken } = createPageInstances();
      const accessToken = await getLoginToken.getAccessToken(request);      
      const env: string = process.env.TESTENV || "";
      const orgId = workspace[env].organizationId
  
      const baseURL: string =
      getURL.returnURL("bff-" + env) +
      "/v1/account-management/workspace/all?organizationId="+ orgId + "&feature=CREATIVE-INTELLIGENCE"
  
      const response = await request.get(baseURL, {
        headers: {
            'Content-Type': 'application/json',
             Authorization: 'Bearer '+accessToken
          }
      });   
  
      expect(response.status()).toBe(200);
      const responseBody = await response.json();
      expect(responseBody.status).toBe('OK');
      expect(responseBody.result).not.toBeNull();
  
  });
  
  // POST
  test(
    "API Analytics sanity - Create Media Report @api_sanity @apitest @api_an" ,
    async ({ request }) => {
      const { getURL, getLoginToken } = createPageInstances();
      const accessToken = await getLoginToken.getAccessToken(request);      
      const env: string = process.env.TESTENV || "";
      const orgId = workspace[env].organizationId
  
      const baseURL: string =
      getURL.returnURL("bff-" + env) +
      "/v1/analytics-report/organization/"+ orgId
  
      const response = await request.post(baseURL, {
        headers: {
            'Content-Type': 'application/json',
             Authorization: 'Bearer '+accessToken
          },
        data: createReportMediaPayload[env],
      });   
  
      expect(response.status()).toBe(201);
      const responseBody = await response.json();
      expect(responseBody.status).toBe('OK');
      expect(responseBody.result.name).toBe('TEST 1303');
  
  });
  
  test(
    "API Analytics sanity - Create Element Report @api_sanity @apitest @api_an" ,
    async ({ request }) => {
      const { getURL, getLoginToken } = createPageInstances();
      const accessToken = await getLoginToken.getAccessToken(request);      
      const env: string = process.env.TESTENV || "";
      const orgId = workspace[env].organizationId
  
      const baseURL: string =
      getURL.returnURL("bff-" + env) +
      "/v1/analytics-report/organization/"+ orgId
  
      const response = await request.post(baseURL, {
        headers: {
            'Content-Type': 'application/json',
             Authorization: 'Bearer '+accessToken
          },
        data: createReportElementPayload[env],
      });  
  
      expect(response.status()).toBe(201);
      const responseBody = await response.json();
      expect(responseBody.status).toBe('OK');
      expect(responseBody.result.name).toBe('yrd');
  
  });
  
  
  //PATCH
  test(
  "API Analytics sanity - Rename Media Report @api_sanity @apitest @api_an" ,
  async ({ request }) => {
    const { getURL, getLoginToken, setReportMediaId } = createPageInstances();
    const accessToken = await getLoginToken.getAccessToken(request); 
    const mediaReportID = await setReportMediaId.ReportMediaId(request);
    const env: string = process.env.TESTENV || "";
    const orgId = workspace[env].organizationId
  
    const baseURL: string =
    getURL.returnURL("bff-" + env) +
    "/v1/analytics-report/organization/"+ orgId + "/report/" + mediaReportID
  
    const response = await request.patch(baseURL, {
      headers: {
          'Content-Type': 'application/json',
           Authorization: 'Bearer '+ accessToken
        },
      data: renameReportMediaPayload[env],
    }); 
  
    expect(response.status()).toBe(200);
    const responseBody = await response.json();
    expect(responseBody.status).toBe('OK');
    expect(responseBody.result.name).toBe('TEST 1303 renamed');
  
  });
  
  test(
  "API Analytics sanity - Rename Elements Report @api_sanity @apitest @api_an" ,
  async ({ request }) => {
    const { getURL, getLoginToken, setReportElementId } = createPageInstances();
    const accessToken = await getLoginToken.getAccessToken(request);   
    const mediaElementID = await setReportElementId.ReportElementId(request);
   
    const env: string = process.env.TESTENV || "";
    const orgId = workspace[env].organizationId
  
    const baseURL: string =
    getURL.returnURL("bff-" + env) +
    "/v1/analytics-report/organization/"+ orgId + "/report/" + mediaElementID
  
    const response = await request.patch(baseURL, {
      headers: {
          'Content-Type': 'application/json',
           Authorization: 'Bearer '+accessToken
        },
      data: renameReportElementPayload[env],
    });   
  
    expect(response.status()).toBe(200);
    const responseBody = await response.json();
    expect(responseBody.status).toBe('OK');
    expect(responseBody.result.name).toBe('yrd renamed');
  
  });
  