import { test, expect } from "@playwright/test";
import { createPageInstances } from "../../../utils/helper";
import * as stud from "../../../feeder/stud.json";
import * as createProjectsPayload from "../../../payload/smoke/createProjects.json";

test("API Studio sanity - Get Load Project @api_sanity @apitest @api_stud", async ({
  request,
}) => {
  const { getURL, getLoginToken } = createPageInstances();
  const accessToken = await getLoginToken.getAccessToken(request);
  const env: string = process.env.TESTENV || "";
  const studProjectID = stud[env].studioProjectID;

  const baseURL: string =
    getURL.returnURL("basicApi-" + env) +
    "/VidMob/api/v2/project/" +
    studProjectID;

  const response = await request.get(baseURL, {
    headers: {
      "Content-Type": "application/json",
      Authorization: "Bearer " + accessToken,
    },
  });

  expect(response.status()).toBe(200);
  const responseBody = await response.json();
  expect(responseBody.status).toBe("OK");
  expect(responseBody.result).not.toBeNull();
});

test("API Studio sanity - Get Project Assets @api_sanity @apitest @api_stud", async ({
  request,
}) => {
  const { getURL, getLoginToken } = createPageInstances();
  const accessToken = await getLoginToken.getAccessToken(request);
  const env: string = process.env.TESTENV || "";
  const studProjectID = stud[env].studioProjectID;

  const baseURL: string =
    getURL.returnURL("basicApi-" + env) +
    "/VidMob/api/v2/project/" +
    studProjectID +
    "/partnerAssetFolder/0";

  const response = await request.get(baseURL, {
    headers: {
      "Content-Type": "application/json",
      Authorization: "Bearer " + accessToken,
    },
  });

  expect(response.status()).toBe(200);
  const responseBody = await response.json();
  expect(responseBody.status).toBe("OK");
  expect(responseBody.result).not.toBeNull();
  expect(responseBody.result.id).not.toBeNull();
});

test("API Studio sanity - Get Project Outputs @api_sanity @apitest @api_stud", async ({
  request,
}) => {
  const { getURL, getLoginToken } = createPageInstances();
  const accessToken = await getLoginToken.getAccessToken(request);
  const env: string = process.env.TESTENV || "";
  const studProjectID = stud[env].studioProjectID;

  const baseURL: string =
    getURL.returnURL("basicApi-" + env) +
    "/VidMob/api/v2/project/" +
    studProjectID +
    "/outputVideo?projectId=" +
    studProjectID;

  const response = await request.get(baseURL, {
    headers: {
      "Content-Type": "application/json",
      Authorization: "Bearer " + accessToken,
    },
  });

  expect(response.status()).toBe(200);
  const responseBody = await response.json();
  expect(responseBody.status).toBe("OK");
  expect(responseBody.pagination).not.toBeNull();
  expect(responseBody.pagination.perPage).toBeGreaterThan(19);
});

test("API Studio sanity - Get Project Team @api_sanity @apitest @api_stud", async ({
  request,
}) => {
  const { getURL, getLoginToken } = createPageInstances();
  const accessToken = await getLoginToken.getAccessToken(request);
  const env: string = process.env.TESTENV || "";
  const studProjectID = stud[env].studioProjectID;

  const baseURL: string =
    getURL.returnURL("basicApi-" + env) +
    "/VidMob/api/v2/project/" +
    studProjectID +
    "/team";

  const response = await request.get(baseURL, {
    headers: {
      "Content-Type": "application/json",
      Authorization: "Bearer " + accessToken,
    },
  });

  expect(response.status()).toBe(200);
  const responseBody = await response.json();
  expect(responseBody.status).toBe("OK");
  expect(responseBody.result).not.toBeNull();
  expect(responseBody.result.person).not.toBeNull();
});

test("API Studio sanity - Get Project Notes @api_sanity @apitest @api_stud", async ({
  request,
}) => {
  const { getURL, getLoginToken } = createPageInstances();
  const accessToken = await getLoginToken.getAccessToken(request);
  const env: string = process.env.TESTENV || "";
  const studProjectID = stud[env].studioProjectID;

  const baseURL: string =
    getURL.returnURL("basicApi-" + env) +
    "/VidMob/api/v2/project/" +
    studProjectID +
    "/briefNote";

  const response = await request.get(baseURL, {
    headers: {
      "Content-Type": "application/json",
      Authorization: "Bearer " + accessToken,
    },
  });

  expect(response.status()).toBe(200);
  const responseBody = await response.json();
  expect(responseBody.status).toBe("OK");
  expect(responseBody.result).not.toBeNull();
  expect(responseBody.result.person).not.toBeNull();
});

//POST Project
test("API Analytics sanity - Create Media Report @api_sanity @apitest @api_stud", async ({
  request,
}) => {
  const { getURL, getLoginToken } = createPageInstances();
  const accessToken = await getLoginToken.getAccessToken(request);
  const env: string = process.env.TESTENV || "";
  const partnerID = createProjectsPayload[env].partnerId;

  const baseURL: string =
    getURL.returnURL("basicApi-" + env) +
    "/VidMob/api/v2/project/?partnerId=" +
    partnerID;

  const response = await request.post(baseURL, {
    headers: {
      "Content-Type": "application/json",
      Authorization: "Bearer " + accessToken,
    },
    data: createProjectsPayload[env],
  });

  expect(response.status()).toBe(200);
  const responseBody = await response.json();
  expect(responseBody.status).toBe("OK");
  expect(responseBody.result.name).toBe("test");
});
