/* eslint-disable @typescript-eslint/no-unused-vars */
import { test, expect } from "@playwright/test";
import { createPageInstances } from "../../../utils/helper";
import * as feeder_stud from '../../../feeder/stud.json'
import * as workspace from '../../../feeder/workspace.json'
import * as createReportMediaPayload from '../../../payload/analytics/createReportMedia.json';
import * as createReportElementPayload from '../../../payload/analytics/createReportElement.json'
import * as renameReportMediaPayload from '../../../payload/analytics/renameReportMedia.json';
import * as renameReportElementPayload from '../../../payload/analytics/renameReportElement.json'

const env = process.env.TESTENV || "";

async function performAPICall(request, endpoint, workspaceId) {
  const { getURL, getLoginToken } = createPageInstances();
  const accessToken = await getLoginToken.getAccessToken(request);

  const baseURL: string = getURL.returnURL("bff-" + env) + endpoint;

  const requestOptions = {
    headers: {
      'Content-Type': 'application/json',
      Authorization: 'Bearer ' + accessToken
    },
  };

  const response = await request.get(baseURL, requestOptions)

  return response;
}

  /**
   * Scorecards
   * */
  test(
    "sanity API test - SCOR Load PRE_FLIGHT Scorecards page @api_sanity @api_regression @api_scor",
    async ({ request }) => {
      const workspaceId = feeder_stud[env].partnerId;
      const response = await performAPICall(request, `/v1/scorecard?workspaceId=${workspaceId}&types=PRE_FLIGHT&sortBy=lastModifiedDate&sortOrder=DESC&offset=0&perPage=10`, workspaceId);

      expect(response.status()).toBe(200);

      const responseBody = await response.json();
      expect(responseBody.status).toBe('OK');
      expect(responseBody.result).not.toBeNull();
      expect(responseBody.result).not.toContain('IN_FLIGHT');
      expect(responseBody.result).not.toContain('AD_ACCOUNT');

      expect(responseBody.pagination.perPage).toBe(10);
  });

  test(
      "sanity API test - SCOR Load IN-FLIGHT Scorecards page @api_sanity @api_regression @api_scor",
      async ({ request }) => {
        const workspaceId = feeder_stud[env].partnerId;
        const response = await performAPICall(request, `/v1/scorecard?workspaceId=${workspaceId}&types=IN_FLIGHT&sortBy=lastModifiedDate&sortOrder=DESC&offset=0&perPage=10`, workspaceId);

        expect(response.status()).toBe(200);

        const responseBody = await response.json();


        expect(responseBody.status).toBe('OK');
        expect(responseBody.result).not.toBeNull();
        expect(responseBody.result).not.toContain('PRE_FLIGHT');
        expect(responseBody.result).not.toContain('AD_ACCOUNT');
        //expect(responseBody.result[0].batchType).toContain('IN_FLIGHT');

        expect(responseBody.pagination.perPage).toBe(10);
  });
  test(
    "sanity API test - SCOR Load AD_ACCOUNT Scorecards page @api_sanity @apitest @api_scor",
    async ({ request }) => {
      const workspaceId = feeder_stud[env].partnerId;
      const response = await performAPICall(request, `/v1/scorecard?workspaceId=${workspaceId}&types=AD_ACCOUNT&sortBy=lastModifiedDate&sortOrder=DESC&offset=0&perPage=10`, workspaceId);

      expect(response.status()).toBe(200);

      const responseBody = await response.json();

      expect(responseBody.status).toBe('OK');
      expect(responseBody.result).not.toBeNull();
      expect(responseBody.result[0]).not.toContain('PRE_FLIGHT');

      expect(responseBody.pagination.perPage).toBe(10);
  });

  /**
 * Criteria
 */
  test(
    "sanity API test - SCOR Load Criteria page @api_sanity @apitest @api_scor",
    async ({ request }) => {
      const workspaceId = feeder_stud[env].partnerId;
      const response = await performAPICall(request, `/v1/criteria?workspaceId=${workspaceId}&offset=0&perPage=10`, workspaceId);

      expect(response.status()).toBe(200);

      const responseBody = await response.json();
      expect(responseBody.status).toBe('OK');
      expect(responseBody.result).not.toBeNull();
      expect(responseBody.pagination.perPage).toBe(10);
  });

  /**
   * Reports
   */
test(
    "sanity API test - SCOR Load Reports page @api_sanity @apitest @api_scor",
    async ({ request }) => {
      const partnerId = feeder_stud[env].partnerId

      const response = await performAPICall(request, `/v1/report/workspace/${partnerId}?types=ADOPTION,ADHERENCE,DIVERSITY,IMPRESSION_ADHERENCE&perPage=10`, partnerId);
      expect(response.status()).toBe(200);

      const responseBody = await response.json();
      expect(responseBody.status).toBe('OK');
      expect(responseBody.result).not.toBeNull();
      expect(responseBody.pagination.perPage).toBe(10);
  });
