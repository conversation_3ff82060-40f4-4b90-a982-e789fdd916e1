import { test, expect } from "@playwright/test";
import { createPageInstances } from "../../../utils/helper";

  test(
    "API test - Verify successfully login by correct param @apitest @api_auth",
    async ({ request }) => {
      const { getURL } = createPageInstances();
      const env: string = process.env.TESTENV || "";
      const username: string = process.env.EMAIL || "";
      const password: string = process.env.PASSWORD || "";
      
      const baseURL: string =
      getURL.returnURL("acs-login-"+env) +
      "/api/login";

      const response = await request.post(baseURL, {
        headers: {
            'Content-Type': 'application/json'
          },
        data: {
          username: username,
          password: password,
        }
      });      
      
      expect(response.status()).toBe(200);
      const responseBody = await response.json();

      expect(responseBody.status).toBe('OK');
      expect(responseBody.result.sessionId).not.toBeNull();
      expect(responseBody.result.accessToken).not.toBeNull();
    });

type loginScenario = {
  testScenario: string;
  username: string;
  password: string;
};

const invalidLogin: loginScenario[] = [];
const correctUser = process.env.EMAIL || "";
const correctPW = process.env.PASSWORD || "";

/**
 * Outline Test Scenarios
 * Error status: 401
 */
invalidLogin.push({ username:correctUser , password:"error",  testScenario: "correct user - wrong password" });
invalidLogin.push({ username:"user" , password:correctPW,  testScenario: "correct password - wrong user" });

invalidLogin.forEach((scenario) => {
  test(
    "API test - Authentication - negative " +
      scenario.testScenario +
      "- @apitest @api_auth",
    async ({ request }) => {
      const { getURL } = createPageInstances();
      const env: string = process.env.TESTENV || "";
      
      const baseURL: string =
      getURL.returnURL("acs-login-"+env) +
      "/api/login";

      const response = await request.post(baseURL, {
        headers: {
            'Content-Type': 'application/json'
          },
        data: {
          username: scenario.username,
          password: scenario.password,
        }
      });  

      expect(response.status()).toBe(401);

      const responseBody = await response.json();
      expect(responseBody.status).toBe('error');
      expect(responseBody.error.code).toContain('vidmob.login.invalid');
      expect(responseBody.error.type).toContain('invalid');  
      expect(responseBody.error.system).toContain('login');  
      expect(responseBody.error.message).toContain('You entered invalid login information');  

    },
  );
});

const badRequest: loginScenario[] = []

/**
 * Outline Test Scenarios
 * Error status: 400
 */
badRequest.push({ username:correctUser , password:"error",  testScenario: "correct user - correct password - extra param" });
badRequest.push({ username:"user" , password:correctPW,  testScenario: "correct password - wrong user - extra param" });
badRequest.push({ username:correctUser , password:correctPW,  testScenario: "correct user and password - extra param" });

badRequest.forEach((scenario) => {
  test(
    "API test - Authentication - negative - Bad Request" +
      scenario.testScenario +
      " error returned  @apitest @api_auth",
    async ({ request }) => {
      const { getURL } = createPageInstances();
      const env: string = process.env.TESTENV || "";
      
      const baseURL: string =
      getURL.returnURL("acs-login-"+env) +
      "/api/login";
      const response = await request.post(baseURL, {
        headers: {
            'Content-Type': 'application/json'
          },
        data: {
          user: scenario.username,
          password: scenario.password,
        }
      });  
      expect(response.status()).toBe(400);

    },
  );
});