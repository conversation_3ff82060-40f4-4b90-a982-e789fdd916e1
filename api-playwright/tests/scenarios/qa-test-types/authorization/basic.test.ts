import { test, expect } from "@playwright/test";
import { createPageInstances } from "../../../utils/helper";
import * as negativePlatformMediaPayload from '../../../payload/analytics/authorization.json'
import * as successPlatformMediaPayload from '../../../payload/analytics/platformMedia.json'

const env = process.env.TESTENV || "";

  test(
    "API test - Success basic login @apitest @api_auth ",
    async ({ request }) => {
      const { getURL } = createPageInstances();
      const env: string = process.env.TESTENV || "";
      const username: string = process.env.EMAIL || "";
      const password: string = process.env.PASSWORD || "";
      
      const baseURL: string =
      getURL.returnURL("acs-login-"+env) +
      "/api/login";

      const response = await request.post(baseURL, {
        headers: {
            'Content-Type': 'application/json'
          },
        data: {
          username: username,
          password: password,
        }
      });      
      
      expect(response.status()).toBe(200);
      const responseBody = await response.json();

      expect(responseBody.status).toBe('OK');
      expect(responseBody.result.sessionId).not.toBeNull();
      expect(responseBody.result.accessToken).not.toBeNull();
    });

type loginScenario = {
  testScenario: string;
  username: string;
  password: string;
};

const invalidLogin: loginScenario[] = [];
const correctUser = process.env.EMAIL || "";
const correctPW = process.env.PASSWORD || "";

/**
 * Outline Test Scenarios
 * Error status: 401
 */
invalidLogin.push({ username:correctUser , password:"error",  testScenario: "wrong password" });
invalidLogin.push({ username:"user" , password:correctPW,  testScenario: "wrong user" });

invalidLogin.forEach((scenario) => {
  test(
    "API test - Not Success basic login " +
      scenario.testScenario +
      "-   @apiRegressionTest @api_auth ",
    async ({ request }) => {
      const { getURL } = createPageInstances();
      const env: string = process.env.TESTENV || "";
      
      const baseURL: string =
      getURL.returnURL("acs-login-"+env) +
      "/api/login";

      const response = await request.post(baseURL, {
        headers: {
            'Content-Type': 'application/json'
          },
        data: {
          username: scenario.username,
          password: scenario.password,
        }
      });  

      expect(response.status()).toBe(401);

      const responseBody = await response.json();
      expect(responseBody.status).toBe('error');
      expect(responseBody.error.code).toContain('vidmob.login.invalid');
      expect(responseBody.error.type).toContain('invalid');  
      expect(responseBody.error.system).toContain('login');  
      expect(responseBody.error.message).toContain('You entered invalid login information');  

    },
  );
});

test(
  "API test - Success using bearer token @apitest @api_auth",
  async ({ request }) => {
    const { getURL, getLoginToken } = createPageInstances();
    const accessToken = await getLoginToken.getAccessToken(request);

    const baseURL: string =
      getURL.returnURL("anApiUrl-" + env) +
        "/api/v2/platformMedia";

    const requestOptions = {
      data: successPlatformMediaPayload[env],
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer '+accessToken
      },
    };          
    const response = await request.post(baseURL, requestOptions)

    expect(response.status()).toBe(200);

    const responseBody = await response.json();
    expect(responseBody.status).toBe('ok');
    expect(responseBody.result.platformMedias).not.toBeNull();
    expect(responseBody.result.platformMedias.media).not.toBeNull();
  });

  test(
    "API test - Not Success using wrong bearer token @apitest @api_auth",
    async ({ request }) => {
      const { getURL } = createPageInstances();
      const accessToken = '12312321546548131564';
  
      const baseURL: string =
        getURL.returnURL("anApiUrl-" + env) +
          "/api/v2/platformMedia";
  
      const requestOptions = {
        data: negativePlatformMediaPayload[env],
        headers: {
          'Content-Type': 'application/json',
          Authorization: 'Bearer '+accessToken
        },
      };          
      const response = await request.post(baseURL, requestOptions)
  
      expect(response.status()).toBe(403);
  
      const responseBody = await response.json();
      expect(responseBody.status).toBe('error');
      expect(responseBody.error.message).toContain('Request not authorized');
      
    });

    test(
      "API test - Not authorized user @apitest @api_auth",
      async ({ request }) => {
        const { getURL, getLoginToken } = createPageInstances();
        const accessToken = await getLoginToken.getAccessToken(request); //<EMAIL> - doesn not have access to org
    
        const baseURL: string =
          getURL.returnURL("anApiUrl-" + env) +
            "/api/v2/platformMedia";
    
        const requestOptions = {
          data: negativePlatformMediaPayload[env],
          headers: {
            'Content-Type': 'application/json',
            Authorization: 'Bearer '+accessToken
          },
        };          
        const response = await request.post(baseURL, requestOptions)
    
        expect(response.status()).toBe(400);
    
        const responseBody = await response.json();
        expect(responseBody.status).toBe('error');
        expect(responseBody.error.message).toContain('is not authorized to access platform ad accounts');
        
      });