import { test, expect, APIResponse } from "@playwright/test";
import { ApiKeyInfo } from "../../../utils/api-key.types";
import { SuccessResponseBody } from "../../../utils/scoring.types";
import { createPageInstances } from "../../../utils/helper";
import {
  deleteApiKey,
  getApiKeyInfo,
  scopes,
} from "../../../utils/api-token-utils";

type SuccessfulOrganizationResponse = {
  status: "OK";
  result: {
    id: string;
    name: string;
  };
};

const env = process.env.TESTENV || "dev";

const envTestResource: {
  [env: string]: { organization: SuccessfulOrganizationResponse };
} = {
  dev: {
    organization: {
      status: "OK",
      result: {
        id: "27a7e882-43de-4bfa-8f53-3b62875b8432",
        name: "TAF Organization",
      },
    },
  },
  stage: {
    organization: {
      status: "OK",
      result: {
        id: "cb90fd8c-4740-43c6-9def-98d9f8eb5a0b",
        name: "Generic Vidmob Organization",
      },
    },
  },
  prod: {
    organization: {
      status: "OK",
      result: {
        id: "855275f7-abf0-47a4-9885-79ad7559db57",
        name: "Chirag Test Partner - Brand Gov",
      },
    },
  },
};

test(`Success 200 Response - Fetch Users Organization @apitest`, async ({
  request,
}) => {
  const apiTokenInfo: ApiKeyInfo = await getApiKeyInfo(
    request,
    scopes["all-rw"]
  );
  const { getURL } = createPageInstances();
  const baseUrl: string | undefined = getURL.returnURL(`api-bff-${env}`);
  const fullUrl: string = `${baseUrl}/v1/organization`;
  const response: APIResponse = await request.get(fullUrl, {
    headers: {
      Authorization: `Bearer ${apiTokenInfo.apiKey}`,
    },
  });
  try {
    expect(response.status()).toBe(200);
    const responseBody: SuccessResponseBody = await response.json();
    expect(responseBody["result"]["id"]).toBe(
      envTestResource[env]["organization"]["result"]["id"]
    );
    expect(responseBody["result"]["name"]).toBe(
      envTestResource[env]["organization"]["result"]["name"]
    );
  } catch (error) {
    console.error("Error on getting api key:", error);
  } finally {
    await deleteApiKey(request, apiTokenInfo.id);
  }
});

test(`Success 200 Response - Fetch Users Organization Workspaces @apitest`, async ({
  request,
}) => {
  const apiTokenInfo: ApiKeyInfo = await getApiKeyInfo(
    request,
    scopes["all-rw"]
  );
  const { getURL } = createPageInstances();
  const baseUrl: string | undefined = getURL.returnURL(`api-bff-${env}`);
  const fullUrl: string = `${baseUrl}/v1/workspaces`;
  const response: APIResponse = await request.get(fullUrl, {
    headers: {
      Authorization: `Bearer ${apiTokenInfo.apiKey}`,
    },
  });
  try {
    expect(response.status()).toBe(200);
    const responseBody: SuccessResponseBody = await response.json();
    expect(responseBody["result"]).toBeInstanceOf(Array);
  } catch (error) {
    console.error("Error on getting api key:", error);
  } finally {
    await deleteApiKey(request, apiTokenInfo.id);
  }
});

test(`Forbidden 403 Response - Fetching scorecard with no scoring access api key @apitest`, async ({
  request,
}) => {
  const apiTokenInfo: ApiKeyInfo = await getApiKeyInfo(
    request,
    scopes["platform-rw"]
  );
  const { getURL } = createPageInstances();
  const baseUrl: string | undefined = getURL.returnURL(`api-bff-${env}`);
  const fullUrl: string = `${baseUrl}/v1/scoring/workspace/1/scorecards?types=PRE_FLIGHT`;
  const response: APIResponse = await request.get(fullUrl, {
    headers: {
      Authorization: `Bearer ${apiTokenInfo.apiKey}`,
    },
  });
  try {
    expect(response.status()).toBe(403);
  } catch (error) {
    console.error("Error on getting api key:", error);
  } finally {
    await deleteApiKey(request, apiTokenInfo.id);
  }
});

test(`Unauthorized 401 Response - Fetching scorecard with no scoring access api key @apitest`, async ({
  request,
}) => {
  const apiTokenInfo: ApiKeyInfo = await getApiKeyInfo(
    request,
    scopes["all-rw"]
  );
  const { getURL } = createPageInstances();
  const baseUrl: string | undefined = getURL.returnURL(`api-bff-${env}`);
  const fullUrl: string = `${baseUrl}/v1/scoring/workspace/1/scorecards?types=PRE_FLIGHT`;
  const response: APIResponse = await request.get(fullUrl, {
    headers: {
      Authorization: `Bearer ${apiTokenInfo.apiKey}`,
    },
  });
  try {
    expect(response.status()).toBe(401);
  } catch (error) {
    console.error("Error on getting api key:", error);
  } finally {
    await deleteApiKey(request, apiTokenInfo.id);
  }
});
