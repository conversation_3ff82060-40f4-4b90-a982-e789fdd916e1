/* eslint-disable @typescript-eslint/no-unused-vars */
import { test, expect } from "@playwright/test";
import { createPageInstances } from "../../utils/helper";
// @ts-ignore
import * as workspace from '../../feeder/workspace.json'
// @ts-ignore
import * as feeder_stud from '../../feeder/stud.json'

const env = process.env.TESTENV || "";

async function performAPICall(request, endpoint, workspaceId) {
    const { getURL, getLoginToken } = createPageInstances();
    const accessToken = await getLoginToken.getAccessToken(request);

    const baseURL: string = getURL.returnURL("bff-" + env) + endpoint;

    const requestOptions = {
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + accessToken
      },
    };

    const response = await request.get(baseURL, requestOptions)

    return response;
  }

//GET
test(
    "API Analytics smoke - Get Load All Reports @api_smoke" ,
    async ({ request }) => {
      const { getURL, getLoginToken } = createPageInstances();
      const accessToken = await getLoginToken.getAccessToken(request);
      const env: string = process.env.TESTENV || "";
      const orgId = workspace[env].organizationId

      const baseURL: string =
      getURL.returnURL("bff-" + env) +
      "/v1/analytics-report/organization/"+ orgId + "?enabled=true&searchTerm=&offset=0&perPage=20"

      const response = await request.get(baseURL, {
        headers: {
            'Content-Type': 'application/json',
             Authorization: 'Bearer '+accessToken
          }
      });

      expect(response.status()).toBe(200);
      const responseBody = await response.json();
      expect(responseBody.status).toBe('OK');
      expect(responseBody.result).not.toBeNull();
      expect(responseBody.pagination.perPage).toBe(20);

  });

  test(
    "API Analytics smoke - Get Load Creative Intelligence @api_smoke" ,
    async ({ request }) => {
      const { getURL, getLoginToken } = createPageInstances();
      const accessToken = await getLoginToken.getAccessToken(request);
      const env: string = process.env.TESTENV || "";
      const orgId = workspace[env].organizationId

      const baseURL: string =
      getURL.returnURL("bff-" + env) +
      "/v1/account-management/workspace/all?organizationId="+ orgId + "&feature=CREATIVE-INTELLIGENCE"

      const response = await request.get(baseURL, {
        headers: {
            'Content-Type': 'application/json',
             Authorization: 'Bearer '+accessToken
          }
      });

      expect(response.status()).toBe(200);
      const responseBody = await response.json();
      expect(responseBody.status).toBe('OK');
      expect(responseBody.result).not.toBeNull();

  });

  test(
    "API Scoring smoke -  Load PRE_FLIGHT Scorecards page @api_smoke",
    async ({ request }) => {
      const workspaceId = feeder_stud[env].partnerId;
      const response = await performAPICall(request, `/v1/scorecard?workspaceId=${workspaceId}&types=PRE_FLIGHT&sortBy=lastModifiedDate&sortOrder=DESC&offset=0&perPage=10`, workspaceId);

      expect(response.status()).toBe(200);

      const responseBody = await response.json();
      expect(responseBody.status).toBe('OK');
      expect(responseBody.result).not.toBeNull();
      expect(responseBody.result).not.toContain('IN_FLIGHT');
      expect(responseBody.result).not.toContain('AD_ACCOUNT');

      expect(responseBody.pagination.perPage).toBe(10);
  });

    /**
 * Criteria
 */
    test(
        "API Scoring smoke -  Load Criteria page @api_smoke",
        async ({ request }) => {
          const workspaceId = feeder_stud[env].partnerId;
          const response = await performAPICall(request, `/v1/criteria?workspaceId=${workspaceId}&offset=0&perPage=10`, workspaceId);

          expect(response.status()).toBe(200);

          const responseBody = await response.json();
          expect(responseBody.status).toBe('OK');
          expect(responseBody.result).not.toBeNull();
          expect(responseBody.pagination.perPage).toBe(10);
      });

      /**
       * Reports
       */
    test(
        "API Scoring smoke -  Load Reports page @api_smoke",
        async ({ request }) => {
          const { getLoginToken } = createPageInstances();
          const partnerId = feeder_stud[env].partnerId

          const response = await performAPICall(request, `/v1/report/workspace/${partnerId}?types=ADOPTION,ADHERENCE,DIVERSITY,IMPRESSION_ADHERENCE&perPage=10`, partnerId);
          expect(response.status()).toBe(200);

          const responseBody = await response.json();
          expect(responseBody.status).toBe('OK');
          expect(responseBody.result).not.toBeNull();
          expect(responseBody.pagination.perPage).toBe(10);
      });

      test(
        "API Analytics smoke - Get Load Creative Intelligence @bff_test" ,
        async ({ request }) => {
          const { getURL, getLoginToken } = createPageInstances();
          const accessToken = await getLoginToken.getAccessToken(request);
          const env: string = process.env.TESTENV || "";
          const orgId = workspace[env].organizationId

          const baseURL: string =
          getURL.returnURL("bff-" + env) +
          "/v1/account-management/workspace/all?organizationId="+ orgId + "&feature=CREATIVE-INTELLIGENCE"

          const response = await request.get(baseURL, {
            headers: {
                'Content-Type': 'application/json',
                 Authorization: 'Bearer '+accessToken
              }
          });

          expect(response.status()).toBe(200);
          const responseBody = await response.json();
          expect(responseBody.status).toBe('OK');
          expect(responseBody.result).not.toBeNull();
      });
