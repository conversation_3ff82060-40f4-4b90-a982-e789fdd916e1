import { test, expect } from "@playwright/test";
import { createPageInstances } from "../../utils/helper";
// @ts-ignore
import * as feeder_stud from "../../feeder/stud.json";
// @ts-ignore
import * as workspace from "../../feeder/workspace.json";
const env = process.env.TESTENV || "";

test("BVT API test - Success basic login @api_bvt", async ({ request }) => {
  const { getURL } = createPageInstances();
  const env: string = process.env.TESTENV || "";
  const username: string = process.env.EMAIL || "";
  const password: string = process.env.PASSWORD || "";

  const baseURL: string = getURL.returnURL("acs-login-" + env) + "/api/login";

  const response = await request.post(baseURL, {
    headers: {
      "Content-Type": "application/json",
    },
    data: {
      username: username,
      password: password,
    },
  });

  expect(response.status()).toBe(200);
  const responseBody = await response.json();

  expect(responseBody.status).toBe("OK");
  expect(responseBody.result.sessionId).not.toBeNull();
  expect(responseBody.result.accessToken).not.toBeNull();
});
test("BVT API test - Success using bearer token @api_bvt", async ({
  request,
}) => {
  const { getURL, getLoginToken } = createPageInstances();
  const accessToken = await getLoginToken.getAccessToken(request);
  const partnerId = feeder_stud[env].partnerId;

  const baseURL: string =
    getURL.returnURL("basicApi-" + env) +
    "/VidMob/api/v2/partner/" +
    partnerId +
    "/project?partnerId=" +
    partnerId +
    "&perPage=10";

  const requestOptions = {
    headers: {
      "Content-Type": "application/json",
      Authorization: "Bearer " + accessToken,
    },
  };
  const response = await request.get(baseURL, requestOptions);

  expect(response.status()).toBe(200);

  const responseBody = await response.json();
  expect(responseBody.status).toBe("OK");
});
test("BVT API test - Success MFA login @api_bvt", async ({ request }) => {
  const { getURL } = createPageInstances();
  const env: string = process.env.TESTENV || "";
  const username: string = process.env.MFA_USER || "";
  const password: string = process.env.MFA_PASSWORD || "";

  const baseURL: string = getURL.returnURL("acs-login-" + env) + "/api/login";

  const response = await request.post(baseURL, {
    headers: {
      "Content-Type": "application/json",
    },
    data: {
      username: username,
      password: password,
      mfa: "123",
    },
  });

  expect(response.status()).toBe(401);
  const responseBody = await response.json();
  expect(responseBody.status).toBe("error");
  expect(responseBody.error.code).toContain("twoFactorAuthentication");
  expect(responseBody.error.type).toContain("twoFactorAuthentication");
  expect(responseBody.error.system).toContain("login");
  expect(responseBody.error.message).toContain(
    "You have two factor authentication enabled."
  );
});
test("BVT API test - Success STUD Active Project page @api_bvt", async ({
  request,
}) => {
  const { getURL, getLoginToken } = createPageInstances();
  const accessToken = await getLoginToken.getAccessToken(request);
  const partnerId = feeder_stud[env].partnerId;

  const baseURL: string =
    getURL.returnURL("basicApi-" + env) +
    "/VidMob/api/v2/partner/" +
    partnerId +
    "/project?partnerId=" +
    partnerId +
    "&perPage=10";

  const requestOptions = {
    headers: {
      "Content-Type": "application/json",
      Authorization: "Bearer " + accessToken,
    },
  };
  const response = await request.get(baseURL, requestOptions);

  expect(response.status()).toBe(200);

  const responseBody = await response.json();
  expect(responseBody.status).toBe("OK");
  expect(responseBody.result).not.toBeNull();
  expect(responseBody.pagination.perPage).toBe(10);
});
test("BVT API test - Success PLAT Workspaces page @api_bvt", async ({
  request,
}) => {
  const { getURL, getLoginToken } = createPageInstances();
  const accessToken = await getLoginToken.getAccessToken(request);
  const orgId = workspace[env].organizationId;

  const baseURL: string =
    getURL.returnURL("bff-" + env) +
    "/v1/account-management/organization/" +
    orgId +
    "/workspace?search=&perPage=20&offset=0";
  const requestOptions = {
    headers: {
      "Content-Type": "application/json",
      Authorization: "Bearer " + accessToken,
    },
  };
  const response = await request.get(baseURL, requestOptions);

  expect(response.status()).toBe(200);

  const responseBody = await response.json();
  expect(responseBody.status).toBe("OK");
  expect(responseBody.result).not.toBeNull();
  expect(responseBody.pagination.perPage).toBe(20);
});
test("BVT API test - Success AN Reports page @api_bvt", async ({ request }) => {
  const { getURL, getLoginToken } = createPageInstances();
  const accessToken = await getLoginToken.getAccessToken(request);
  const orgId = workspace[env].organizationId;

  const baseURL: string =
    getURL.returnURL("bff-" + env) +
    "/v1/analytics-report/organization/" +
    orgId +
    "/?enabled=true&searchTerm=&offset=0&perPage=20";
  const requestOptions = {
    headers: {
      "Content-Type": "application/json",
      Authorization: "Bearer " + accessToken,
    },
  };
  const response = await request.get(baseURL, requestOptions);

  expect(response.status()).toBe(200);

  const responseBody = await response.json();
  expect(responseBody.status).toBe("OK");
  expect(responseBody.result).not.toBeNull();
  expect(responseBody.pagination.perPage).toBe(20);
});
test("BVT API test - Success SCOR Reports page @api_bvt", async ({
  request,
}) => {
  const { getURL, getLoginToken } = createPageInstances();
  const accessToken = await getLoginToken.getAccessToken(request);
  const partnerId = feeder_stud[env].partnerId;

  const baseURL: string =
    getURL.returnURL("bff-" + env) +
    "/v1/report/workspace/" +
    partnerId +
    "?types=ADOPTION,ADHERENCE,DIVERSITY,IMPRESSION_ADHERENCE&perPage=10";
  const requestOptions = {
    headers: {
      "Content-Type": "application/json",
      Authorization: "Bearer " + accessToken,
    },
  };
  const response = await request.get(baseURL, requestOptions);

  expect(response.status()).toBe(200);

  const responseBody = await response.json();
  expect(responseBody.status).toBe("OK");
  expect(responseBody.result).not.toBeNull();
  expect(responseBody.pagination.perPage).toBe(10);
});
