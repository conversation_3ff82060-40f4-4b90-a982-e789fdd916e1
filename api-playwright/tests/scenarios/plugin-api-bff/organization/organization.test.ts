/* eslint-disable @typescript-eslint/no-unused-vars */
import { test, expect } from "@playwright/test";
import { createPageInstances } from "../../../utils/helper";
import * as dotenv from "dotenv";

const env = process.env.TESTENV || "dev";

test("API test - Verify the success of GET organization request @apitest @plugin_api_bff", async ({
  request,
}) => {
  const { getURL, getLoginToken } = createPageInstances();
  const accessToken = await getLoginToken.getPluginAccessToken(request);

  const baseURL: string =
    getURL.returnURL("api-bff-" + env) + `/v1/plugin/organization`;

  const requestOptions = {
    headers: {
      "Content-Type": "application/json",
      Authorization: "Bearer " + accessToken,
    },
  };
  const response = await request.get(baseURL, requestOptions);

  const responseBody = await response.json();

  const fistResult = responseBody.result[0];

  expect(response.status()).toBe(200);

  expect(fistResult).not.toBeNull();
  expect(fistResult.id).not.toBeNull();
  expect(fistResult.name).not.toBeNull();
  expect(fistResult.associatedWorkspaces).not.toBeNull();
  expect(fistResult.redirectWorkspaceId).not.toBeNull();
  expect(responseBody.pagination).not.toBeNull();
});

test("API test - negative GET organization request with invalid token @apitest @plugin_api_bff", async ({
  request,
}) => {
  const { getURL } = createPageInstances();
  const accessToken = "invalidToken";

  const baseURL: string =
    getURL.returnURL("api-bff-" + env) + `/v1/plugin/organization`;

  const requestOptions = {
    headers: {
      "Content-Type": "application/json",
      Authorization: "Bearer " + accessToken,
    },
  };
  const response = await request.get(baseURL, requestOptions);

  const responseBody = await response.json();

  expect(response.status()).toBe(403);
  expect(responseBody.message).toBe("Forbidden resource");
  expect(responseBody.error).toBe("Forbidden");
});
