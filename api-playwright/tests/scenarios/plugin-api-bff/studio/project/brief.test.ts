/* eslint-disable @typescript-eslint/no-unused-vars */
import { test, expect } from "@playwright/test";
import { createPageInstances } from "../../../../utils/helper";
import * as workspace from "../../../../feeder/plugin-workspace.json";
import * as dotenv from "dotenv";

const env = process.env.TESTENV || "dev";

test("API test - Verify the success of GET project-brief request @apitest @plugin_api_bff", async ({
  request,
}) => {
  test.skip(env === "prod", "Skipping API isolated tests in prod - requires test data not available in prod");
  const { getURL, getLoginToken } = createPageInstances();
  const accessToken = await getLoginToken.getPluginAccessToken(request);

  const projectId = workspace[env].projectId;

  const baseURL: string =
    getURL.returnURL("api-bff-" + env) +
    `/v1/plugin/studio/project/${projectId}/brief`;

  const requestOptions = {
    headers: {
      "Content-Type": "application/json",
      Authorization: "Bearer " + accessToken,
    },
  };

  const response = await request.get(baseURL, requestOptions);
  const responseBody = await response.json();

  const firstQuestionAnswer = responseBody.result.questionAnswers[0];

  expect(response.status()).toBe(200);
  expect(responseBody.status).toBe("OK");
  expect(responseBody.result).not.toBeNull();

  expect(responseBody.result).toHaveProperty("questionAnswers");
  expect(firstQuestionAnswer).toHaveProperty("id");
  expect(firstQuestionAnswer).toHaveProperty("topic");
  expect(firstQuestionAnswer).toHaveProperty("text");
  expect(firstQuestionAnswer).toHaveProperty("lastUpdatedDate");
  expect(firstQuestionAnswer).toHaveProperty("user");
  expect(firstQuestionAnswer).toHaveProperty("questionType");
  expect(firstQuestionAnswer).toHaveProperty("answer");
  expect(firstQuestionAnswer).toHaveProperty("answers");
  expect(firstQuestionAnswer).toHaveProperty("allowAttachments");
  expect(firstQuestionAnswer).toHaveProperty("media");

  const projectBrief = responseBody.result.projectBrief[0];

  expect(projectBrief).toHaveProperty("id");
  expect(projectBrief).toHaveProperty("projectId");
  expect(projectBrief).toHaveProperty("description");
  expect(projectBrief).toHaveProperty("documentType");
  expect(projectBrief).toHaveProperty("dateCreated");
  expect(projectBrief).toHaveProperty("lastUpdated");
  expect(projectBrief).toHaveProperty("media");
});

test("API test - negative project-brief request invalid accessToken @apitest @plugin_api_bff", async ({
  request,
}) => {
  test.skip(env === "prod", "Skipping API isolated tests in prod - requires test data not available in prod");
  const { getURL } = createPageInstances();
  const accessToken = "invalidToken";

  const projectId = workspace[env].projectId;

  const baseURL: string =
    getURL.returnURL("api-bff-" + env) +
    `/v1/plugin/studio/project/${projectId}/brief`;

  const requestOptions = {
    headers: {
      "Content-Type": "application/json",
      Authorization: "Bearer " + accessToken,
    },
  };

  const response = await request.get(baseURL, requestOptions);
  const responseBody = await response.json();

  expect(response.status()).toBe(403);
  expect(responseBody.error).toBe("Forbidden");
  expect(responseBody.message).toBe("Forbidden resource");
});
