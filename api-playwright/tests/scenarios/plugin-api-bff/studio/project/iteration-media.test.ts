/* eslint-disable @typescript-eslint/no-unused-vars */
import { test, expect } from "@playwright/test";
import { createPageInstances } from "../../../../utils/helper";
import * as workspace from "../../../../feeder/plugin-workspace.json";
import * as dotenv from "dotenv";

dotenv.config();

const env = process.env.TESTENV || "dev";

const outputVideoId = workspace[env].outputVideoId;

test("API test - Verify the success of get iteration media @apitest @plugin_api_bff", async ({
  request,
}) => {
  test.skip(env === "prod", "Skipping API isolated tests in prod - requires test data not available in prod");
  const { getURL, getLoginToken } = createPageInstances();
  const accessToken = await getLoginToken.getPluginAccessToken(request);

  const baseURL =
    getURL.returnURL("api-bff-" + env) +
    `/v1/plugin/studio/project/outputVideo/${outputVideoId}/iterationMedia`;

  const requestOptions = {
    headers: {
      "Content-Type": "application/json",
      Authorization: "Bearer " + accessToken,
    },
  };

  const response = await request.get(baseURL, requestOptions);

  const responseBody = await response.json();

  expect(response.status()).toBe(200);
  expect(responseBody).toHaveProperty("status");
  expect(responseBody.status).toBe("OK");
  expect(responseBody).toHaveProperty("result");

  const iterationMedia = responseBody.result.iterationMedia[0];
  expect(iterationMedia).toHaveProperty("id");
  expect(iterationMedia).toHaveProperty("mediaId");
  expect(iterationMedia).toHaveProperty("scorecardId");
  expect(iterationMedia).toHaveProperty("url");
  expect(iterationMedia).toHaveProperty("accepted");
  expect(iterationMedia).toHaveProperty("name");
  expect(iterationMedia).toHaveProperty("score");
  expect(iterationMedia).toHaveProperty("iteration");
  expect(iterationMedia).toHaveProperty("iterationVersion");
  expect(iterationMedia).toHaveProperty("isFeedbackComplete");
  expect(iterationMedia).toHaveProperty("scorecardStatus");
  expect(iterationMedia).toHaveProperty("mediaProcessingState");
  expect(iterationMedia).toHaveProperty("fileType");
  expect(iterationMedia).toHaveProperty("unreadAnnotationCount");
});

test("API test - Negative iteration media request with invalid accessToken @apitest @plugin_api_bff", async ({
  request,
}) => {
  test.skip(env === "prod", "Skipping API isolated tests in prod - requires test data not available in prod");
  const { getURL } = createPageInstances();
  const accessToken = "invalidToken";
  const baseURL =
    getURL.returnURL("api-bff-" + env) +
    `/v1/plugin/studio/project/outputVideo/${outputVideoId}/iterationMedia`;

  const requestOptions = {
    headers: {
      "Content-Type": "application/json",
      Authorization: "Bearer " + accessToken,
    },
  };

  const response = await request.get(baseURL, requestOptions);

  const responseBody = await response.json();

  expect(response.status()).toBe(403);
  expect(responseBody.error).toBe("Forbidden");
  expect(responseBody.message).toBe("Forbidden resource");
});
