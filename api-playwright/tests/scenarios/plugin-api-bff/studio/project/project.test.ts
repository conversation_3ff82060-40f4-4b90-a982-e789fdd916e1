/* eslint-disable @typescript-eslint/no-unused-vars */
import { test, expect } from "@playwright/test";
import { createPageInstances } from "../../../../utils/helper";
import * as dotenv from "dotenv";

const env = process.env.TESTENV || "dev";

test("API test - Verify the success of get editor projects @apitest @plugin_api_bff", async ({
  request,
}) => {
  test.skip(env === "prod", "Skipping API isolated tests in prod - requires test data not available in prod");
  const { getURL, getLoginToken } = createPageInstances();
  const accessToken = await getLoginToken.getPluginAccessToken(request);

  const baseURL =
    getURL.returnURL("api-bff-" + env) + `/v1/plugin/studio/project`;

  const requestOptions = {
    headers: {
      "Content-Type": "application/json",
      Authorization: "Bearer " + accessToken,
    },
  };

  const response = await request.get(baseURL, requestOptions);

  const responseBody = await response.json();

  expect(response.status()).toBe(200);
  expect(responseBody).toHaveProperty("status");
  expect(responseBody).toHaveProperty("result");
  expect(responseBody).toHaveProperty("pagination");

  const project = responseBody.result[0];
  expect(project).toHaveProperty("id");
  expect(project).toHaveProperty("name");
  expect(project).toHaveProperty("status");
  expect(project).toHaveProperty("workspaceId");
  expect(project).toHaveProperty("draftReview");
  expect(project).toHaveProperty("scoringEnabled");
  expect(project).toHaveProperty("milestoneIdentifier");
  expect(project).toHaveProperty("milestoneText");
  expect(project).toHaveProperty("projectPreviewMedia");
  expect(project).toHaveProperty("previewMediaDownloadUrl");
  expect(project).toHaveProperty("projectThumbnailUrl");

  const pagination = responseBody.pagination;
  expect(pagination).toHaveProperty("offset");
  expect(pagination).toHaveProperty("perPage");
  expect(pagination).toHaveProperty("nextOffset");
  expect(pagination).toHaveProperty("totalSize");
});

test("API test - negative project request invalid accessToken @apitest @plugin_api_bff", async ({
  request,
}) => {
  test.skip(env === "prod", "Skipping API isolated tests in prod - requires test data not available in prod");
  const { getURL } = createPageInstances();
  const accessToken = "invalidToken";

  const baseURL =
    getURL.returnURL("api-bff-" + env) + `/v1/plugin/studio/project`;

  const requestOptions = {
    headers: {
      "Content-Type": "application/json",
      Authorization: "Bearer " + accessToken,
    },
  };

  const response = await request.get(baseURL, requestOptions);

  const responseBody = await response.json();

  expect(response.status()).toBe(403);
  expect(responseBody.error).toBe("Forbidden");
  expect(responseBody.message).toContain("Forbidden resource");
});
