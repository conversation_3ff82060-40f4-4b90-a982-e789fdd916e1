/* eslint-disable @typescript-eslint/no-unused-vars */
import { test, expect } from "@playwright/test";
import { createPageInstances } from "../../../../utils/helper";
import * as workspace from "../../../../feeder/plugin-workspace.json";
import * as dotenv from "dotenv";

const env = process.env.TESTENV || "dev";

const iterationMediaId = workspace[env].iterationMediaId;

test("API test - Verify the success of get projects annotation @apitest @plugin_api_bff", async ({
  request,
}) => {
  test.skip(env === "prod", "Skipping API isolated tests in prod - requires test data not available in prod");
  const { getURL, getLoginToken } = createPageInstances();
  const accessToken = await getLoginToken.getPluginAccessToken(request);

  const baseURL =
    getURL.returnURL("api-bff-" + env) +
    `/v1/plugin/studio/project/iteration/${iterationMediaId}/annotation`;

  const requestOptions = {
    headers: {
      "Content-Type": "application/json",
      Authorization: "Bearer " + accessToken,
    },
  };

  const response = await request.get(baseURL, requestOptions);

  const responseBody = await response.json();

  expect(response.status()).toBe(200);
  expect(responseBody).toHaveProperty("status");
  expect(responseBody).toHaveProperty("result");

  const projectAnnotation = responseBody.result.annotations[0];

  expect(projectAnnotation).toHaveProperty("id");
  expect(projectAnnotation).toHaveProperty("text");
  expect(projectAnnotation).toHaveProperty("visibility");
  expect(projectAnnotation).toHaveProperty("contextType");
  expect(projectAnnotation).toHaveProperty("contextSubType");
  expect(projectAnnotation).toHaveProperty("unread");
  expect(projectAnnotation).toHaveProperty("person");
  expect(projectAnnotation).toHaveProperty("dateCreated");
  expect(projectAnnotation).toHaveProperty("lastUpdated");
  expect(projectAnnotation).toHaveProperty("scope");
  expect(projectAnnotation).toHaveProperty("annotationSet");
  expect(projectAnnotation).toHaveProperty("annotationType");
  expect(projectAnnotation).toHaveProperty("checked");
  expect(projectAnnotation).toHaveProperty("attachmentIds");
  expect(projectAnnotation).toHaveProperty("attachments");
  expect(projectAnnotation).toHaveProperty("mediaClip");
});

test("API test - negative project annotation request invalid accessToken @apitest @plugin_api_bff", async ({
  request,
}) => {
  test.skip(env === "prod", "Skipping API isolated tests in prod - requires test data not available in prod");
  const { getURL } = createPageInstances();
  const accessToken = "invalidToken";
  const baseURL =
    getURL.returnURL("api-bff-" + env) +
    `/v1/plugin/studio/project/iteration/${iterationMediaId}/annotation`;
  const requestOptions = {
    headers: {
      "Content-Type": "application/json",
      Authorization: "Bearer " + accessToken,
    },
  };
  const response = await request.get(baseURL, requestOptions);

  const responseBody = await response.json();

  expect(response.status()).toBe(403);
  expect(responseBody.error).toBe("Forbidden");
  expect(responseBody.message).toBe("Forbidden resource");
});
