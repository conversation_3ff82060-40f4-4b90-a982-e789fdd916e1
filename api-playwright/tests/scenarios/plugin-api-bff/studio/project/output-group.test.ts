/* eslint-disable @typescript-eslint/no-unused-vars */
import { test, expect } from "@playwright/test";
import { createPageInstances } from "../../../../utils/helper";
import * as workspace from "../../../../feeder/plugin-workspace.json";
import * as dotenv from "dotenv";

const env = process.env.TESTENV || "dev";

const projectId = workspace[env].projectId;

test("API test - Verify the success of get output groups @apitest @plugin_api_bff", async ({
  request,
}) => {
  test.skip(env === "prod", "Skipping API isolated tests in prod - requires test data not available in prod");
  const { getURL, getLoginToken } = createPageInstances();
  const accessToken = await getLoginToken.getPluginAccessToken(request);

  const baseURL =
    getURL.returnURL("api-bff-" + env) +
    `/v1/plugin/studio/project/${projectId}/outputgroup`;

  const requestOptions = {
    headers: {
      "Content-Type": "application/json",
      Authorization: "Bearer " + accessToken,
    },
  };

  const response = await request.get(baseURL, requestOptions);

  const responseBody = await response.json();

  expect(response.status()).toBe(200);
  expect(responseBody).toHaveProperty("status");
  expect(responseBody.status).toBe("OK");
  expect(responseBody).toHaveProperty("result");
  expect(responseBody.result).toHaveProperty("outputGroups");

  const outputGroup = responseBody.result.outputGroups[0];

  expect(outputGroup).toHaveProperty("id");
  expect(outputGroup).toHaveProperty("name");
  expect(outputGroup).toHaveProperty("outputGroupType");
  expect(outputGroup).toHaveProperty("outputVideos");

  const outputVideo = outputGroup.outputVideos[0];

  expect(outputVideo).toHaveProperty("id");
  expect(outputVideo).toHaveProperty("outputType");
  expect(outputVideo).toHaveProperty("name");
  expect(outputVideo).toHaveProperty("mediaType");
  expect(outputVideo).toHaveProperty("aspectRatio");
  expect(outputVideo).toHaveProperty("formatDescrition");
  expect(outputVideo).toHaveProperty("url");
  expect(outputVideo).toHaveProperty("placement");
  expect(outputVideo).toHaveProperty("channel");
  expect(outputVideo).toHaveProperty("variationType");
  expect(outputVideo).toHaveProperty("currentIteration");
  expect(outputVideo).toHaveProperty("totalUnreadAnnotations");
});

test("API test - negative output groups request invalid accessToken @apitest @plugin_api_bff", async ({
  request,
}) => {
  test.skip(env === "prod", "Skipping API isolated tests in prod - requires test data not available in prod");
  const { getURL } = createPageInstances();
  const accessToken = "invalidToken";
  const baseURL =
    getURL.returnURL("api-bff-" + env) +
    `/v1/plugin/studio/project/${projectId}/outputgroup`;
  const requestOptions = {
    headers: {
      "Content-Type": "application/json",
      Authorization: "Bearer " + accessToken,
    },
  };
  const response = await request.get(baseURL, requestOptions);

  const responseBody = await response.json();

  expect(response.status()).toBe(403);
  expect(responseBody.error).toBe("Forbidden");
  expect(responseBody.message).toBe("Forbidden resource");
});
