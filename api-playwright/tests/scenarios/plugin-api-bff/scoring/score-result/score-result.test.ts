/* eslint-disable @typescript-eslint/no-unused-vars */
import { test, expect } from "@playwright/test";
import { createPageInstances } from "../../../../utils/helper";
import * as workspace from "../../../../feeder/plugin-workspace.json";
import * as dotenv from "dotenv";

const env = process.env.TESTENV || "dev";

test("API test - Verify the success of GET score-result request @apitest @plugin_api_bff", async ({
  request,
}) => {
  test.skip(env === "prod", "Skipping score-result test in prod - requires test data not available in prod");
  const { getURL, getLoginToken } = createPageInstances();
  const accessToken = await getLoginToken.getPluginAccessToken(request);

  const workspaceId = workspace[env].workspaceIds;
  const mediaId = workspace[env].mediaId;
  const scorecardId = workspace[env].scorecardId;

  const baseURL: string =
    getURL.returnURL("api-bff-" + env) +
    `/v1/plugin/scoring/workspace/${workspaceId}/media/${mediaId}/scoreResult?scorecardId=${scorecardId}`;

  const requestOptions = {
    headers: {
      "Content-Type": "application/json",
      Authorization: "Bearer " + accessToken,
    },
  };
  const response = await request.get(baseURL, requestOptions);

  const responseBody = await response.json();

  const expectedKeys = [
    "scorePercentage",
    "platform",
    "mediaProcessingState",
    "mediaUrl",
    "scores",
  ];

  const actualKeys = Object.keys(responseBody.result);

  expectedKeys.forEach((key) => {
    expect(actualKeys.includes(key)).toBe(true);
  });

  expect(actualKeys.length).toBe(expectedKeys.length);

  expect(response.status()).toBe(200);
  expect(responseBody.status).toBe("OK");
  expect(responseBody.result).not.toBeNull();
});

type getScoreResult = {
  mediaId: string | number;
  scorecardId: string;
  testScenario: string;
};

const notFound: getScoreResult[] = [];

notFound.push({
  mediaId: "0",
  scorecardId: workspace[env].scorecardId,
  testScenario: "Score result with an invalid mediaId and a valid scorecardId",
});
notFound.push({
  mediaId: workspace[env].mediaId,
  scorecardId: "0",
  testScenario: "Score result with an valid mediaId and an invalid scorecardId",
});
notFound.push({
  mediaId: "0",
  scorecardId: "0",
  testScenario:
    "Score result with an invalid mediaId and an invalid scorecardId",
});

notFound.forEach((scenario) => {
  test(
    "API test - negative " +
      scenario.testScenario +
      " @apitest @plugin_api_bff",
    async ({ request }) => {
      test.skip(env === "prod", "Skipping score-result test in prod - requires test data not available in prod");
      const { getURL, getLoginToken } = createPageInstances();
      const accessToken = await getLoginToken.getPluginAccessToken(request);
      const workspaceId = workspace[env].workspaceIds;

      const baseURL: string =
        getURL.returnURL("api-bff-" + env) +
        `/v1/plugin/scoring/workspace/${workspaceId}/media/${scenario.mediaId}/scoreResult?scorecardId=${scenario.scorecardId}`;

      const requestOptions = {
        headers: {
          "Content-Type": "application/json",
          Authorization: "Bearer " + accessToken,
        },
      };
      const response = await request.get(baseURL, requestOptions);
      console.log(response);

      expect(response.status()).toBe(404);
    }
  );
});

test("API test - negative GET score-result request with invalid workspace @apitest @plugin_api_bff", async ({
  request,
}) => {
  test.skip(env === "prod", "Skipping score-result test in prod - requires test data not available in prod");
  const { getURL, getLoginToken } = createPageInstances();
  const accessToken = await getLoginToken.getPluginAccessToken(request);

  const baseURL: string =
    getURL.returnURL("api-bff-" + env) +
    `/v1/plugin/scoring/workspace/0/media/0/scoreResult?scorecardId=0`;

  const requestOptions = {
    headers: {
      "Content-Type": "application/json",
      Authorization: "Bearer " + accessToken,
    },
  };
  const response = await request.get(baseURL, requestOptions);

  const responseBody = await response.json();

  expect(response.status()).toBe(403);

  expect(responseBody.message).toBe("Forbidden resource");
  expect(responseBody.error).toBe("Forbidden");
});
