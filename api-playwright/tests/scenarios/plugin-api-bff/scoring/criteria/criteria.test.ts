/* eslint-disable @typescript-eslint/no-unused-vars */
import { test, expect } from "@playwright/test";
import { createPageInstances } from "../../../../utils/helper";
import * as workspace from "../../../../feeder/plugin-workspace.json";
import * as dotenv from "dotenv";

const env = process.env.TESTENV || "dev";

test("API test - Verify the success of GET criteria @apitest @plugin_api_bff", async ({
  request,
}) => {
  const { getURL, getLoginToken } = createPageInstances();
  const accessToken = await getLoginToken.getPluginAccessToken(request);

  const workspaceId = workspace[env].workspaceIds;

  const baseURL: string =
    getURL.returnURL("api-bff-" + env) +
    `/v1/plugin/scoring/criteria?workspaceId=${workspaceId}`;

  const requestOptions = {
    headers: {
      "Content-Type": "application/json",
      Authorization: "Bearer " + accessToken,
    },
  };
  const response = await request.get(baseURL, requestOptions);

  const responseBody = await response.json();

  expect(response.status()).toBe(200);
  expect(responseBody.status).toBe("OK");
  expect(responseBody.result.criteria).not.toBeNull();
  expect(responseBody.pagination).not.toBeNull();
});

test("API test - negative criteria request not allowed workspace @apitest @plugin_api_bff", async ({
  request,
}) => {
  const { getURL, getLoginToken } = createPageInstances();
  const accessToken = await getLoginToken.getPluginAccessToken(request);

  const workspaceId = 0;

  const baseURL: string =
    getURL.returnURL("api-bff-" + env) +
    `/v1/plugin/scoring/criteria?workspaceId=${workspaceId}`;

  const requestOptions = {
    headers: {
      "Content-Type": "application/json",
      Authorization: "Bearer " + accessToken,
    },
  };
  const response = await request.get(baseURL, requestOptions);

  const responseBody = await response.json();

  expect(response.status()).toBe(403);
  expect(responseBody.status).toBe("ERROR");
  expect(responseBody.error.message).toContain(
    "User does not have the correct permissions to get list of criteria for workspace 0"
  );
});

test("API test - negative criteria request invalid accessToken @apitest @plugin_api_bff", async ({
  request,
}) => {
  const { getURL } = createPageInstances();
  const accessToken = "invalidToken";

  const workspaceId = workspace[env].workspaceIds;

  const baseURL: string =
    getURL.returnURL("api-bff-" + env) +
    `/v1/plugin/scoring/criteria?workspaceId=${workspaceId}`;

  const requestOptions = {
    headers: {
      "Content-Type": "application/json",
      Authorization: "Bearer " + accessToken,
    },
  };

  const response = await request.get(baseURL, requestOptions);

  const responseBody = await response.json();

  expect(response.status()).toBe(403);
  expect(responseBody.error).toBe("Forbidden");
  expect(responseBody.message).toContain("Forbidden resource");
});
