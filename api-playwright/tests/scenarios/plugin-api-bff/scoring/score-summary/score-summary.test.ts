/* eslint-disable @typescript-eslint/no-unused-vars */
import { test, expect } from "@playwright/test";
import { createPageInstances } from "../../../../utils/helper";
import * as workspace from "../../../../feeder/plugin-workspace.json";
import * as dotenv from "dotenv";

const env = process.env.TESTENV || "dev";

// test("API test - Verify the success of GET score-summary request @apitest @plugin_api_bff", async ({
//   request,
// }) => {
//   const { getURL, getLoginToken } = createPageInstances();
//   const accessToken = await getLoginToken.getPluginAccessToken(request);

//   const workspaceId = workspace[env].workspaceIds;

//   const baseURL: string =
//     getURL.returnURL("api-bff-" + env) +
//     `/v1/plugin/scoring/workspace/${workspaceId}/summary?perPage=1`;

//   const requestOptions = {
//     headers: {
//       "Content-Type": "application/json",
//       Authorization: "Bearer " + accessToken,
//     },
//   };

//   const response = await request.get(baseURL, requestOptions);

//   const responseBody = await response.json();

//   const expectedKeys = [
//     "scorecardStatus",
//     "scorecardId",
//     "mediaId",
//     "mediaProcessingState",
//     "mediaUrl",
//     "mediaRecognitionStatus",
//     "name",
//     "scorePercentage",
//     "channel",
//     "dateCreated",
//     "mimeType",
//     "version",
//   ];

//   const actualKeys = Object.keys(responseBody.result[0]);

//   expectedKeys.forEach((key) => {
//     expect(actualKeys.includes(key)).toBe(true);
//   });

//   expect(actualKeys.length).toBe(expectedKeys.length);

//   expect(response.status()).toBe(200);
//   expect(responseBody.status).toBe("OK");
//   expect(responseBody.result).not.toBeNull();
// });

type getScoreSummary = {
  workspaceId: string | number;
  testScenario: string;
};

const notFound: getScoreSummary[] = [];

notFound.push({
  workspaceId: "0",
  testScenario: "negative GET score-summary request with invalid workspaceId",
});

notFound.forEach((data) => {
  test(`API test - negative GET score-summary request with invalid workspaceId @apitest @plugin_api_bff`, async ({
    request,
  }) => {
    const { getURL, getLoginToken } = createPageInstances();
    const accessToken = await getLoginToken.getPluginAccessToken(request);

    const baseURL: string =
      getURL.returnURL("api-bff-" + env) +
      `/v1/plugin/scoring/workspace/${data.workspaceId}/summary?perPage=1`;

    const requestOptions = {
      headers: {
        "Content-Type": "application/json",
        Authorization: "Bearer " + accessToken,
      },
    };
    const response = await request.get(baseURL, requestOptions);

    const responseBody = await response.json();

    expect(response.status()).toBe(403);
    expect(responseBody.message).toBe("Forbidden resource");
    expect(responseBody.error).toBe("Forbidden");
  });
});
