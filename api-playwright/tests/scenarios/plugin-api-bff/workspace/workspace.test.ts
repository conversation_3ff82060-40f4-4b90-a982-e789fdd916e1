/* eslint-disable @typescript-eslint/no-unused-vars */
import { test, expect } from "@playwright/test";
import { createPageInstances } from "../../../utils/helper";
import * as workspace from "../../../feeder/plugin-workspace.json";

const env = process.env.TESTENV || "dev";

test("API test - Verify the success of GET workspace request @apitest @plugin_api_bff", async ({
  request,
}) => {
  const { getURL, getLoginToken } = createPageInstances();
  const accessToken = await getLoginToken.getPluginAccessToken(request);

  const organizationId = workspace[env].organizationId;

  const baseURL: string =
    getURL.returnURL("api-bff-" + env) +
    `/v1/plugin/organization/${organizationId}/workspace`;

  const requestOptions = {
    headers: {
      "Content-Type": "application/json",
      Authorization: "Bearer " + accessToken,
    },
  };
  const response = await request.get(baseURL, requestOptions);

  const responseBody = await response.json();
  expect(response.status()).toBe(200);
  expect(responseBody.status).toBe("OK");
  expect(responseBody.result).not.toBeNull();
  expect(responseBody.result[0].id).not.toBeNull();
  expect(responseBody.result[0].name).not.toBeNull();
});

type getOrganizationWorkspace = {
  organizationId: string;
  testScenario: string;
};

const notFound: getOrganizationWorkspace[] = [];
notFound.push({
  organizationId: workspace[env].organizationId,
  testScenario: "Workspace with an valid organizationId invalid accessToken",
});
notFound.push({
  organizationId: "invalidOrgId",
  testScenario:
    "Workspace with an invalid organization ID and an invalid access token",
});

notFound.forEach((scenario) => {
  test(
    "API test - negative " +
      scenario.testScenario +
      " @apitest @plugin_api_bff",
    async ({ request }) => {
      const { getURL } = createPageInstances();
      const accessToken = "invalidToken";
      const organizationId = scenario.organizationId;

      const baseURL: string =
        getURL.returnURL("api-bff-" + env) +
        `/v1/plugin/organization/${organizationId}/workspace`;

      const requestOptions = {
        headers: {
          "Content-Type": "application/json",
          Authorization: "Bearer " + accessToken,
        },
      };
      const response = await request.get(baseURL, requestOptions);

      const responseBody = await response.json();

      expect(response.status()).toBe(403);
      expect(responseBody.message).toBe("Forbidden resource");
      expect(responseBody.error).toBe("Forbidden");
    }
  );
});
