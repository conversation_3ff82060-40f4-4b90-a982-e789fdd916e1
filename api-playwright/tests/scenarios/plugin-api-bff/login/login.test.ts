/* eslint-disable @typescript-eslint/no-unused-vars */
import { test, expect } from "@playwright/test";
import { createPageInstances } from "../../../utils/helper";
import * as dotenv from "dotenv";

test("API test - Verify successfully login by correct param @apitest @plugin_api_bff", async ({
  request,
}) => {
  const { getURL } = createPageInstances();
  const env: string = process.env.TESTENV || "dev";
  const username: string = process.env.PLUGIN_EMAIL || "";
  const password: string = process.env.PLUGIN_PASSWORD || "";

  const baseURL: string = getURL.returnURL("api-bff-" + env) + "/v1/login";

  const response = await request.post(baseURL, {
    headers: {
      "Content-Type": "application/json",
    },
    data: {
      username: username,
      password: password,
    },
  });

  expect(response.status()).toBe(200);
  const responseBody = await response.json();

  expect(responseBody.accessToken).not.toBeNull();
  expect(responseBody.refreshToken).not.toBeNull();
  expect(responseBody.tokenType).toBe("Bearer");
  expect(responseBody.expiresIn).toBe(3600);
});

type loginScenario = {
  testScenario: string;
  username: string;
  password: string;
};

const invalidLogin: loginScenario[] = [];
const correctUser = process.env.PLUGIN_EMAIL || "";
const correctPW = process.env.PLUGIN_PASSWORD || "";

/**
 * Outline Test Scenarios
 * Error status: 400
 */
invalidLogin.push({
  username: correctUser,
  password: "error",
  testScenario: "correct user - wrong password"
});
invalidLogin.push({
  username: "user",
  password: correctPW,
  testScenario: "correct password - wrong user",
});

invalidLogin.forEach((scenario) => {
  test(
    "API test - Authentication - negative " +
      scenario.testScenario +
      "- @apitest @plugin_api_bff",
    async ({ request }) => {
      const { getURL } = createPageInstances();
      const env: string = process.env.TESTENV || "dev";

      const baseURL: string = getURL.returnURL("api-bff-" + env) + "/v1/login";

      const response = await request.post(baseURL, {
        headers: {
          "Content-Type": "application/json",
        },
        data: {
          username: scenario.username,
          password: scenario.password,
        },
      });

      expect(response.status()).toBe(400);

      const responseBody = await response.json();
      expect(responseBody.status).toBe("ERROR");
      expect(responseBody.error.identifier).toContain(
        "vidmob.api-bff.badrequestexception"
      );
      expect(responseBody.error.type).toContain("BADREQUESTEXCEPTION");
      expect(responseBody.error.system).toContain("api-bff");
    }
  );
});

const badRequest: loginScenario[] = [];

/**
 * Outline Test Scenarios
 * Error status: 400
 */
badRequest.push({
  username: correctUser,
  password: "error",
  testScenario: "correct user - correct password - extra param",
});
badRequest.push({
  username: "user",
  password: correctPW,
  testScenario: "correct password - wrong user - extra param",
});
badRequest.push({
  username: correctUser,
  password: correctPW,
  testScenario: "correct user and password - extra param",
});

badRequest.forEach((scenario) => {
  test(
    "API test - Authentication - negative - Bad Request" +
      scenario.testScenario +
      " error returned  @apitest @plugin_api_bff",
    async ({ request }) => {
      const { getURL, getLoginToken } = createPageInstances();
      const env: string = process.env.TESTENV || "dev";
      const username: string = process.env.PLUGIN_EMAIL || "";
      const password: string = process.env.PLUGIN_PASSWORD || "";

      const baseURL: string = getURL.returnURL("api-bff-" + env) + "/v1/login";
      const response = await request.post(baseURL, {
        headers: {
          "Content-Type": "application/json",
        },
        data: {
          user: scenario.username,
          password: scenario.password,
        },
      });
      expect(response.status()).toBe(400);
    }
  );
});
