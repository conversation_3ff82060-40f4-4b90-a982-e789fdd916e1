/* eslint-disable @typescript-eslint/no-unused-vars */
import { test, expect } from "@playwright/test";
import { createPageInstances } from "../../../utils/helper";
import * as dotenv from "dotenv";

test("API test - Verify successfully refresh token by correct param @apitest @plugin_api_bff", async ({
  request,
}) => {
  const { getURL, getLoginToken } = createPageInstances();
  const refreshToken = await getLoginToken.getPluginRefreshToken(request);

  const env: string = process.env.TESTENV || "dev";

  const baseURL: string =
    getURL.returnURL("api-bff-" + env) + "/v1/login/refresh-token";

  const response = await request.post(baseURL, {
    headers: {
      "Content-Type": "application/json",
    },
    data: {
      refreshToken: refreshToken,
    },
  });

  expect(response.status()).toBe(200);
  const responseBody = await response.json();

  expect(responseBody.accessToken).not.toBeNull();
  expect(responseBody.refreshToken).not.toBeNull();
  expect(responseBody.tokenType).toBe("Bearer");
  expect(responseBody.expiresIn).toBe(3600);
});

test("API test - Verify failed refresh token by incorrect param @apitest @plugin_api_bff", async ({
    request,
    }) => {
    const { getURL } = createPageInstances();
    const env: string = process.env.TESTENV || "dev";
    
    const baseURL: string =
        getURL.returnURL("api-bff-" + env) + "/v1/login/refresh-token";
    
    const response = await request.post(baseURL, {
        headers: {
        "Content-Type": "application/json",
        },
        data: {
        refreshToken: "error",
        },
    });
    
    expect(response.status()).toBe(400);
    const responseBody = await response.json();
    
    expect(responseBody.error).toBe("invalid_grant");
    expect(responseBody.errorDescription).toBe("Refresh token invalid");
    });

