import { test, expect } from "@playwright/test";
import { createPageInstances } from "../../../../utils/helper";

const env: string = process.env.TESTENV || "";

test.skip(env == "prod"); //skip on prod because it does not pass through bff

type getOrganization = {
  userId: string;
  testScenario: string;
};

const orgGET: getOrganization[] = [];

if (env === "dev") {
  orgGET.push({ userId: "10785", testScenario: "positive" });
  orgGET.push({ userId: "51629", testScenario: "positive" });
  orgGET.push({ userId: "51687", testScenario: "positive" });
  orgGET.push({ userId: "19940", testScenario: "positive" });
  orgGET.push({ userId: "19949", testScenario: "positive" });
} else if (env === "stage") {
  orgGET.push({ userId: "9377", testScenario: "positive" });
}

orgGET.forEach((scenario) => {
  test(
    "API test -" +
      scenario.testScenario +
      " user: " +
      scenario.userId +
      "- Verify redirectWorkspaceId is not empty @apiRegressionTest @apitest @organizationUser",
    async ({ request }) => {
      const { getURL } = createPageInstances();
      const baseURL: string =
        getURL.returnURL("soa-" + env) +
          "/v1/organization/user/" +
          scenario.userId || "";

      const response = await request.get(baseURL, {
        headers: {
          "vidmob-nestjs-service": "vidmob-organization-service",
        },
      });

      expect(response.status()).toBe(200);

      const responseBody = await response.json();
      expect(responseBody.result.redirectWorkspaceId).not.toBeNull();
    }
  );

  test(
    "API test -" +
      scenario.testScenario +
      " user: " +
      scenario.userId +
      " Verify all fields are not empty @apitest @organizationUser",
    async ({ request }) => {
      const { getURL } = createPageInstances();
      const baseURL: string =
        getURL.returnURL("soa-" + env) +
          "/v1/organization/user/" +
          scenario.userId || "";

      const response = await request.get(baseURL, {
        headers: {
          "vidmob-nestjs-service": "vidmob-organization-service",
        },
      });

      expect(response.status()).toBe(200);

      const responseBody = await response.json();
      expect(responseBody.result.id).not.toBeNull();
      expect(responseBody.result.name).not.toBeNull();
      expect(responseBody.result.associatedWorkspaces).not.toBeNull();
      expect(responseBody.result.redirectWorkspaceId).not.toBeNull();
    }
  );
});

const notFound: getOrganization[] = [
  { userId: "888888888", testScenario: "non existent numeric user" },
];

notFound.forEach((scenario) => {
  test(
    "API test - negative " +
      scenario.testScenario +
      " user: " +
      scenario.userId +
      "- verify Not Found error returned @apitest @organizationUser",
    async ({ request }) => {
      const { getURL } = createPageInstances();

      const baseURL: string =
        getURL.returnURL("soa-" + env) +
          "/v1/organization/user/" +
          scenario.userId || "";

      const response = await request.get(baseURL, {
        headers: {
          "vidmob-nestjs-service": "vidmob-organization-service",
        },
      });

      expect(response.status()).toBe(404);
      const responseBody = await response.json();
      expect(responseBody.status).toBe("ERROR");
      expect(responseBody.traceId).not.toBeNull();
      expect(responseBody.error.identifier).toBe(
        "vidmob.organization.notfoundexception"
      );
      expect(responseBody.error.type).toBe("NOTFOUNDEXCEPTION");
      expect(responseBody.error.system).toBe("organization");
      expect(responseBody.error.message).toBe(
        "User with ID " + scenario.userId + " does not exist"
      );
    }
  );
});

const badRequest: getOrganization[] = [
  { userId: "@$#5%ˆ&*", testScenario: "special chars only" },
  { userId: "user", testScenario: "string only" },
  { userId: "!@#29u832teat", testScenario: "misc chars" },
  { userId: "' '", testScenario: "user null" },
];

badRequest.forEach((scenario) => {
  test(
    "API test - negative " +
      scenario.testScenario +
      " user: " +
      scenario.userId +
      "- verify Bad Request error returned @apitest @organizationUser",
    async ({ request }) => {
      const { getURL } = createPageInstances();

      const baseURL: string =
        getURL.returnURL("soa-" + env) +
          "/v1/organization/user/" +
          scenario.userId || "";

      const response = await request.get(baseURL, {
        headers: {
          "vidmob-nestjs-service": "vidmob-organization-service",
        },
      });

      expect(response.status()).toBe(400);

      const responseBody = await response.json();
      expect(responseBody.status).toBe("ERROR");
      expect(responseBody.traceId).not.toBeNull();
      expect(responseBody.error.identifier).toBe(
        "vidmob.organization.badrequestexception"
      );
      expect(responseBody.error.type).toBe("BADREQUESTEXCEPTION");
      expect(responseBody.error.system).toBe("organization");
      expect(responseBody.error.message).toBe(
        "Validation failed (numeric string is expected)"
      );
    }
  );
});
