import { test, expect } from "@playwright/test";
import { createPageInstances } from "../../../../utils/helper";

const env: string = process.env.TESTENV || "";

test.skip(env === "prod"); //skip on prod because it does not pass through bff

type scenarios = {
  userId: number;
  testScenario: string;
  email: string;
  testCase: string;
  mediaId: number;
  expectedResult: boolean;
};

const SCENARIOS: scenarios[] = [];

if (env === "dev") {
  SCENARIOS.push({
    userId: 51849,
    testScenario: "positive",
    email: "<EMAIL>",
    testCase:
      "Org Admin should have access to IN-FLIGHT media @apiRegressionTest @apitest @organizationUser @organizationMediaValidate",
    mediaId: 86160,
    expectedResult: true,
  });
  SCENARIOS.push({
    userId: 21294,
    testScenario: "positive",
    email: "<EMAIL>",
    testCase:
      "Account Manager on workspace should have access to IN-FLIGHT media @apiRegressionTest @apitest @organizationUser @organizationMediaValidate",
    mediaId: 86160,
    expectedResult: true,
  });
  SCENARIOS.push({
    userId: 21200,
    testScenario: "positive",
    email: "<EMAIL>",
    testCase:
      "Project Manager on workspace should have access to IN-FLIGHT media @apiRegressionTest @apitest @organizationUser @organizationMediaValidate",
    mediaId: 86160,
    expectedResult: true,
  });
  SCENARIOS.push({
    userId: 51795,
    testScenario: "positive",
    email: "<EMAIL>",
    testCase:
      "Standard User on workspace should have access to IN-FLIGHT media @apiRegressionTest @apitest @organizationUser @organizationMediaValidate",
    mediaId: 86160,
    expectedResult: true,
  });
  SCENARIOS.push({
    userId: 517950,
    testScenario: "negative",
    email: "not real user",
    testCase:
      "User NOT on workspace should NOT have access to IN-FLIGHT media @apiRegressionTest @apitest @organizationUser @organizationMediaValidate",
    mediaId: 86160,
    expectedResult: false,
  });
  SCENARIOS.push({
    userId: 51849,
    testScenario: "positive",
    email: "<EMAIL>",
    testCase:
      "Org Admin should have access to PRE-FLIGHT media @apiRegressionTest @apitest @organizationUser @organizationMediaValidate",
    mediaId: 201259,
    expectedResult: true,
  });
  SCENARIOS.push({
    userId: 21294,
    testScenario: "positive",
    email: "<EMAIL>",
    testCase:
      "Account Manager on workspace should have access to PRE-FLIGHT media @apiRegressionTest @apitest @organizationUser @organizationMediaValidate",
    mediaId: 201259,
    expectedResult: true,
  });
  SCENARIOS.push({
    userId: 21200,
    testScenario: "positive",
    email: "<EMAIL>",
    testCase:
      "Project Manager on workspace should have access to PRE-FLIGHT media @apiRegressionTest @apitest @organizationUser @organizationMediaValidate",
    mediaId: 201259,
    expectedResult: true,
  });
  SCENARIOS.push({
    userId: 51795,
    testScenario: "positive",
    email: "<EMAIL>",
    testCase:
      "Standard User on workspace should have access to PRE-FLIGHT media @apiRegressionTest @apitest @organizationUser @organizationMediaValidate",
    mediaId: 201259,
    expectedResult: true,
  });
  SCENARIOS.push({
    userId: 517950,
    testScenario: "negative",
    email: "not real user",
    testCase:
      "User NOT on workspace should NOT have access to PRE-FLIGHT media @apiRegressionTest @apitest @organizationUser @organizationMediaValidate",
    mediaId: 201259,
    expectedResult: false,
  });
} else if (env === "stage") {
  SCENARIOS.push({
    userId: 9551,
    testScenario: "positive",
    email: "<EMAIL>",
    testCase:
      "Org Admin should have access to IN-FLIGHT media @apiRegressionTest @apitest @organizationUser @organizationMediaValidate",
    mediaId: 18535,
    expectedResult: true,
  });
  SCENARIOS.push({
    userId: 7780,
    testScenario: "positive",
    email: "<EMAIL>",
    testCase:
      "Account Manager on workspace should have access to IN-FLIGHT media @apiRegressionTest @apitest @organizationUser @organizationMediaValidate",
    mediaId: 18535,
    expectedResult: true,
  });
  SCENARIOS.push({
    userId: 9437,
    testScenario: "positive",
    email: "<EMAIL>",
    testCase:
      "Project Manager on workspace should have access to IN-FLIGHT media @apiRegressionTest @apitest @organizationUser @organizationMediaValidate",
    mediaId: 18535,
    expectedResult: true,
  });
  SCENARIOS.push({
    userId: 9525,
    testScenario: "positive",
    email: "<EMAIL>",
    testCase:
      "Standard User on workspace should have access to IN-FLIGHT media @apiRegressionTest @apitest @organizationUser @organizationMediaValidate",
    mediaId: 18535,
    expectedResult: true,
  });
  SCENARIOS.push({
    userId: 517950,
    testScenario: "negative",
    email: "not real user",
    testCase:
      "User NOT on workspace should NOT have access to IN-FLIGHT media @apiRegressionTest @apitest @organizationUser @organizationMediaValidate",
    mediaId: 18535,
    expectedResult: false,
  });
  SCENARIOS.push({
    userId: 9551,
    testScenario: "positive",
    email: "<EMAIL>",
    testCase:
      "Org Admin should have access to PRE-FLIGHT media @apiRegressionTest @apitest @organizationUser @organizationMediaValidate",
    mediaId: 188227,
    expectedResult: true,
  });
  SCENARIOS.push({
    userId: 7780,
    testScenario: "positive",
    email: "<EMAIL>",
    testCase:
      "Account Manager on workspace should have access to PRE-FLIGHT media @apiRegressionTest @apitest @organizationUser @organizationMediaValidate",
    mediaId: 188227,
    expectedResult: true,
  });
  SCENARIOS.push({
    userId: 9437,
    testScenario: "positive",
    email: "<EMAIL>",
    testCase:
      "Project Manager on workspace should have access to PRE-FLIGHT media @apiRegressionTest @apitest @organizationUser @organizationMediaValidate",
    mediaId: 188227,
    expectedResult: true,
  });
  SCENARIOS.push({
    userId: 9525,
    testScenario: "positive",
    email: "<EMAIL>",
    testCase:
      "Standard User on workspace should have access to PRE-FLIGHT media @apiRegressionTest @apitest @organizationUser @organizationMediaValidate",
    mediaId: 188227,
    expectedResult: true,
  });
  SCENARIOS.push({
    userId: 517950,
    testScenario: "negative",
    email: "not real user",
    testCase:
      "User NOT on workspace should NOT have access to PRE-FLIGHT media @apiRegressionTest @apitest @organizationUser @organizationMediaValidate",
    mediaId: 188227,
    expectedResult: false,
  });
} else if (env === "prod") {
  SCENARIOS.push({
    userId: 0,
    testScenario: "dummy test to skip production",
    email: "<EMAIL>",
    testCase:
      "dummy test to skip production@organizationUser @organizationMediaValidate",
    mediaId: 0,
    expectedResult: true,
  });
}

SCENARIOS.forEach((scenario) => {
  test(`API test - ${scenario.testScenario} user: ${scenario.userId} - ${scenario.testCase}`, async ({
    request,
  }) => {
    const { getURL } = createPageInstances();
    const baseURL: string =
      getURL.returnURL("soa" + (env === "prod" ? "" : `-${env}`)) +
      `/v1/media/${scenario.mediaId}/person/${scenario.userId}/validate`;

    const response = await request.get(baseURL, {
      headers: {
        "vidmob-nestjs-service": "vidmob-organization-service",
      },
    });

    expect(response.status()).toBe(200);

    const responseBody = await response.json();
    expect(responseBody.result.canView).toEqual(scenario.expectedResult);
  });
});
