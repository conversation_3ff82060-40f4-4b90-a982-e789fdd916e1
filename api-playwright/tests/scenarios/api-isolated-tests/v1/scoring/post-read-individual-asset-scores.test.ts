import { test, expect, APIResponse } from '@playwright/test';
import { createPageInstances } from '../../../../utils/helper';
import { API_RESPONSE_STATUS, SCORING_TAGS, TEST_TYPES } from '../../../../utils/util-constants';
import {IndividualScoreResult, SuccessResponseBody} from "../../../../utils/scoring.types";

const env = process.env.TESTENV || 'dev';

test.skip(env == "prod"); //skip on prod

const DEV_PRE_FLIGHT_MEDIA_ID = 195186;
const DEV_PRE_FLIGHT_SCORECARD_ID = 20751;
const DEV_PRE_FLIGHT_SCORECARD_CHANNELS = ['FACEBOOK'];
const DEV_IN_FLIGHT_MEDIA_ID = 134503;
const DEV_IN_FLIGHT_SCORECARD_ID = 16079;
const DEV_IN_FLIGHT_SCORECARD_CHANNELS = ['TIKTOK', 'ALL_PLATFORMS'];
const DEV_IN_FLIGHT_AD_ACCOUNT_ID = '6807608752410198022';


const STAGE_PRE_FLIGHT_MEDIA_ID = 186565;
const STAGE_PRE_FLIGHT_SCORECARD_ID = 19711;
const STAGE_PRE_FLIGHT_SCORECARD_CHANNELS = ['DV360'];
const STAGE_IN_FLIGHT_MEDIA_ID = 18495;
const STAGE_IN_FLIGHT_SCORECARD_ID = 12107;
const STAGE_IN_FLIGHT_SCORECARD_CHANNELS = ['SNAPCHAT'];
const STAGE_IN_FLIGHT_AD_ACCOUNT_ID = '79712f90-23c6-4d90-b63a-dfc49d1a21d4';

const envTestResource = {
  dev: {
    preFlight: {
      mediaId: DEV_PRE_FLIGHT_MEDIA_ID,
      scorecardId: DEV_PRE_FLIGHT_SCORECARD_ID,
      channels: DEV_PRE_FLIGHT_SCORECARD_CHANNELS,
    },
    inFlight: {
      mediaId: DEV_IN_FLIGHT_MEDIA_ID,
      scorecardId: DEV_IN_FLIGHT_SCORECARD_ID,
      adAccountId: DEV_IN_FLIGHT_AD_ACCOUNT_ID,
      channels: DEV_IN_FLIGHT_SCORECARD_CHANNELS,
    },
  },
  stage: {
    preFlight: {
      mediaId: STAGE_PRE_FLIGHT_MEDIA_ID,
      scorecardId: STAGE_PRE_FLIGHT_SCORECARD_ID,
      channels: STAGE_PRE_FLIGHT_SCORECARD_CHANNELS,
    },
    inFlight: {
      mediaId: STAGE_IN_FLIGHT_MEDIA_ID,
      scorecardId: STAGE_IN_FLIGHT_SCORECARD_ID,
      adAccountId: STAGE_IN_FLIGHT_AD_ACCOUNT_ID,
      channels: STAGE_IN_FLIGHT_SCORECARD_CHANNELS,
    },
  }
};

test.skip(env == 'prod');

const testScenarios = [
  {
    case: 'Success 200 Response - Pre-Flight',
    type: TEST_TYPES.POSITIVE,
    role: 'admin',
    scorecardType: 'preFlight',
    resources: envTestResource[env],
    verify: async (response: APIResponse) => {
      const responseBody: SuccessResponseBody = await response.json();
      const channels: string[] = envTestResource[env]['preFlight']?.channels;
      expect(response.status()).toBe(API_RESPONSE_STATUS.SUCCESS);
      Object.values(responseBody?.result.scores).forEach((score: IndividualScoreResult) => {
        expect(channels.includes(score.platformIdentifier)).toBeTruthy();
      });
    },
  },
  {
    case: 'Success 200 Response - In-Flight',
    type: TEST_TYPES.POSITIVE,
    role: 'admin',
    scorecardType: 'inFlight',
    resources: envTestResource[env],
    verify: async (response: APIResponse) => {
      const responseBody: SuccessResponseBody = await response.json();
      const channels: string[] = envTestResource[env]['inFlight']?.channels;
      expect(response.status()).toBe(API_RESPONSE_STATUS.SUCCESS);
      Object.values(responseBody?.result.scores).forEach((score: IndividualScoreResult) => {
        expect(channels.includes(score.platformIdentifier)).toBeTruthy();
      });
    },
  },
  {
    case: 'Bad Request 400 Response',
    type: TEST_TYPES.NEGATIVE,
    role: 'non-admin',
    scorecardType: 'preFlight',
    resources: {preFlight: {mediaId: 1, channels: [], batchId: 1}},
    verify: async (response: APIResponse) => {
      const responseBody: SuccessResponseBody = await response.json();
      expect(response.status()).toBe(API_RESPONSE_STATUS.BAD_REQUEST);
    },
  },
];

testScenarios.forEach((scenario) => {
  test(
    `Individual Asset Scores POST API,
    case: ${scenario.case},
    type: ${scenario.type},
    tags: @alpha @apiRegressionTest @apitest ${SCORING_TAGS.REGRESSION_TAG} ${SCORING_TAGS.SCORING_SERVICE_TAG}`,
    async ({request}) => {
      const scenarioResources = scenario.resources[scenario.scorecardType];
      const { getURL, getLoginToken } = createPageInstances();
      const accessToken = await getLoginToken.getAccessToken(request);
      const baseUrl = getURL.returnURL(`api-public-${env}`);
      const requestUrl = `${baseUrl}/as-gw/api/v1/media/contentAudit/${scenarioResources?.mediaId}`
      const requestOptions = {
        data: {
          mediaId: scenarioResources?.mediaId,
          channels: scenarioResources.channels,
          batchId: scenarioResources.scorecardId,
        },
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${accessToken}`
        }
      }

      const response = await request.post(requestUrl, requestOptions);
      await scenario.verify(response)
  });
});
