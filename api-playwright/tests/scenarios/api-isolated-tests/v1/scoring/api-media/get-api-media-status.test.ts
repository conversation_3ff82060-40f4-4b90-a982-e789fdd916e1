import { test, expect, APIResponse } from "@playwright/test";
import { createPageInstances } from "../../../../../utils/helper";
import {
  scopes,
  getApiKeyInfo,
  deleteApiKey,
} from "../../../../../utils/api-token-utils";
import { ApiKeyInfo } from "../../../../../utils/api-key.types";
import {
  API_RESPONSE_STATUS,
  COMMON_TEST_TAGS,
  SCORING_TAGS,
  TEST_TYPES,
} from "../../../../../utils/util-constants";
import {
  ErrorResponseBody,
  SuccessResponseBody,
} from "../../../../../utils/scoring.types";

const env = process.env.TESTENV || "dev";
const SUCCESS_API_MEDIA_ID = "API-PLAYWRIGHT-TEST-MEDIA";
const FAIL_API_MEDIA_ID = "garbage-dechets-basura";
const API_MEDIA_SOURCE = "playwright-test";
const API_MEDIA_VERSION = "1.0";

const resources = {
  dev: {
    mediaScoresExist: {
      mediaId: SUCCESS_API_MEDIA_ID,
      params: {
        source: API_MEDIA_SOURCE,
        version: API_MEDIA_VERSION,
      },
    },
    mediaScoresDoNotExist: {
      mediaId: FAIL_API_MEDIA_ID,
      params: {
        source: API_MEDIA_SOURCE,
      },
    },
  },
  stage: {
    mediaScoresExist: {
      mediaId: SUCCESS_API_MEDIA_ID,
      params: {
        source: API_MEDIA_SOURCE,
        version: API_MEDIA_VERSION,
      },
    },
    mediaScoresDoNotExist: {
      mediaId: FAIL_API_MEDIA_ID,
      params: {
        source: API_MEDIA_SOURCE,
      },
    },
  },
  prod: {
    mediaScoresExist: {
      mediaId: SUCCESS_API_MEDIA_ID,
      params: {
        source: API_MEDIA_SOURCE,
        version: API_MEDIA_VERSION,
      },
    },
    mediaScoresDoNotExist: {
      mediaId: FAIL_API_MEDIA_ID,
      params: {
        source: API_MEDIA_SOURCE,
      },
    },
  },
};

const testScenarios = [
  {
    case: "Success 200 Response - Media Scores Exist",
    type: TEST_TYPES.POSITIVE,
    resource: resources[env].mediaScoresExist,
    verify: async (response: APIResponse) => {
      const responseBody: SuccessResponseBody = await response.json();

      expect(response.status()).toBe(API_RESPONSE_STATUS.SUCCESS);
      expect(responseBody.result).toBeDefined();
      expect(responseBody.result.id).toBe(SUCCESS_API_MEDIA_ID);
    },
  },
  {
    case: "Not Found 404 Response - Media Scores Do Not Exist",
    type: TEST_TYPES.NEGATIVE,
    resource: resources[env].mediaScoresDoNotExist,
    verify: async (response: APIResponse) => {
      const expectedErrorMessage = `No API media record`;
      const responseBody: ErrorResponseBody = await response.json();
      expect(response.status()).toBe(API_RESPONSE_STATUS.NOT_FOUND);
      expect(responseBody.error).toBeDefined();
      expect(JSON.stringify(responseBody.error)).toContain(
        expectedErrorMessage
      );
    },
  },
];

test.describe(() => {
  testScenarios.forEach((scenario) => {
    test(`Get API Media Status API,
    case: ${scenario.case},
    type: ${scenario.type},
    tags: ${COMMON_TEST_TAGS.API_TEST} ${COMMON_TEST_TAGS.REGRESSION_TEST} ${SCORING_TAGS.REGRESSION_TAG} ${COMMON_TEST_TAGS.API_BFF_TAG}`, async ({
      request,
    }) => {
      test.skip(env === "prod", "Skipping API isolated tests in prod - requires test data not available in prod");
      test.slow();
      const { getURL } = createPageInstances();
      const apiTokenInfo: ApiKeyInfo = await getApiKeyInfo(
        request,
        scopes["scoring-ro"]
      );
      const apiToken = apiTokenInfo.apiKey;
      const apiTokenId = apiTokenInfo.id;
      const baseUrl = getURL.returnURL(`api-bff-${env}`);
      const requestUrl = `${baseUrl}/v1/media/${scenario.resource.mediaId}/status`;
      const requestOptions = {
        params: { ...scenario.resource.params },
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${apiToken}`,
        },
      };

      const response = await request.get(requestUrl, requestOptions);
      await deleteApiKey(request, apiTokenId);
      await scenario.verify(response);
    });
  });
});
