import { test, expect, APIResponse } from "@playwright/test";
import { createPageInstances } from "../../../../../utils/helper";
import {
  scopes,
  getApiKeyInfo,
  deleteApi<PERSON>ey,
} from "../../../../../utils/api-token-utils";
import { ApiKeyInfo } from "../../../../../utils/api-key.types";
import {
  API_RESPONSE_STATUS,
  COMMON_TEST_TAGS,
  SCORING_TAGS,
  TEST_TYPES,
} from "../../../../../utils/util-constants";
import {
  ErrorResponseBody,
  SuccessResponseBody,
} from "../../../../../utils/scoring.types";

const env = process.env.TESTENV || "dev";
const TEST_API_MEDIA_ID = "API-PLAYWRIGHT-TEST-MEDIA";
const TEST_API_MEDIA_SOURCE = "playwright-test";
const TEST_API_MEDIA_VERSION = "1.0";

const resources = {
  success: {
    mediaId: TEST_API_MEDIA_ID,
    params: {
      version: TEST_API_MEDIA_VERSION,
      source: TEST_API_MEDIA_SOURCE,
    },
  },
  successSingleChannel: {
    mediaId: TEST_API_MEDIA_ID,
    params: {
      version: TEST_API_MEDIA_VERSION,
      source: TEST_API_MEDIA_SOURCE,
      channel: "FACEBOOK",
    },
  },
  badMediaId: {
    mediaId: "garbage-dechets-basura",
    params: {
      version: TEST_API_MEDIA_VERSION,
      source: TEST_API_MEDIA_SOURCE,
    },
  },
  missingMediaSource: {
    mediaId: TEST_API_MEDIA_ID,
    params: {
      version: TEST_API_MEDIA_VERSION,
    },
  },
};

const testScenarios = [
  {
    case: "Success 200 Response - Score summary in response",
    type: TEST_TYPES.POSITIVE,
    resource: resources.success,
    verify: async (response: APIResponse) => {
      const responseBody: SuccessResponseBody = await response.json();
      expect(response.status()).toBe(API_RESPONSE_STATUS.SUCCESS);
      expect(responseBody.result.media).toBeDefined();
      expect(responseBody.result.summary).toBeDefined();
      expect(responseBody.result.media.id).toBe(TEST_API_MEDIA_ID);
    },
  },
  {
    case: "Success 200 Response - Score summary filtered by channel",
    type: TEST_TYPES.POSITIVE,
    resource: resources.successSingleChannel,
    verify: async (response: APIResponse) => {
      const responseBody: SuccessResponseBody = await response.json();
      expect(response.status()).toBe(API_RESPONSE_STATUS.SUCCESS);
      expect(responseBody.result.summary).toBeDefined();
      expect(responseBody.result.summary["FACEBOOK"]).toBeDefined();
      expect(Object.keys(responseBody.result.summary).length).toBe(1);
    },
  },
  {
    case: "Not Found 404 Response - Bad Media ID",
    type: TEST_TYPES.NEGATIVE,
    resource: resources.badMediaId,
    verify: async (response: APIResponse) => {
      const expectedErrorMessage = `No API media record found for media ID: ${resources.badMediaId.mediaId} with source: ${TEST_API_MEDIA_SOURCE} and version: ${TEST_API_MEDIA_VERSION}`;
      const responseBody: ErrorResponseBody = await response.json();
      expect(response.status()).toBe(API_RESPONSE_STATUS.NOT_FOUND);
      expect(JSON.stringify(responseBody)).toContain(expectedErrorMessage);
    },
  },
  {
    case: "Bad Request 404 Response - Missing Media Source",
    type: TEST_TYPES.NEGATIVE,
    resource: resources.missingMediaSource,
    verify: async (response: APIResponse) => {
      const expectedErrorMessage = `No API media record found for media ID: ${TEST_API_MEDIA_ID} with source:  and version: ${TEST_API_MEDIA_VERSION}`;
      const responseBody: ErrorResponseBody = await response.json();
      expect(response.status()).toBe(API_RESPONSE_STATUS.NOT_FOUND);
      expect(JSON.stringify(responseBody)).toContain(expectedErrorMessage);
    },
  },
];

test.describe(() => {
  testScenarios.forEach((scenario) => {
    test(`Get API Media Score API,
    case: ${scenario.case},
    type: ${scenario.type},
    tags: ${COMMON_TEST_TAGS.API_TEST} ${COMMON_TEST_TAGS.REGRESSION_TEST} ${SCORING_TAGS.REGRESSION_TAG} ${COMMON_TEST_TAGS.API_BFF_TAG}`, async ({
      request,
    }) => {
      test.skip(env === "prod", "Skipping API isolated tests in prod - requires test data not available in prod");
      const { getURL } = createPageInstances();
      const apiTokenInfo: ApiKeyInfo = await getApiKeyInfo(
        request,
        scopes["scoring-ro"]
      );
      const apiToken = apiTokenInfo.apiKey;
      const apiTokenId = apiTokenInfo.id;
      const baseUrl = getURL.returnURL(`api-bff-${env}`);
      const requestUrl = `${baseUrl}/v1/scoring/media/${scenario.resource.mediaId}/scores`;
      const requestOptions = {
        params: {
          ...scenario.resource.params,
        },
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${apiToken}`,
        },
      };

      const response = await request.get(requestUrl, requestOptions);
      await deleteApiKey(request, apiTokenId);
      await scenario.verify(response);
    });
  });
});
