import { test, expect, APIResponse } from "@playwright/test";
import { createPageInstances } from "../../../../../utils/helper";
import {
  scopes,
  getApiKeyInfo,
  deleteApiKey,
} from "../../../../../utils/api-token-utils";
import { ApiKeyInfo } from "../../../../../utils/api-key.types";
import {
  API_RESPONSE_STATUS,
  COMMON_TEST_TAGS,
  SCORING_TAGS,
  TEST_TYPES,
} from "../../../../../utils/util-constants";
import {
  ErrorResponseBody,
  SuccessResponseBody,
} from "../../../../../utils/scoring.types";

const env = process.env.TESTENV || "dev";

const DEV_WORKSPACE_ID = 23142;
const STAGE_WORKSPACE_ID = 2735;
const PROD_WORKSPACE_ID = 36470;
const START_DATE = "2024-01-01";
const END_DATE = "2024-03-01";
const TEST_CHANNEL = "FACEBOOK";
const TEST_DEV_BRAND = "3a98dd8d-9428-4509-a2b0-dfcb9ad86b9d";
const TEST_STAGE_BRAND = "090c8998-1f16-4f8a-9b26-bc7ac9457ccc";
const TEST_PROD_BRAND = "09613d1c-b6e6-4757-abde-37f846b42c69";
const TEST_MARKET = "usa";

const testBrands = {
  dev: {
    brandId: TEST_DEV_BRAND,
  },
  stage: {
    brandId: TEST_STAGE_BRAND,
  },
  prod: {
    brandId: TEST_PROD_BRAND,
  },
};

const resources = {
  dev: {
    getScorecards: {
      workspaceId: DEV_WORKSPACE_ID,
      params: {
        types: "PRE_FLIGHT",
        startDate: START_DATE,
        endDate: END_DATE,
      },
    },
    getScorecardsWithDetails: {
      workspaceId: DEV_WORKSPACE_ID,
      params: {
        types: "PRE_FLIGHT",
        startDate: START_DATE,
        endDate: END_DATE,
        scoreDetail: true,
      },
    },
    getScorecardsFilteredByChannels: {
      workspaceId: DEV_WORKSPACE_ID,
      params: {
        types: "IN_FLIGHT",
        channels: TEST_CHANNEL,
        startDate: START_DATE,
        endDate: END_DATE,
      },
    },
    getScorecardsFilteredByBrands: {
      workspaceId: DEV_WORKSPACE_ID,
      params: {
        types: "PRE_FLIGHT",
        startDate: START_DATE,
        endDate: END_DATE,
        brands: testBrands[env].brandId,
      },
    },
    getScorecardsFilteredByMarkets: {
      workspaceId: DEV_WORKSPACE_ID,
      params: {
        types: "PRE_FLIGHT",
        startDate: START_DATE,
        endDate: END_DATE,
        markets: TEST_MARKET,
      },
    },
    getScorecardsFilteredByDate: {
      workspaceId: DEV_WORKSPACE_ID,
      params: {
        types: "PRE_FLIGHT",
        startDate: START_DATE,
        endDate: END_DATE,
        markets: TEST_MARKET,
      },
    },
    missingScorecardType: {
      workspaceId: DEV_WORKSPACE_ID,
      params: {
        startDate: START_DATE,
        endDate: END_DATE,
        markets: TEST_MARKET,
      },
    },
  },
  stage: {
    getScorecards: {
      workspaceId: STAGE_WORKSPACE_ID,
      params: {
        types: "PRE_FLIGHT",
        startDate: START_DATE,
        endDate: END_DATE,
      },
    },
    getScorecardsWithDetails: {
      workspaceId: STAGE_WORKSPACE_ID,
      params: {
        types: "PRE_FLIGHT",
        startDate: START_DATE,
        endDate: END_DATE,
        scoreDetail: true,
      },
    },
    getScorecardsFilteredByChannels: {
      workspaceId: STAGE_WORKSPACE_ID,
      params: {
        types: "IN_FLIGHT",
        channels: TEST_CHANNEL,
        startDate: START_DATE,
        endDate: END_DATE,
      },
    },
    getScorecardsFilteredByBrands: {
      workspaceId: STAGE_WORKSPACE_ID,
      params: {
        types: "PRE_FLIGHT",
        startDate: START_DATE,
        endDate: END_DATE,
        brands: testBrands[env].brandId,
      },
    },
    getScorecardsFilteredByMarkets: {
      workspaceId: STAGE_WORKSPACE_ID,
      params: {
        types: "PRE_FLIGHT",
        startDate: START_DATE,
        endDate: END_DATE,
        markets: TEST_MARKET,
      },
    },
    getScorecardsFilteredByDate: {
      workspaceId: STAGE_WORKSPACE_ID,
      params: {
        types: "PRE_FLIGHT",
        startDate: START_DATE,
        endDate: END_DATE,
        markets: TEST_MARKET,
      },
    },
    missingScorecardType: {
      workspaceId: STAGE_WORKSPACE_ID,
      params: {
        startDate: START_DATE,
        endDate: END_DATE,
        markets: TEST_MARKET,
      },
    },
  },
  prod: {
    getScorecards: {
      workspaceId: PROD_WORKSPACE_ID,
      params: {
        types: "PRE_FLIGHT",
        startDate: START_DATE,
        endDate: END_DATE,
      },
    },
    getScorecardsWithDetails: {
      workspaceId: PROD_WORKSPACE_ID,
      params: {
        types: "PRE_FLIGHT",
        startDate: START_DATE,
        endDate: END_DATE,
        scoreDetail: true,
      },
    },
    getScorecardsFilteredByChannels: {
      workspaceId: PROD_WORKSPACE_ID,
      params: {
        types: "PRE_FLIGHT",
        channels: TEST_CHANNEL,
        startDate: START_DATE,
        endDate: END_DATE,
      },
    },
    getScorecardsFilteredByBrands: {
      workspaceId: PROD_WORKSPACE_ID,
      params: {
        types: "PRE_FLIGHT",
        startDate: START_DATE,
        endDate: END_DATE,
        brands: testBrands[env].brandId,
      },
    },
    getScorecardsFilteredByMarkets: {
      workspaceId: PROD_WORKSPACE_ID,
      params: {
        types: "PRE_FLIGHT",
        startDate: START_DATE,
        endDate: END_DATE,
        markets: TEST_MARKET,
      },
    },
    getScorecardsFilteredByDate: {
      workspaceId: PROD_WORKSPACE_ID,
      params: {
        types: "PRE_FLIGHT",
        startDate: START_DATE,
        endDate: END_DATE,
        markets: TEST_MARKET,
      },
    },
    missingScorecardType: {
      workspaceId: PROD_WORKSPACE_ID,
      params: {
        startDate: START_DATE,
        endDate: END_DATE,
        markets: TEST_MARKET,
      },
    },
  },
};

const testScenarios = [
  {
    case: "Success 200 Response - Get Workspace Scorecards",
    type: TEST_TYPES.POSITIVE,
    resource: resources[env].getScorecards,
    verify: async (response: APIResponse) => {
      const scorecardTypes = new Set<string>();
      const responseBody: SuccessResponseBody = await response.json();
      expect(response.status()).toBe(API_RESPONSE_STATUS.SUCCESS);
      expect(responseBody.pagination).toBeDefined();
      expect(responseBody.result).toBeDefined();
      expect(responseBody.result).toBeInstanceOf(Array);

      responseBody.result.forEach((scorecard: any) => {
        scorecardTypes.add(scorecard.type);
      });

      expect(scorecardTypes.size).toBe(1);
    },
  },
  {
    case: "Success 200 Response - Get Workspace Scorecards with Score Details",
    type: TEST_TYPES.POSITIVE,
    resource: resources[env].getScorecardsWithDetails,
    verify: async (response: APIResponse) => {
      const responseBody: SuccessResponseBody = await response.json();
      expect(response.status()).toBe(API_RESPONSE_STATUS.SUCCESS);
      expect(responseBody.pagination).toBeDefined();
      expect(responseBody.result).toBeDefined();
      expect(responseBody.result).toBeInstanceOf(Array);

      responseBody.result.forEach((scorecard: any) => {
        expect(scorecard.scoreDetail).toBeDefined();
      });
    },
  },
  {
    case: "Success 200 Response - Get Workspace Scorecards Filtered By Channels",
    type: TEST_TYPES.POSITIVE,
    resource: resources[env].getScorecardsFilteredByChannels,
    verify: async (response: APIResponse) => {
      const channels = new Set<string>();
      const responseBody: SuccessResponseBody = await response.json();
      expect(response.status()).toBe(API_RESPONSE_STATUS.SUCCESS);
      expect(responseBody.pagination).toBeDefined();
      expect(responseBody.result).toBeDefined();
      expect(responseBody.result).toBeInstanceOf(Array);

      responseBody.result.forEach((scorecard: any) => {
        scorecard.channels.forEach((channel: string) => {
          channels.add(channel);
        });
      });

      expect(channels.size).toBe(1);
      expect(channels.has(TEST_CHANNEL)).toBe(true);
    },
  },
  {
    case: "Success 200 Response - Get Workspace Scorecards Filtered By Brands",
    type: TEST_TYPES.POSITIVE,
    resource: resources[env].getScorecardsFilteredByBrands,
    verify: async (response: APIResponse) => {
      const brands = new Set<string>();
      const responseBody: SuccessResponseBody = await response.json();
      expect(response.status()).toBe(API_RESPONSE_STATUS.SUCCESS);
      expect(responseBody.pagination).toBeDefined();
      expect(responseBody.result).toBeDefined();
      expect(responseBody.result).toBeInstanceOf(Array);

      responseBody.result.forEach((scorecard: any) => {
        scorecard.brands.forEach((brand: { id: string; name: string }) => {
          brands.add(brand.id);
        });
      });

      expect(brands.size).toBeGreaterThan(0);
      expect(brands.has(testBrands[env].brandId)).toBe(true);
    },
  },
  {
    case: "Success 200 Response - Get Workspace Scorecards Filtered By Markets",
    type: TEST_TYPES.POSITIVE,
    resource: resources[env].getScorecardsFilteredByMarkets,
    verify: async (response: APIResponse) => {
      const markets = new Set<string>();
      const responseBody: SuccessResponseBody = await response.json();
      expect(response.status()).toBe(API_RESPONSE_STATUS.SUCCESS);
      expect(responseBody.pagination).toBeDefined();
      expect(responseBody.result).toBeDefined();
      expect(responseBody.result).toBeInstanceOf(Array);

      responseBody.result.forEach((scorecard: any) => {
        scorecard.markets.forEach(
          (market: { isoCode: string; name: string }) => {
            markets.add(market.isoCode);
          }
        );
      });

      expect(markets.size).toBeGreaterThan(0);
      expect(markets.has(TEST_MARKET)).toBe(true);
    },
  },
  {
    case: "Success 200 Response - Get Workspace Scorecards Filtered By Date",
    type: TEST_TYPES.POSITIVE,
    resource: resources[env].getScorecardsFilteredByDate,
    verify: async (response: APIResponse) => {
      const responseBody: SuccessResponseBody = await response.json();
      expect(response.status()).toBe(API_RESPONSE_STATUS.SUCCESS);
      expect(responseBody.pagination).toBeDefined();
      expect(responseBody.result).toBeDefined();
      expect(responseBody.result).toBeInstanceOf(Array);

      responseBody.result.forEach((scorecard: any) => {
        expect(
          new Date(scorecard.dateCreated).getTime()
        ).toBeGreaterThanOrEqual(new Date(START_DATE).getTime());
        expect(new Date(scorecard.dateCreated).getTime()).toBeLessThanOrEqual(
          new Date(END_DATE).getTime()
        );
      });
    },
  },
  {
    case: "Bad Request 400 Response - Scorecard Type Not Found",
    type: TEST_TYPES.NEGATIVE,
    resource: resources[env].missingScorecardType,
    verify: async (response: APIResponse) => {
      const expectedErrorMessage = "types must be a string";
      const responseBody: ErrorResponseBody = await response.json();
      expect(response.status()).toBe(API_RESPONSE_STATUS.BAD_REQUEST);
      expect(responseBody.error).toBeDefined();
      expect(JSON.stringify(responseBody.error)).toContain(
        expectedErrorMessage
      );
    },
  },
];

test.describe(() => {
  testScenarios.forEach((scenario) => {
    test(`Get Workspace Scorecards API,
    case: ${scenario.case},
    type: ${scenario.type},
    tags: ${COMMON_TEST_TAGS.API_TEST} ${COMMON_TEST_TAGS.REGRESSION_TEST} ${SCORING_TAGS.REGRESSION_TAG} ${COMMON_TEST_TAGS.API_BFF_TAG}`, async ({
      request,
    }) => {
      test.skip(env === "prod", "Skipping API isolated tests in prod - requires test data not available in prod");
      test.slow();
      const { getURL } = createPageInstances();
      const apiTokenInfo: ApiKeyInfo = await getApiKeyInfo(
        request,
        scopes["scoring-ro"]
      );
      const apiToken = apiTokenInfo.apiKey;
      const apiTokenId = apiTokenInfo.id;
      const baseUrl = getURL.returnURL(`api-bff-${env}`);
      const requestUrl = `${baseUrl}/v1/scoring/workspace/${scenario.resource.workspaceId}/scorecards`;
      const requestOptions = {
        params: {
          ...scenario.resource.params,
        },
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${apiToken}`,
        },
      };

      const response = await request.get(requestUrl, requestOptions);
      await deleteApiKey(request, apiTokenId);
      await scenario.verify(response);
    });
  });
});
