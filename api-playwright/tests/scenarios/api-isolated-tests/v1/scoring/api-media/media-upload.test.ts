import { test, expect, APIResponse } from '@playwright/test';
import { createPageInstances } from '../../../../../utils/helper';
import { scopes, getApiKeyInfo, deleteApiKey } from '../../../../../utils/api-token-utils';
import { ApiKeyInfo } from '../../../../../utils/api-key.types';
import { API_RESPONSE_STATUS, COMMON_TEST_TAGS, SCORING_TAGS, TEST_TYPES } from '../../../../../utils/util-constants';
import { ErrorResponseBody, SuccessResponseBody } from '../../../../../utils/scoring.types';

const env = process.env.TESTENV || 'dev';
const TEST_API_MEDIA_ID = 'API-PLAYWRIGHT-TEST-MEDIA';
const TEST_MEDIA_SOURCE = 'playwright-test';
const TEST_MEDIA_VERSION = new Date().getTime().toString();
const TEST_MEDIA_URL = 'https://vidmob-assets-public-dev.s3.amazonaws.com/tests/test-media-api-tests.jpg';
const TEST_MEDIA_NAME = 'Playwright Test Media';
const TEST_MEDIA_DESCRIPTION = 'Playwright test media';
const TEST_MEDIA_BRANDS = ['hello', 'fine'];
const TEST_MEDIA_MARKETS = ['usa'];
const TEST_MEDIA_CHANNELS = ['FACEBOOK','TWITTER','DV360','SNAPCHAT','TIKTOK','LINKEDIN'];

const resources = {
  uploadMediaRequest: {
    id: TEST_API_MEDIA_ID,
    version: TEST_MEDIA_VERSION,
    source: TEST_MEDIA_SOURCE,
    url: TEST_MEDIA_URL,
    name: TEST_MEDIA_NAME,
    description: TEST_MEDIA_DESCRIPTION,
    brands: TEST_MEDIA_BRANDS,
    markets: TEST_MEDIA_MARKETS,
    channels: TEST_MEDIA_CHANNELS,
  },
  completeRequest: {
    id: TEST_API_MEDIA_ID,
    version: TEST_MEDIA_VERSION,
    source: TEST_MEDIA_SOURCE,
    url: TEST_MEDIA_URL,
    name: TEST_MEDIA_NAME,
    description: TEST_MEDIA_DESCRIPTION,
    brands: TEST_MEDIA_BRANDS,
    markets: TEST_MEDIA_MARKETS,
    channels: TEST_MEDIA_CHANNELS,
  },
  missingMediaId: {
    version: TEST_MEDIA_VERSION,
    source: TEST_MEDIA_SOURCE,
    url: TEST_MEDIA_URL,
    name: TEST_MEDIA_NAME,
    description: TEST_MEDIA_DESCRIPTION,
    brands: TEST_MEDIA_BRANDS,
    markets: TEST_MEDIA_MARKETS,
    channels: TEST_MEDIA_CHANNELS,
  },
  missingUrl: {
    id: TEST_API_MEDIA_ID,
    version: TEST_MEDIA_VERSION,
    source: TEST_MEDIA_SOURCE,
    name: TEST_MEDIA_NAME,
    description: TEST_MEDIA_DESCRIPTION,
    brands: TEST_MEDIA_BRANDS,
    markets: TEST_MEDIA_MARKETS,
    channels: TEST_MEDIA_CHANNELS,
  },
};

const testScenarios = [
  {
    case: 'Success 201 Response - Media Uploaded',
    type: TEST_TYPES.POSITIVE,
    resource: resources.completeRequest,
    verify: async (response: APIResponse) => {
      const responseBody: SuccessResponseBody = await response.json();
      expect(response.status()).toBe(API_RESPONSE_STATUS.CREATED);
      expect(responseBody.result.uniqueId).toBeDefined();
    },
  },
  {
    case: 'Conflict 409 Response - Media Already Uploaded',
    type: TEST_TYPES.NEGATIVE,
    resource: resources.completeRequest,
    verify: async (response: APIResponse) => {
      const expectedErrorMessage = 'already exists';
      const responseBody: ErrorResponseBody = await response.json();
      expect(response.status()).toBe(API_RESPONSE_STATUS.CONFLICT);
      expect(JSON.stringify(responseBody)).toContain(expectedErrorMessage);
    },
  },
  {
    case: 'Bad Request 400 Response - Missing Media ID',
    type: TEST_TYPES.NEGATIVE,
    resource: resources.missingMediaId,
    verify: async (response: APIResponse) => {
      const expectedErrorMessage = 'id must be a string';
      const responseBody: ErrorResponseBody = await response.json();
      expect(response.status()).toBe(API_RESPONSE_STATUS.BAD_REQUEST);
      expect(JSON.stringify(responseBody)).toContain(expectedErrorMessage);
    },
  },
  {
    case: 'Bad Request 400 Response - Missing Media Url',
    type: TEST_TYPES.NEGATIVE,
    resource: resources.missingUrl,
    verify: async (response: APIResponse) => {
      const expectedErrorMessage = 'url must be a string';
      const responseBody: ErrorResponseBody = await response.json();
      expect(response.status()).toBe(API_RESPONSE_STATUS.BAD_REQUEST);
      expect(JSON.stringify(responseBody)).toContain(expectedErrorMessage);
    },
  },
];

test.describe(() => {
  // All tests in this describe group will get 2 retry attempts.
  test.describe.configure({ retries: 2 });

  testScenarios.forEach((scenario) => {
    test(
    `Upload API Media POST API,
    case: ${scenario.case},
    type: ${scenario.type},
    tags: ${COMMON_TEST_TAGS.API_TEST} ${COMMON_TEST_TAGS.REGRESSION_TEST} ${SCORING_TAGS.REGRESSION_TAG} ${COMMON_TEST_TAGS.API_BFF_TAG}`,
      async ({request}) => {
        test.skip(env === "prod", "Skipping API isolated tests in prod - requires test data not available in prod");
        test.slow();
        const { getURL } = createPageInstances();
        const apiTokenInfo: ApiKeyInfo = await getApiKeyInfo(request, scopes['scoring-rw']);
        const apiToken = apiTokenInfo.apiKey;
        const apiTokenId = apiTokenInfo.id;
        const baseUrl = getURL.returnURL(`api-bff-${env}`);
        const requestUrl = `${baseUrl}/v1/media`;
        const requestOptions = {
          data: { ...scenario.resource },
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${apiToken}`,
          }
        }

        const response = await request.post(requestUrl, requestOptions);
        await deleteApiKey(request, apiTokenId);
        await scenario.verify(response);
      })
  });
});
