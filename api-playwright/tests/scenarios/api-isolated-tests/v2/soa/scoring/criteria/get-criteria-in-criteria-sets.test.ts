/* eslint-disable @typescript-eslint/no-unused-vars */
import { test, expect, APIResponse } from '@playwright/test';
import { createPageInstances } from '../../../../../../utils/helper';
import { SuccessResponseBody, CriteriaResult } from '../../../../../../utils/scoring.types';
import {API_RESPONSE_STATUS, SCORING_TAGS, SOA_PROJECTS, TEST_TYPES} from '../../../../../../utils/util-constants';
import {getRequestOptions} from "../../../../../../utils/util-functions";

const env = process.env.TESTENV || 'dev';
const DEV_CRITERIA_SET_IDS = [3];
const STAGE_CRITERIA_SET_IDS = [2];

test.skip(env == "prod");

const criteriaSetIds = {
    dev: DEV_CRITERIA_SET_IDS,
    stage: STAGE_CRITERIA_SET_IDS
};

export function assertCriteriaResultShape(criteria: CriteriaResult, expectedPerPage: number): void {
    expect(criteria).toBeDefined();
    expect(typeof criteria.name).toBe('string');
    expect(typeof criteria.identifier).toBe('string');
    expect(typeof criteria.dateCreated).toBe('string');
    expect(typeof criteria.id).toBe('number');
    expect(typeof criteria.criteriaSetId).toBe('number');
    expect(typeof criteria.criteriaSet).toBe('object');
    expect(Array.isArray(criteria.mediaTypes)).toBeTruthy();
    expect(typeof criteria.platform).toBe('string');
}

const testScenarios = [
    {
        case: 'success 200 response - valid criteriaSetIds',
        type: TEST_TYPES.POSITIVE,
        criteriaSetIds: criteriaSetIds[env],
        verify: async (response: APIResponse) => {
            const responseBody: SuccessResponseBody = await response.json();
            expect(response.status()).toBe(API_RESPONSE_STATUS.SUCCESS);
            const firstResult = responseBody.result[0];
            assertCriteriaResultShape(firstResult, 1);
            expect(responseBody.pagination.perPage).toEqual(1);
        },
    },
];

testScenarios.forEach((scenario) => {
  /*  test(`API test GET criteria, case: ${scenario.case}, criteriaSetIds: ${scenario.criteriaSetIds.join(', ')} tags: @apiRegressionTest @apitest ${SCORING_TAGS.REGRESSION_TAG} ${SCORING_TAGS.SCORING_SERVICE_TAG}`, async ({ request }) => {

        const { getURL, getLoginToken } = createPageInstances();
        const accessToken = await getLoginToken.getAccessToken(request);
        const baseURL = `${getURL.returnURL("soa-" + env)}/v1/criteria-set/criteria`;
        const queryParams = `criteriaSetIds=${scenario.criteriaSetIds.join(',')}&perPage=1`;
        const urlWithQueryParams = `${baseURL}?${queryParams}`;
        const requestOptions = getRequestOptions({
            Authorization: `Bearer ${accessToken}`,
            "vidmob-nestjs-service": SOA_PROJECTS.SCORING_SERVICE
        });

        const response = await request.get(urlWithQueryParams, requestOptions);
        await scenario.verify(response);
    });*/
});
