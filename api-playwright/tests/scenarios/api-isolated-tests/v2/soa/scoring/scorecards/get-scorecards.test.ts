import { test, expect, APIResponse } from '@playwright/test';
import { createPageInstances } from '../../../../../../utils/helper';
import { API_RESPONSE_STATUS, SCORING_TAGS, TEST_TYPES, SOA_PROJECTS } from '../../../../../../utils/util-constants';
import { ErrorResponseBody, SuccessResponseBody } from '../../../../../../utils/scoring.types';
import { getRequestOptions } from '../../../../../../utils/util-functions';

const env = process.env.TESTENV || 'dev';
const DEV_WORKSPACE_ID = 23142;
const STAGE_WORKSPACE_ID = 2735;

test.skip(env == "prod");

const workspaceIds = {
    dev: DEV_WORKSPACE_ID,
    stage: STAGE_WORKSPACE_ID
};

const testScenarios = [
    {
        case: 'success 200 response',
        type: TEST_TYPES.POSITIVE,
        workspaceId: workspaceIds[env],
        role: 'admin',
        parameters: [{key: 'perPage', value: '1'}, {key: 'types', value: 'PRE_FLIGHT'}],
        verify: async (response: APIResponse) => {
            const responseBody: SuccessResponseBody = await response.json();
            expect(response.status()).toBe(API_RESPONSE_STATUS.SUCCESS);
            expect(responseBody.result.length).toEqual(1);
            expect(responseBody.pagination.perPage).toEqual(1);
        }
    },
    {
        case: 'bad request 400 - check scorecard types',
        type: TEST_TYPES.NEGATIVE,
        workspaceId: workspaceIds[env],
        role: 'admin',
        parameters: [{key: 'types', value: 'unknown'}, {key: 'perPage', value: '1'}],
        verify: async (response: APIResponse) => {
            const responseBody: ErrorResponseBody = await response.json();
            expect(response.status()).toBe(API_RESPONSE_STATUS.BAD_REQUEST);
            expect(responseBody.error.message).toContain('Invalid Scorecard type present in the param');
        }
    }
];

testScenarios.forEach((scenario) => {
    test(`API test GET scorecards, 
type: ${scenario.type}, 
workspace: ${scenario.workspaceId}
tags: @apiRegressionTest @apitest ${SCORING_TAGS.REGRESSION_TAG} ${SCORING_TAGS.SCORING_SERVICE_TAG}`, async ({ request }) => {
        const { getURL, getLoginToken } = createPageInstances();
        const accessToken = await getLoginToken.getAccessToken(request);
        const baseURL = `${getURL.returnURL("soa-" + env)}/v1/scorecard`;
        const queryParams = scenario.parameters.map(p => `${p.key}=${p.value}`).join('&');
        const urlWithQueryParams = `${baseURL}?workspaceId=${scenario.workspaceId}&${queryParams}`;
        const requestOptions = getRequestOptions({
            Authorization: `Bearer ${accessToken}`,
            "vidmob-nestjs-service": SOA_PROJECTS.SCORING_SERVICE
        });

        const response = await request.get(urlWithQueryParams, requestOptions);
        await scenario.verify(response);
    });
});
