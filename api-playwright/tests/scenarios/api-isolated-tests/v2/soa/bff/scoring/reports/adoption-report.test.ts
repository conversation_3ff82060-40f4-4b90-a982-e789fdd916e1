import { test, expect } from "@playwright/test";
import { createPageInstances } from '../../../../../../../utils/helper';
// @ts-ignore
import * as requestsConfig from '../../../../../../../payload/bff/scoring-reports/createAdoptionReportPayload';
// @ts-ignore
import * as expectedResponses from '../../../../../../../payload/bff/scoring-reports/createAdoptionReportExpectedResponse';

const env = process.env.TESTENV || "";

test(
  "API test - Verify the accuracy of PRE_FLIGHT adoption report @apiRegressionTest @apitest @adoptionReport",
  async ({ request }) => {
    const { getURL, getLoginToken } = createPageInstances();
    const accessToken = await getLoginToken.getAccessToken(request);
    const workspaceId = requestsConfig[env].workspaceId;

    const baseURL: string = getURL.returnURL("bff-" + env) + "/v2/reports/adoption/workspace/" + workspaceId;
    const {analyticsFilters, ...payloadWithoutAnalyticsFilters} = requestsConfig[env].payload;
    const requestOptions = {
      data: {
        ...payloadWithoutAnalyticsFilters,
        filters: [
          ...payloadWithoutAnalyticsFilters.filters,
          {
            "fieldName": "batchType",
            "operator": "equals",
            "value": "PRE_FLIGHT"
          },
        ]
      },
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + accessToken
      },
    };

    const response = await request.post(baseURL, requestOptions)
    const responseBody = await response.json();

    const usaMarch2023 = responseBody.result.data.filter(item => item.row.market === 'usa' && item.column.month === '2023-01')[0];
    const {expectedDataLength, expectedUSAMarch2023} = expectedResponses[env].preFlight;

    expect(response.status()).toBe(201);
    expect(responseBody.status).toBe('OK');
    expect(responseBody.result.data.length).toBe(expectedDataLength);
    expect(usaMarch2023 || null).toStrictEqual(expectedUSAMarch2023);
  }
);

test(
  "API test - Verify the accuracy of IN_FLIGHT adoption report @apiRegressionTest @apitest @adoptionReport",
  async ({ request }) => {
    const { getURL, getLoginToken } = createPageInstances();
    const accessToken = await getLoginToken.getAccessToken(request);
    const workspaceId = requestsConfig[env].workspaceId;

    const baseURL: string = getURL.returnURL("bff-" + env) + "/v2/reports/adoption/workspace/" + workspaceId;
    const {analyticsFilters, ...payloadWithoutAnalyticsFilters} = requestsConfig[env].payload;
    const requestOptions = {
      data: {
        ...payloadWithoutAnalyticsFilters,
        filters: [
          ...payloadWithoutAnalyticsFilters.filters,
          {
            "fieldName": "batchType",
            "operator": "equals",
            "value": "IN_FLIGHT"
          },
        ]
      },
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + accessToken
      },
    };

    const response = await request.post(baseURL, requestOptions)
    const responseBody = await response.json();

    const usaMarch2023 = responseBody.result.data.filter(item => item.row.market === 'usa' && item.column.month === '2023-01')[0];
    const {expectedDataLength, expectedUSAMarch2023} = expectedResponses[env].inFlightNoAnalyticsFilters;

    expect(response.status()).toBe(201);
    expect(responseBody.status).toBe('OK');
    expect(responseBody.result.data.length).toBe(expectedDataLength);
    expect(usaMarch2023 || null).toStrictEqual(expectedUSAMarch2023);
  }
);

test(
  "API test - Verify the accuracy of IN_FLIGHT adoption report with advanced analytics filters @apiRegressionTest @apitest @adoptionReport",
  async ({ request }) => {
    const { getURL, getLoginToken } = createPageInstances();
    const accessToken = await getLoginToken.getAccessToken(request);
    const workspaceId = requestsConfig[env].workspaceId;

    const baseURL: string = getURL.returnURL("bff-" + env) + "/v2/reports/adoption/workspace/" + workspaceId;
    const requestOptions = {
      data: {
        ...requestsConfig[env].payload,
        filters: [
          ...requestsConfig[env].payload.filters,
          {
            "fieldName": "batchType",
            "operator": "equals",
            "value": "IN_FLIGHT"
          },
        ]
      },
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + accessToken
      },
    };

    const response = await request.post(baseURL, requestOptions)
    const responseBody = await response.json();

    const usaMarch2023 = responseBody.result.data.filter(item => item.row.market === 'usa' && item.column.month === '2023-01')[0];
    const {expectedDataLength, expectedUSAMarch2023} = expectedResponses[env].inFlightWithAnalyticsFilters;

    expect(response.status()).toBe(201);
    expect(responseBody.status).toBe('OK');
    expect(responseBody.result.data.length).toBe(expectedDataLength);
    expect(usaMarch2023 || null).toStrictEqual(expectedUSAMarch2023);
  }
);

test(
  "API test - Verify api fails when invalid channel provided for analytics filters @apiRegressionTest @apitest @adoptionReport",
  async ({ request }) => {
    const { getURL, getLoginToken } = createPageInstances();
    const accessToken = await getLoginToken.getAccessToken(request);
    const workspaceId = requestsConfig[env].workspaceId;

    const baseURL: string = getURL.returnURL("bff-" + env) + "/v2/reports/adoption/workspace/" + workspaceId;
    const requestWithInvalidPlatform = {
      ...requestsConfig[env].payload,
      filters: [
        ...requestsConfig[env].payload.filters,
        {
          "fieldName": "batchType",
          "operator": "equals",
          "value": "IN_FLIGHT"
        }
      ],
      analyticsFilters: {
        ...requestsConfig[env].payload.analyticsFilters,
        INVALID_CHANNEL: [{ type: "media_type", values: ["VIDEO", "IMAGE"]}]
      }
    };
    const requestOptions = {
      data: requestWithInvalidPlatform,
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + accessToken
      },
    };

    const response = await request.post(baseURL, requestOptions)
    const responseBody = await response.json();

    expect(response.status()).toBe(400);
    expect(responseBody.status).toBe('ERROR');
    expect(responseBody.error.message).toBe('Invalid platform key: INVALID_CHANNEL. Allowed platforms: ALL_PLATFORMS, TWITTER, FACEBOOK, TIKTOK, PINTEREST, LINKEDIN, DV360, ADWORDS, SNAPCHAT, AMAZON, REDDIT');
    expect(responseBody.error.system).toBe('scoring');
  }
);

test(
  "API test - Verify api fails when analytics filter is invalid type @apiRegressionTest @apitest @adoptionReport",
  async ({ request }) => {
    const { getURL, getLoginToken } = createPageInstances();
    const accessToken = await getLoginToken.getAccessToken(request);
    const workspaceId = requestsConfig[env].workspaceId;

    const baseURL: string = getURL.returnURL("bff-" + env) + "/v2/reports/adoption/workspace/" + workspaceId;
    const requestWithInvalidPlatform = {
      ...requestsConfig[env].payload,
      filters: [
        ...requestsConfig[env].payload.filters,
        {
          "fieldName": "batchType",
          "operator": "equals",
          "value": "IN_FLIGHT"
        }
      ],
      analyticsFilters: {
        ...requestsConfig[env].payload.analyticsFilters,
        FACEBOOK: [
          ...requestsConfig[env].payload.analyticsFilters.FACEBOOK,
          { type: "invalid-type", values: ["bad"] }
        ]
      }
    };
    const requestOptions = {
      data: requestWithInvalidPlatform,
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + accessToken
      },
    };

    const response = await request.post(baseURL, requestOptions)
    const responseBody = await response.json();

    expect(response.status()).toBe(400);
    expect(responseBody.status).toBe('ERROR');
    expect(responseBody.error.message).toBe('Invalid analytics filter type key for FACEBOOK: invalid-type. Allowed types: media_type, campaign_identifier, created_by_vidmob, campaign_objective, adset_identifier, ad_identifier, ad_impressions, ad_placement, ad_type, creative_impressions');
    expect(responseBody.error.system).toBe('scoring');
  }
);
