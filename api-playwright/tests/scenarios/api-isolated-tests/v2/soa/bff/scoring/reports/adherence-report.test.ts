import { test, expect } from "@playwright/test";
import { createPageInstances } from "../../../../../../../utils/helper";
import {
  getPayloadWithoutAnalyticsFilters,
  getPayloadWithAnalyticsFilters,
} from "../../../../../../../utils/scoring-report-utils";
// @ts-ignore
import * as requestsConfig from "../../../../../../../payload/bff/scoring-reports/createAdherenceReportPayload";
// @ts-ignore
import * as expectedResponses from "../../../../../../../payload/bff/scoring-reports/createAdherenceReportExpectedResponse";

const env = process.env.TESTENV || "";

const getCriteriaToTest = (responseBody) => {
  const [firstCriteriaId, secondCriteriaId, thirdCriteriaId] =
    expectedResponses[env].idsOfCriteriaToTest;
  const firstCriteria =
    responseBody.result.columns.filter(
      (column: any) => column.id === firstCriteriaId
    )?.[0] || null;
  const secondCriteria =
    responseBody.result.columns.filter(
      (column: any) => column.id === secondCriteriaId
    )?.[0] || null;
  const thirdCriteria =
    responseBody.result.columns.filter(
      (column: any) => column.id === thirdCriteriaId
    )?.[0] || null;

  return [firstCriteria, secondCriteria, thirdCriteria];
};

test("API test - Verify the accuracy of PRE_FLIGHT adherence report with norms @apiRegressionTest @apitest @adherenceReport", async ({
  request,
}) => {
  const { getURL, getLoginToken } = createPageInstances();
  const accessToken = await getLoginToken.getAccessToken(request);
  const workspaceId = requestsConfig[env].workspaceId;

  const baseURL: string =
    getURL.returnURL("bff-" + env) +
    "/v2/reports/adherence-norms/workspace/" +
    workspaceId;

  const requestOptions = {
    data: getPayloadWithoutAnalyticsFilters(
      requestsConfig[env].payload,
      "PRE_FLIGHT"
    ),
    headers: {
      "Content-Type": "application/json",
      Authorization: "Bearer " + accessToken,
    },
  };

  const response = await request.post(baseURL, requestOptions);
  const responseBody = await response.json();

  const {
    columnsLength,
    rowsLength,
    columnsWithNormsDataLength,
    expectedFirstCriteria,
    expectedSecondCriteria,
    expectedThirdCriteria,
  } = expectedResponses[env].preFlight;
  const columnsWithNormsData = responseBody.result.columns.filter(
    (column: any) => Boolean(column.totalNorms)
  );
  const [firstCriteria, secondCriteria, thirdCriteria] =
    getCriteriaToTest(responseBody);

  expect(response.status()).toBe(201);
  expect(responseBody.status).toBe("OK");
  expect(responseBody.result.columns.length).toBe(columnsLength);
  expect(responseBody.result.rows.length).toBe(rowsLength);
  expect(columnsWithNormsData.length).toBe(columnsWithNormsDataLength);
  expect(firstCriteria).toStrictEqual(expectedFirstCriteria);
  expect(secondCriteria).toStrictEqual(expectedSecondCriteria);
  expect(thirdCriteria).toStrictEqual(expectedThirdCriteria);
});

test("API test - Verify the accuracy of IN_FLIGHT adherence report with norms @apiRegressionTest @apitest @adherenceReport", async ({
  request,
}) => {
  const { getURL, getLoginToken } = createPageInstances();
  const accessToken = await getLoginToken.getAccessToken(request);
  const workspaceId = requestsConfig[env].workspaceId;

  const baseURL: string =
    getURL.returnURL("bff-" + env) +
    "/v2/reports/adherence-norms/workspace/" +
    workspaceId;
  const requestOptions = {
    data: getPayloadWithoutAnalyticsFilters(
      requestsConfig[env].payload,
      "IN_FLIGHT"
    ),
    headers: {
      "Content-Type": "application/json",
      Authorization: "Bearer " + accessToken,
    },
  };

  const response = await request.post(baseURL, requestOptions);
  const responseBody = await response.json();

  const {
    columnsLength,
    rowsLength,
    columnsWithNormsDataLength,
    expectedFirstCriteria,
    expectedSecondCriteria,
    expectedThirdCriteria,
  } = expectedResponses[env].inFlightNoAnalyticsFilters;
  const columnsWithNormsData = responseBody.result.columns.filter(
    (column: any) => Boolean(column.totalNorms)
  );
  const availableIds = responseBody.result.columns.map(
    (column: any) => column.id
  );

  const [firstCriteria, secondCriteria, thirdCriteria] =
    getCriteriaToTest(responseBody);

  expect(response.status()).toBe(201);
  expect(responseBody.status).toBe("OK");
  expect(responseBody.result.columns.length).toBe(columnsLength);
  expect(responseBody.result.rows.length).toBe(rowsLength);
  expect(columnsWithNormsData.length).toBe(columnsWithNormsDataLength);
  expect(firstCriteria).toStrictEqual(expectedFirstCriteria);
  expect(secondCriteria).toStrictEqual(expectedSecondCriteria);
  expect(thirdCriteria).toStrictEqual(expectedThirdCriteria);
});

test("API test - Verify the accuracy of IN_FLIGHT adherence report with advanced analytics filters and norms @apiRegressionTest @apitest @adherenceReport", async ({
  request,
}) => {
  const { getURL, getLoginToken } = createPageInstances();
  const accessToken = await getLoginToken.getAccessToken(request);
  const workspaceId = requestsConfig[env].workspaceId;

  const baseURL: string =
    getURL.returnURL("bff-" + env) +
    "/v2/reports/adherence-norms/workspace/" +
    workspaceId;

  const requestOptions = {
    data: getPayloadWithAnalyticsFilters(
      requestsConfig[env].payload,
      "IN_FLIGHT"
    ),
    headers: {
      "Content-Type": "application/json",
      Authorization: "Bearer " + accessToken,
    },
  };

  const response = await request.post(baseURL, requestOptions);
  const responseBody = await response.json();

  const {
    columnsLength,
    rowsLength,
    columnsWithNormsDataLength,
    expectedFirstCriteria,
    expectedSecondCriteria,
    expectedThirdCriteria,
  } = expectedResponses[env].inFlightWithAnalyticsFilters;
  const columnsWithNormsData = responseBody.result.columns.filter(
    (column: any) => Boolean(column.totalNorms)
  );
  const [firstCriteria, secondCriteria, thirdCriteria] =
    getCriteriaToTest(responseBody);

  expect(response.status()).toBe(201);
  expect(responseBody.status).toBe("OK");
  expect(responseBody.result.columns.length).toBe(columnsLength);
  expect(responseBody.result.rows.length).toBe(rowsLength);
  expect(columnsWithNormsData.length).toBe(columnsWithNormsDataLength);
  expect(firstCriteria).toStrictEqual(expectedFirstCriteria);
  expect(secondCriteria).toStrictEqual(expectedSecondCriteria);
  expect(thirdCriteria).toStrictEqual(expectedThirdCriteria);
});

test("API test - Verify api fails when invalid channel provided for analytics filters @apiRegressionTest @apitest @adherenceReport", async ({
  request,
}) => {
  const { getURL, getLoginToken } = createPageInstances();
  const accessToken = await getLoginToken.getAccessToken(request);
  const workspaceId = requestsConfig[env].workspaceId;

  const baseURL: string =
    getURL.returnURL("bff-" + env) +
    "/v2/reports/adherence-norms/workspace/" +
    workspaceId;
  const basePayload = getPayloadWithAnalyticsFilters(
    requestsConfig[env].payload,
    "IN_FLIGHT"
  );
  const requestWithInvalidPlatform = {
    ...basePayload,
    analyticsFilters: {
      ...basePayload.analyticsFilters,
      INVALID_CHANNEL: [{ type: "media_type", values: ["VIDEO", "IMAGE"] }],
    },
  };
  const requestOptions = {
    data: requestWithInvalidPlatform,
    headers: {
      "Content-Type": "application/json",
      Authorization: "Bearer " + accessToken,
    },
  };

  const response = await request.post(baseURL, requestOptions);
  const responseBody = await response.json();

  expect(response.status()).toBe(400);
  expect(responseBody.status).toBe("ERROR");
  expect(responseBody.error.message).toBe(
    "Invalid platform key: INVALID_CHANNEL. Allowed platforms: ALL_PLATFORMS, TWITTER, FACEBOOK, TIKTOK, PINTEREST, LINKEDIN, DV360, ADWORDS, SNAPCHAT, AMAZON, REDDIT"
  );
  expect(responseBody.error.system).toBe("scoring");
});

test("API test - Verify api fails when analytics filter is invalid type @apiRegressionTest @apitest @adherenceReport", async ({
  request,
}) => {
  const { getURL, getLoginToken } = createPageInstances();
  const accessToken = await getLoginToken.getAccessToken(request);
  const workspaceId = requestsConfig[env].workspaceId;

  const baseURL: string =
    getURL.returnURL("bff-" + env) +
    "/v2/reports/adherence-norms/workspace/" +
    workspaceId;
  const basePayload = getPayloadWithAnalyticsFilters(
    requestsConfig[env].payload,
    "IN_FLIGHT"
  );
  const requestWithInvalidPlatform = {
    ...basePayload,
    analyticsFilters: {
      ...basePayload.analyticsFilters,
      AMAZON: [{ type: "campaign_identifier", values: "bad" }],
    },
  };
  const requestOptions = {
    data: requestWithInvalidPlatform,
    headers: {
      "Content-Type": "application/json",
      Authorization: "Bearer " + accessToken,
    },
  };

  const response = await request.post(baseURL, requestOptions);
  const responseBody = await response.json();

  expect(response.status()).toBe(500);
  expect(responseBody.status).toBe("ERROR");
  expect(responseBody.error.message).toBe(
    "Invalid aggregate value type for non-aggregate filter campaign_identifier: bad}"
  );
  expect(responseBody.error.system).toBe("scoring");
});
