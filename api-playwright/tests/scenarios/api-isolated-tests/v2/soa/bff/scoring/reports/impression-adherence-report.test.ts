import { test, expect } from "@playwright/test";
import { createPageInstances } from '../../../../../../../utils/helper';
// @ts-ignore
import * as requestsConfig from '../../../../../../../payload/bff/scoring-reports/createImpressionAdherenceReportPayload';
// @ts-ignore
import * as expectedResponses from '../../../../../../../payload/bff/scoring-reports/createImpressionAdherenceReportExpectedResponse';

const env = process.env.TESTENV || "";

test(
  "API test - Verify the accuracy of impression adherence report @apiRegressionTest @apitest @impressionAdherenceReport",
  async ({ request }) => {
    const { getURL, getLoginToken } = createPageInstances();
    const accessToken = await getLoginToken.getAccessToken(request);
    const workspaceId = requestsConfig[env].workspaceId;

    const baseURL: string = getURL.returnURL("bff-" + env) + "/v2/reports/impression-adherence/workspace/" + workspaceId;
    const { analyticsFilters, ...payloadWithoutAnalyticsFilters } = requestsConfig[env].payload;
    const requestOptions = {
      data: payloadWithoutAnalyticsFilters,
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + accessToken
      },
    };

    const response = await request.post(baseURL, requestOptions)
    const responseBody = await response.json();

    const totalImpressions = responseBody.result.impressionCountData.reduce((acc, impressionCountData) => acc + impressionCountData.data.value, 0);

    const usaImpressionCountData =
      responseBody.result.impressionCountData
        .filter((impressionCountData) => impressionCountData.row.market === 'usa')
        .reduce((acc, impressionCountData) => ({...acc, [impressionCountData.row.workspace]: impressionCountData.data.value }), {});

    const {
      dataLength,
      impressionCountDataLength,
      expectedTotalImpressions,
      expectedUsaImpressionCountData,
    } = expectedResponses[env].noAnalyticsFilters;

    expect(response.status()).toBe(201);
    expect(responseBody.status).toBe('OK');
    expect(responseBody.result.data.length).toBe(dataLength);
    expect(responseBody.result.impressionCountData.length).toBe(impressionCountDataLength);
    expect(totalImpressions).toStrictEqual(expectedTotalImpressions);
    expect(usaImpressionCountData).toStrictEqual(expectedUsaImpressionCountData);
  }
);

test(
  "API test - Verify the accuracy of impression adherence report with advanced analytics filters @apiRegressionTest @apitest @impressionAdherenceReport",
  async ({ request }) => {
    const { getURL, getLoginToken } = createPageInstances();
    const accessToken = await getLoginToken.getAccessToken(request);
    const workspaceId = requestsConfig[env].workspaceId;

    const baseURL: string = getURL.returnURL("bff-" + env) + "/v2/reports/impression-adherence/workspace/" + workspaceId;
    const requestOptions = {
      data: requestsConfig[env].payload,
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + accessToken
      },
    };

    const response = await request.post(baseURL, requestOptions)
    const responseBody = await response.json();

    const totalImpressions = responseBody.result.impressionCountData.reduce((acc, impressionCountData) => acc + impressionCountData.data.value, 0);

    const usaImpressionCountData =
      responseBody.result.impressionCountData
        .filter((impressionCountData: any) => impressionCountData.row.market === 'usa')
        .reduce((acc: any, impressionCountData: any) => ({...acc, [impressionCountData.row.workspace]: impressionCountData.data.value }), {});

    const {
      dataLength,
      impressionCountDataLength,
      expectedTotalImpressions,
      expectedUsaImpressionCountData,
    } = expectedResponses[env].withAnalyticsFilters;

    expect(response.status()).toBe(201);
    expect(responseBody.status).toBe('OK');
    expect(responseBody.result.data.length).toBe(dataLength);
    expect(responseBody.result.impressionCountData.length).toBe(impressionCountDataLength);
    expect(totalImpressions).toStrictEqual(expectedTotalImpressions);
    expect(usaImpressionCountData).toStrictEqual(expectedUsaImpressionCountData);
  }
);

test(
  "API test - Verify api fails when invalid channel provided for analytics filters @apiRegressionTest @apitest @impressionAdherenceReport",
  async ({ request }) => {
    const { getURL, getLoginToken } = createPageInstances();
    const accessToken = await getLoginToken.getAccessToken(request);
    const workspaceId = requestsConfig[env].workspaceId;

    const baseURL: string = getURL.returnURL("bff-" + env) + "/v2/reports/impression-adherence/workspace/" + workspaceId;
    const requestWithInvalidPlatform = {
      ...requestsConfig[env].payload,
      analyticsFilters: {
        ...requestsConfig[env].payload.analyticsFilters,
        INVALID_CHANNEL: [{ type: "media_type", values: ["VIDEO", "IMAGE"]}]
      }
    };
    const requestOptions = {
      data: requestWithInvalidPlatform,
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + accessToken
      },
    };

    const response = await request.post(baseURL, requestOptions)
    const responseBody = await response.json();

    expect(response.status()).toBe(400);
    expect(responseBody.status).toBe('ERROR');
    expect(responseBody.error.message).toBe('Invalid platform key: INVALID_CHANNEL. Allowed platforms: ALL_PLATFORMS, TWITTER, FACEBOOK, TIKTOK, PINTEREST, LINKEDIN, DV360, ADWORDS, SNAPCHAT, AMAZON, REDDIT');
    expect(responseBody.error.system).toBe('scoring');
  }
);

test(
  "API test - Verify api fails when analytics filter is invalid type @apiRegressionTest @apitest @impressionAdherenceReport",
  async ({ request }) => {
    const { getURL, getLoginToken } = createPageInstances();
    const accessToken = await getLoginToken.getAccessToken(request);
    const workspaceId = requestsConfig[env].workspaceId;

    const baseURL: string = getURL.returnURL("bff-" + env) + "/v2/reports/impression-adherence/workspace/" + workspaceId;
    const requestWithInvalidPlatform = {
      ...requestsConfig[env].payload,
      analyticsFilters: {
        ...requestsConfig[env].payload.analyticsFilters,
        FACEBOOK: [
          ...requestsConfig[env].payload.analyticsFilters.FACEBOOK.filter((filter: any) => filter.type !== "campaign_identifier"),
          { type: "campaign_identifier", values: "bad" }
        ]
      }
    };
    const requestOptions = {
      data: requestWithInvalidPlatform,
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + accessToken
      },
    };

    const response = await request.post(baseURL, requestOptions)
    const responseBody = await response.json();

    expect(response.status()).toBe(500);
    expect(responseBody.status).toBe('ERROR');
    expect(responseBody.error.message).toBe('Invalid aggregate value type for non-aggregate filter campaign_identifier: bad}');
    expect(responseBody.error.system).toBe('scoring');
  }
);
