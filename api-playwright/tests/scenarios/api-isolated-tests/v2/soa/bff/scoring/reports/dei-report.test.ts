import { test, expect } from "@playwright/test";
import { createPageInstances } from '../../../../../../../utils/helper';
// @ts-ignore
import * as requestsConfig from '../../../../../../../payload/bff/scoring-reports/createDEIReportPayload';
// @ts-ignore
import * as expectedResponses from '../../../../../../../payload/bff/scoring-reports/createDEIReportExpectedResponse.json';

const env = process.env.TESTENV || "";

test(
  "API test - Verify the accuracy of PRE_FLIGHT DEI report with norms @apiRegressionTest @apitest @deiReport",
  async ({ request }) => {
    const { getURL, getLoginToken } = createPageInstances();
    const accessToken = await getLoginToken.getAccessToken(request);
    const workspaceId = requestsConfig[env].workspaceId;

    const baseURL: string = getURL.returnURL("bff-" + env) + "/v2/reports/diversity/workspace/" + workspaceId;
    const {analyticsFilters, ...payloadWithoutAnalyticsFilters} = requestsConfig[env].payload;
    const payload = {
      ...payloadWithoutAnalyticsFilters,
      filters: [
        ...payloadWithoutAnalyticsFilters.filters,
        {
          "fieldName": "batchType",
          "operator": "equals",
          "value": "PRE_FLIGHT"
        },
      ]
    }
    const requestOptions = {
      data: payload,
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + accessToken
      },
    };

    const response = await request.post(baseURL, requestOptions)
    const responseBody = await response.json();

    expect(response.status()).toBe(201);
    expect(responseBody.status).toBe('OK');
    expect(responseBody.result.data).toStrictEqual(expectedResponses[env].preFlight);
  }
);

test(
  "API test - Verify the accuracy of IN_FLIGHT DEI report with norms @apiRegressionTest @apitest @deiReport",
  async ({ request }) => {
    const { getURL, getLoginToken } = createPageInstances();
    const accessToken = await getLoginToken.getAccessToken(request);
    const workspaceId = requestsConfig[env].workspaceId;

    const baseURL: string = getURL.returnURL("bff-" + env) + "/v2/reports/diversity/workspace/" + workspaceId;
    const {analyticsFilters, ...payloadWithoutAnalyticsFilters} = requestsConfig[env].payload;
    const payload = {
      ...payloadWithoutAnalyticsFilters,
      filters: [
        ...payloadWithoutAnalyticsFilters.filters,
        {
          "fieldName": "batchType",
          "operator": "equals",
          "value": "IN_FLIGHT"
        },
      ]
    }
    const requestOptions = {
      data: payload,
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + accessToken
      },
    };

    const response = await request.post(baseURL, requestOptions)
    const responseBody = await response.json();

    expect(response.status()).toBe(201);
    expect(responseBody.status).toBe('OK');
    expect(responseBody.result.data).toStrictEqual(expectedResponses[env].noAnalyticsFilters);
  }
);

test(
  "API test - Verify the accuracy of IN_FLIGHT DEI report with advanced analytics filters and norms @apiRegressionTest @apitest @deiReport",
  async ({ request }) => {
    const { getURL, getLoginToken } = createPageInstances();
    const accessToken = await getLoginToken.getAccessToken(request);
    const workspaceId = requestsConfig[env].workspaceId;

    const baseURL: string = getURL.returnURL("bff-" + env) + "/v2/reports/diversity/workspace/" + workspaceId;
    const payload = {
      ...requestsConfig[env].payload,
      filters: [
        ...requestsConfig[env].payload.filters,
        {
          "fieldName": "batchType",
          "operator": "equals",
          "value": "IN_FLIGHT"
        },
      ]
    }
    const requestOptions = {
      data: payload,
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + accessToken
      },
    };

    const response = await request.post(baseURL, requestOptions)
    const responseBody = await response.json();

    expect(response.status()).toBe(201);
    expect(responseBody.status).toBe('OK');
    expect(responseBody.result.data).toStrictEqual(expectedResponses[env].withAnalyticsFilters);
  }
);

test(
  "API test - Verify api fails when analytics filter is invalid type @apiRegressionTest @apitest @deiReport",
  async ({ request }) => {
    const { getURL, getLoginToken } = createPageInstances();
    const accessToken = await getLoginToken.getAccessToken(request);
    const workspaceId = requestsConfig[env].workspaceId;

    const baseURL: string = getURL.returnURL("bff-" + env) + "/v2/reports/diversity/workspace/" + workspaceId;
    const requestWithInvalidPlatform = {
      ...requestsConfig[env].payload,
      filters: [
        ...requestsConfig[env].payload.filters,
        {
          "fieldName": "batchType",
          "operator": "equals",
          "value": "IN_FLIGHT"
        },
      ],
      analyticsFilters: {
        ...requestsConfig[env].payload.analyticsFilters,
        AMAZON: [
          { type: "invalid_name", values: "bad" }
        ]
      }
    };
    const requestOptions = {
      data: requestWithInvalidPlatform,
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + accessToken
      },
    };

    const response = await request.post(baseURL, requestOptions)
    const responseBody = await response.json();

    expect(response.status()).toBe(400);
    expect(responseBody.status).toBe('ERROR');
    expect(responseBody.error.message).toBe('Invalid analytics filter type key for AMAZON: invalid_name. Allowed types: media_type, campaign_identifier, created_by_vidmob, campaign_objective, adset_identifier, ad_identifier, ad_impressions, ad_placement, ad_type, creative_impressions');
    expect(responseBody.error.system).toBe('scoring');
  }
);
