import { test, expect } from "@playwright/test";
import { createPageInstances } from "../../../../../../../utils/helper";
import { getPayloadWithAnalyticsFilters } from "../../../../../../../utils/scoring-report-utils";
// @ts-ignore
import * as requestsConfig from "../../../../../../../payload/bff/scoring-reports/createV3AdherenceReportPayload";
// @ts-ignore
import * as expectedResponses from "../../../../../../../payload/bff/scoring-reports/createV3AdherenceReportExpectedResponse";

const env = process.env.TESTENV || "dev";
const { getURL, getLoginToken } = createPageInstances();

const testScenarios = [
  {
    title: 'v3 adherence report, PRE_FLIGHT batch',
    baseURL: getURL.returnURL("bff-" + env) + "/v3/reports/adherence-norms/workspace/",
    batchType: "PRE_FLIGHT",
    expectedResponses: expectedResponses[env].preFlight,
  },
  {
    title: 'v3 adherence report, IN_FLIGHT batch',
    baseURL: getURL.returnURL("bff-" + env) + "/v3/reports/adherence-norms/workspace/",
    batchType: "IN_FLIGHT",
    expectedResponses: expectedResponses[env].inFlight,
  },
  {
    title: 'v3 impressions adherence report',
    baseURL: getURL.returnURL("bff-" + env) + "/v3/reports/impression-adherence/workspace/",
    batchType: "IN_FLIGHT",
    expectedResponses: expectedResponses[env].inFlight,
  },
];

test.describe("API tests for v3 reports", () => {
  testScenarios.forEach((scenario) => {
    test(`Verify the accuracy of ${scenario.title} @apiRegressionTest @apitest @adherenceReport`,
      async ({
        request,
      }) => {
      const accessToken = await getLoginToken.getAccessToken(request);
      const workspaceId = requestsConfig[env].workspaceId;
      const baseURL: string = scenario.baseURL + workspaceId;
      const payload = getPayloadWithAnalyticsFilters(
        requestsConfig[env].payload,
        scenario.batchType
      );
      const requestOptions = {
        data: payload,
        headers: {
          "Content-Type": "application/json",
          Authorization: "Bearer " + accessToken,
        },
      };

      const response = await request.post(baseURL, requestOptions);
      const responseBody = await response.json();

      const {
        columnsLength, rowsLength, parentColumnsLength, parentRowsLength,
      } = scenario.expectedResponses;

      expect(response.status()).toBe(201);
      expect(responseBody.status).toBe("OK");
      expect(responseBody.result.columns.length).toBeGreaterThanOrEqual(columnsLength);
      expect(responseBody.result.rows.length).toBeGreaterThanOrEqual(rowsLength);
      expect(responseBody.result.columns.filter(item => !item.parentId).length).toBeGreaterThanOrEqual(parentColumnsLength);
      expect(responseBody.result.rows.filter(item => !item.parentId).length).toBeGreaterThanOrEqual(parentRowsLength);
    });
  });
});

test("API test - Verify v3 adherence api fails when invalid channel provided for analytics filters @apiRegressionTest @apitest @adherenceReport", async ({
  request,
}) => {
  const accessToken = await getLoginToken.getAccessToken(request);
  const workspaceId = requestsConfig[env].workspaceId;

  const baseURL: string =
    getURL.returnURL("bff-" + env) +
    "/v3/reports/adherence-norms/workspace/" +
    workspaceId;
  const basePayload = getPayloadWithAnalyticsFilters(
    requestsConfig[env].payload,
    "IN_FLIGHT"
  );
  const requestWithInvalidPlatform = {
    ...basePayload,
    analyticsFilters: {
      ...basePayload.analyticsFilters,
      INVALID_CHANNEL: [{ type: "media_type", values: ["VIDEO", "IMAGE"] }],
    },
  };
  const requestOptions = {
    data: requestWithInvalidPlatform,
    headers: {
      "Content-Type": "application/json",
      Authorization: "Bearer " + accessToken,
    },
  };

  const response = await request.post(baseURL, requestOptions);
  const responseBody = await response.json();

  expect(response.status()).toBe(400);
  expect(responseBody.status).toBe("ERROR");
  expect(responseBody.error.message).toBe(
    "Invalid platform key: INVALID_CHANNEL. Allowed platforms: ALL_PLATFORMS, TWITTER, FACEBOOK, TIKTOK, PINTEREST, LINKEDIN, DV360, ADWORDS, SNAPCHAT, AMAZON, REDDIT"
  );
  expect(responseBody.error.system).toBe("scoring");
});
