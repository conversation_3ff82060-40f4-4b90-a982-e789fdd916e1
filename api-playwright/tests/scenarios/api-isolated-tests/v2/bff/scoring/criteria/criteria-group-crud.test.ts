import { test, expect } from "@playwright/test";
import { createPageInstances } from "../../../../../../utils/helper";
import * as workspaceDetails from "../../../../../../feeder/workspace.json";

const env = process.env.TESTENV || "dev";
let criteriaGroupId = "";

test("API test - Verify the success of creating a criteria group @apiRegressionTest @apitest @criteriaGroup", async ({
  request,
}) => {
  const { getURL, getLoginToken } = createPageInstances();
  const accessToken = await getLoginToken.getAccessToken(request);
  const { organizationId, workspaceIds: workspaceId } = workspaceDetails[env];
  const criteriaGroupName = `Genuine Ads ${Date.now()}`;

  const requestOptions = {
    data: {
      name: criteriaGroupName,
      description: "Why does no one want to make normal ads these days?",
      color: "#E94F37",
    },
    headers: {
      "Content-Type": "application/json",
      Authorization: "Bearer " + accessToken,
    },
  };

  const baseURL: string =
    getURL.returnURL("bff-" + env) +
    "/v1/criteria-group/organization/" +
    organizationId +
    "/workspace/" +
    workspaceId;

  const response = await request.post(baseURL, requestOptions);
  const responseBody = await response.json();

  criteriaGroupId = responseBody.result.id;

  expect(response.status()).toBe(201);
  expect(responseBody.status).toBe("OK");
  expect(responseBody.result.id).toBeTruthy();
  expect(responseBody.result.name).toBe(criteriaGroupName);
  expect(responseBody.result.color).toBe("#E94F37");
});

test("API test - Verify the failure of creating a criteria group when name already exists @apiRegressionTest @apitest @criteriaGroup", async ({
  request,
}) => {
  const { getURL, getLoginToken } = createPageInstances();
  const accessToken = await getLoginToken.getAccessToken(request);
  const { organizationId, workspaceIds: workspaceId } = workspaceDetails[env];

  const requestOptions = {
    data: {
      name: "Genuine Ads",
      description: "Another criteria that informs ads media",
      color: "#000000",
    },
    headers: {
      "Content-Type": "application/json",
      Authorization: "Bearer " + accessToken,
    },
  };
  const baseURL: string =
    getURL.returnURL("bff-" + env) +
    "/v1/criteria-group/organization/" +
    organizationId +
    "/workspace/" +
    workspaceId;

  const response = await request.post(baseURL, requestOptions);
  const responseBody = await response.json();

  expect(response.status()).toBe(400);
  expect(responseBody.status).toBe("ERROR");
  expect(responseBody.error.message).toContain(
    "Criteria group with name Genuine Ads already exists in organization"
  );
});

test("API test - Verify the success of updating a criteria group @apiRegressionTest @apitest @criteriaGroup", async ({
  request,
}) => {
  const { getURL, getLoginToken } = createPageInstances();
  const accessToken = await getLoginToken.getAccessToken(request);
  const { organizationId, workspaceIds: workspaceId } = workspaceDetails[env];
  const nameToUpate = `Duration Validation ${Date.now()}`;
  const baseURL: string =
    getURL.returnURL("bff-" + env) +
    "/v1/criteria-group/organization/" +
    organizationId +
    "/workspace/" +
    workspaceId +
    "/criteria-group/" +
    criteriaGroupId;
  const requestOptions = {
    data: {
      name: nameToUpate,
      description: "Criteria that validate the duration of ad media",
      color: "#FFFFFF",
    },
    headers: {
      "Content-Type": "application/json",
      Authorization: "Bearer " + accessToken,
    },
  };

  const response = await request.patch(baseURL, requestOptions);
  const responseBody = await response.json();

  expect(response.status()).toBe(200);
  expect(responseBody.status).toBe("OK");
  expect(responseBody.result.id).toBeTruthy();
  expect(responseBody.result.name).toBe(nameToUpate);
  expect(responseBody.result.color).toBe("#FFFFFF");
});

test("API test - Verify the success of fetching all criteria groups @apiRegressionTest @apitest @criteriaGroup", async ({
  request,
}) => {
  const { getURL, getLoginToken } = createPageInstances();
  const accessToken = await getLoginToken.getAccessToken(request);
  const { organizationId, workspaceIds: workspaceId } = workspaceDetails[env];

  const baseURL: string =
    getURL.returnURL("bff-" + env) +
    "/v1/criteria-group/organization/" +
    organizationId;

  const response = await request.get(baseURL, {
    headers: { Authorization: "Bearer " + accessToken },
  });
  const responseBody = await response.json();

  expect(response.status()).toBe(200);
  expect(responseBody.status).toBe("OK");
  expect(responseBody.result.length).toBeTruthy();
  expect(responseBody.pagination.totalSize).toBeTruthy();
});

test("API test - Verify the success of deleting a criteria group @apiRegressionTest @apitest @criteriaGroup", async ({
  request,
}) => {
  const { getURL, getLoginToken } = createPageInstances();
  const accessToken = await getLoginToken.getAccessToken(request);
  const { organizationId, workspaceIds: workspaceId } = workspaceDetails[env];

  const baseURL: string =
    getURL.returnURL("bff-" + env) +
    "/v1/criteria-group/organization/" +
    organizationId +
    "/workspace/" +
    workspaceId +
    "/criteria-group/" +
    criteriaGroupId;

  const response = await request.delete(baseURL, {
    headers: { Authorization: "Bearer " + accessToken },
  });
  const responseBody = await response.json();

  expect(response.status()).toBe(200);
  expect(responseBody.status).toBe("OK");
  expect(responseBody.result).toBe(true);
});
