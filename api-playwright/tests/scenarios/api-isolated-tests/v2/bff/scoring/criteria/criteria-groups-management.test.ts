import { test, expect } from '@playwright/test';
import { createPageInstances } from '../../../../../../utils/helper';
import * as workspaceDetails from '../../../../../../feeder/workspace.json';

const env = process.env.TESTENV || 'dev';

let criteriaGroupId = '';
let criteriaIds = [];

// todo: test on prod

test(
  'API test - Verify the success of fetching criteria and groups, setup the following tests @apiRegressionTest @apitest @criteriaGroup @criteria',
  async ({ request }) => {

    const { getURL, getLoginToken } = createPageInstances();
    const accessToken = await getLoginToken.getAccessToken(request);
    const { organizationId, workspaceIds: workspaceId } = workspaceDetails[env];

    const getAlLCriteriaGroupsUrl: string = getURL.returnURL('bff-' + env) + '/v1/criteria-group/organization/' + organizationId;
    const getAlLCriteriaGroupsResponse = await request.get(getAlLCriteriaGroupsUrl, { headers: { Authorization: 'Bearer ' + accessToken } });
    const getAlLCriteriaGroupsResponseBody = await getAlLCriteriaGroupsResponse.json();

    const getAllCriteriaRequestOptions = {
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + accessToken
      },
    };
    const getAllCriteriaUrl: string = getURL.returnURL('bff-' + env) + '/v1/criteria?workspaceId=' + workspaceId + '&offset=0&perPage=25&sortBy=dateCreated&sortOrder=DESC&platforms=FACEBOOK&mediaTypes=VIDEO,IMAGE,HTML&globalStatuses=DEFAULT';

    const getAllCriteriaResponse = await request.get(getAllCriteriaUrl, getAllCriteriaRequestOptions);
    const getAllCriteriaResponseBody = await getAllCriteriaResponse.json();


    // set up for following tests
    criteriaGroupId = getAlLCriteriaGroupsResponseBody.result[0].id;
    criteriaIds = getAllCriteriaResponseBody.result.slice(0, 3).map((criteria) => criteria.id);

    expect(getAllCriteriaResponse.status()).toBe(200);
    expect(getAllCriteriaResponseBody.status).toBe('OK');
    expect(getAllCriteriaResponseBody.result.length).toBeTruthy();

    expect(getAlLCriteriaGroupsResponse.status()).toBe(200);
    expect(getAlLCriteriaGroupsResponseBody.status).toBe('OK');
    expect(getAlLCriteriaGroupsResponseBody.result.length).toBeTruthy();
  }
)

test(
  'API test - Verify the success of adding criteria to a criteria group @apiRegressionTest @apitest @criteriaGroup @criteria',
  async ({ request }) => {

    const { getURL, getLoginToken } = createPageInstances();
    const accessToken = await getLoginToken.getAccessToken(request);
    const { organizationId, workspaceIds: workspaceId } = workspaceDetails[env];

    const requestOptions = {
      data: {
        criteriaIds: criteriaIds,
        action: 'ADD',
      },
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + accessToken
      },
    };
    const baseURL: string = getURL.returnURL('bff-' + env) + '/v1/criteria-group/organization/' + organizationId + '/workspace/' + workspaceId + '/criteria-group/' + criteriaGroupId + '/criteria';

    const response = await request.post(baseURL, requestOptions);
    const responseBody = await response.json();

    expect(response.status()).toBe(201);
    expect(responseBody.status).toBe('OK');
    expect(responseBody.result).toEqual(true);
  }
)

test(
  'API test - Verify the success of filtering criteria by criteriaGroupIds @apiRegressionTest @apitest @criteriaGroup @criteria',
  async ({ request }) => {
    const { getURL, getLoginToken } = createPageInstances();
    const accessToken = await getLoginToken.getAccessToken(request);
    const { workspaceIds: workspaceId } = workspaceDetails[env];

    const requestOptions = {
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + accessToken
      },
    };
    const url: string = getURL.returnURL('bff-' + env) + '/v1/criteria?workspaceId=' + workspaceId + '&offset=0&perPage=25&sortBy=dateCreated&sortOrder=DESC&platforms=FACEBOOK&mediaTypes=VIDEO,IMAGE,HTML&globalStatuses=DEFAULT&criteriaGroupIds=' + criteriaGroupId;

    const response = await request.get(url, requestOptions);
    const responseBody = await response.json();

    expect(response.status()).toBe(200);
    expect(responseBody.status).toBe('OK');
    expect(responseBody.result.length).toBeGreaterThanOrEqual(3);
  }
)

test(
  'API test - Verify the success of removing criteria from a criteria group @apiRegressionTest @apitest @criteriaGroup @criteria',
  async ({ request }) => {

    const { getURL, getLoginToken } = createPageInstances();
    const accessToken = await getLoginToken.getAccessToken(request);
    const { organizationId, workspaceIds: workspaceId } = workspaceDetails[env];

    const requestOptions = {
      data: {
        criteriaIds: criteriaIds,
        action: 'REMOVE',
      },
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + accessToken
      },
    };
    const baseURL: string = getURL.returnURL('bff-' + env) + '/v1/criteria-group/organization/' + organizationId + '/workspace/' + workspaceId + '/criteria-group/' + criteriaGroupId + '/criteria';

    const response = await request.post(baseURL, requestOptions);
    const responseBody = await response.json();

    expect(response.status()).toBe(201);
    expect(responseBody.status).toBe('OK');
    expect(responseBody.result).toEqual(true);
  }
)


test(
  'API test - Verify the failure of managing a criteria group that does not exist @apiRegressionTest @apitest @criteriaGroup @criteria',
  async ({ request }) => {

    const { getURL, getLoginToken } = createPageInstances();
    const accessToken = await getLoginToken.getAccessToken(request);
    const { organizationId, workspaceIds: workspaceId } = workspaceDetails[env];
    criteriaGroupId = 'invalid-criteria-group-id';

    const requestOptions = {
      data: {
        criteriaIds: [101, 103, 105],
        action: 'REMOVE',
      },
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + accessToken
      },
    };
    const baseURL: string = getURL.returnURL('bff-' + env) + '/v1/criteria-group/organization/' + organizationId + '/workspace/' + workspaceId + '/criteria-group/' + criteriaGroupId + '/criteria';

    const response = await request.post(baseURL, requestOptions);
    const responseBody = await response.json();

    expect(response.status()).toBe(400);
    expect(responseBody.status).toBe('ERROR');
    expect(responseBody.error.message).toBe('Criteria group invalid-criteria-group-id not found for organization ' + organizationId);
  }
)
