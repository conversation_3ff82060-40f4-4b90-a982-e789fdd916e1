import {test, expect, APIResponse} from '@playwright/test';
import {createPageInstances} from '../../../../../../utils/helper';
import {API_RESPONSE_STATUS, SCORING_TAGS, TEST_TYPES} from "../../../../../../utils/util-constants";
import {ErrorResponseBody, ForbiddenResponseBody, SuccessResponseBody} from "../../../../../../utils/scoring.types";

type Scorecard = {
  id: string;
};

let testScenarios = [];
const env = process.env.TESTENV;

if (process.env.TESTENV === 'dev') {
  testScenarios = [
  {
    case: 'success 200 response',
    type: TEST_TYPES.POSITIVE,
    workspaceId: 23142,
    role: 'admin',
    parameters: [{key: 'perPage', value: '1'}],
    verify: async (response: APIResponse) => {
      const responseBody: SuccessResponseBody = await response.json();
      const result: Scorecard[] = responseBody.result;
      expect(response.status()).toBe(API_RESPONSE_STATUS.SUCCESS);
      expect(result.length).toEqual(1);
      expect(responseBody?.pagination?.perPage).toEqual(1);
    }
  },
  {
    case: 'bad request 400 - check scorecard types',
    type: TEST_TYPES.NEGATIVE, workspaceId: 23142,
    role: 'admin',
    response: API_RESPONSE_STATUS.BAD_REQUEST,
    parameters: [{key: 'types', value: 'unknown'}, {key: 'perPage', value: '1'}],
    verify: async (response: APIResponse) => {
      const responseBody: ErrorResponseBody = await response.json();
      expect(response.status()).toBe(API_RESPONSE_STATUS.BAD_REQUEST);
      expect(responseBody.error.message).toContain('Invalid Scorecard type present in the param');
    }
  },
  {
    case: 'unauthorized 403 - invalid role on workspace',
    type: TEST_TYPES.NEGATIVE, workspaceId: 2,
    role: 'none',
    parameters: [{key: 'types', value: 'unknown'}, {key: 'perPage', value: '1'}],
    verify: async (response: APIResponse) => {
      const responseBody: ForbiddenResponseBody = await response.json();
      expect(response.status()).toBe(API_RESPONSE_STATUS.FORBIDDEN);
      expect(responseBody.message).toContain('Forbidden resource');
    }
  }];
} else if (process.env.TESTENV === 'stage') {
  testScenarios = [
  {
    case: 'success 200 response',
    type: TEST_TYPES.POSITIVE,
    workspaceId: 2735,
    role: 'admin',
    response: API_RESPONSE_STATUS.SUCCESS,
    parameters: [{key: 'perPage', value: '1'}],
    verify: async (response: APIResponse) => {
      const responseBody: SuccessResponseBody = await response.json();
      const result: Scorecard[] = responseBody.result;
      expect(response.status()).toBe(API_RESPONSE_STATUS.SUCCESS);
      expect(result.length).toEqual(1);
      expect(responseBody?.pagination?.perPage).toEqual(1);
    }
  },
  {
    case: 'bad request 400 - check scorecard types',
    type: TEST_TYPES.NEGATIVE, workspaceId: 2735,
    role: 'admin',
    parameters: [{key: 'types', value: 'unknown'}, {key: 'perPage', value: '1'}],
    verify: async (response: APIResponse) => {
      const responseBody: ErrorResponseBody = await response.json();
      expect(response.status()).toBe(API_RESPONSE_STATUS.BAD_REQUEST);
      expect(responseBody.error.message).toContain('Invalid Scorecard type present in the param');
    }
  },
  {
    case: 'unauthorized 403 - invalid role on workspace',
    type: TEST_TYPES.NEGATIVE, workspaceId: 2,
    role: 'none',
    parameters: [{key: 'types', value: 'unknown'}, {key: 'perPage', value: '1'}],
    verify: async (response: APIResponse) => {
      const responseBody: ForbiddenResponseBody = await response.json();
      expect(response.status()).toBe(API_RESPONSE_STATUS.FORBIDDEN);
      expect(responseBody.message).toContain('Forbidden resource');
    }
  }];
}

testScenarios.forEach((scenario) => {
  test(`API test GET scorecards, 
   type: ${scenario.type},
   workspace: ${scenario.workspaceId}
   tags: @apiRegressionTest @apitest ${SCORING_TAGS.REGRESSION_TAG}`, async ({request}) => {
    const {getURL, getLoginToken} = createPageInstances();
    const accessToken = await getLoginToken.getAccessToken(request);

    const baseURL: string =
      getURL.returnURL("bff-" + env) + "/v1/scorecard";

    const queryParams = [{key: 'workspaceId', value: scenario.workspaceId}, ...scenario.parameters];

    const urlWithQueryParams =
      getURL.appendQueryParams(baseURL, queryParams);

    const requestOptions = {
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${accessToken}`
      },
    };

    const response = await request.get(urlWithQueryParams, requestOptions)
    await scenario.verify(response);
  });
});
