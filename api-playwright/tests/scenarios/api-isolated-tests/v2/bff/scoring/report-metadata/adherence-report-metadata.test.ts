import { test, expect } from "@playwright/test";
import { createPageInstances } from '../../../../../../utils/helper';
// @ts-ignore
import * as requestsConfig from '../../../../../../payload/bff/scoring-reports/adherenceReportMetadataPayload';
// @ts-ignore
import * as expectedResponse from '../../../../../../payload/bff/scoring-reports/adherenceReportMetadataExpectedResponse';
import { countSubstringOccurrences } from '../../../../../../utils/string-utils';
import { delay } from '../../../../../../utils/common';

const env = process.env.TESTENV || "";

test(
  "API test - Verify the accuracy of PRE_FLIGHT adherence metadata @apiRegressionTest @apitest @adherenceReportMetadata",
  async ({ request }) => {
    await delay(3000);

    const { getURL, getLoginToken } = createPageInstances();
    const accessToken = await getLoginToken.getAccessToken(request);
    const workspaceId = requestsConfig[env].workspaceId;

    const baseURL: string = getURL.returnURL("bff-" + env) + "/v1/reports/adherence/workspace/" + workspaceId + "/metadata/csv";

    const requestOptions = {
      data: requestsConfig[env].preFlightPayload,
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + accessToken
      },
    };

    const response = await request.post(baseURL, requestOptions);
    const responseBody = await response.text();

    const {numberOfLines, platformOneConfig, platformTwoConfig, platformThreeConfig, platformFourConfig} = expectedResponse[env].preFlight;
    const [platformOneCount, platformTwoCount, platformThreeCount, platformFourCount] =
      [platformOneConfig, platformTwoConfig, platformThreeConfig, platformFourConfig]
        .map(platformConfig => platformConfig.value)
        .map(platform => countSubstringOccurrences(responseBody, platform));

    const csvLines = responseBody.split('\n');

    expect(response.status()).toBe(201);
    expect(response.headers()['content-type']).toContain('text/csv');
    expect(csvLines[0]).toContain('"Platform","Brand","Market","Workspace Name","Workspace ID","Campaign Objective","Media ID","Media Name","Link to Download","Link Expiration Date","Media Type","Asset Duration","Date","Overall Score"');
    expect(csvLines.length).toBe(numberOfLines);

    expect(platformOneCount).toBe(platformOneConfig.count);
    expect(platformTwoCount).toBe(platformTwoConfig.count);
    expect(platformThreeCount).toBe(platformThreeConfig.count);
    expect(platformFourCount).toBe(platformFourConfig.count);
  }
);

test(
  "API test - Verify the accuracy of IN_FLIGHT adherence metadata @apiRegressionTest @apitest @adherenceReportMetadata",
  async ({ request }) => {
    await delay(3000);

    const { getURL, getLoginToken } = createPageInstances();
    const accessToken = await getLoginToken.getAccessToken(request);
    const workspaceId = requestsConfig[env].workspaceId;

    const baseURL: string = getURL.returnURL("bff-" + env) + "/v1/reports/adherence/workspace/" + workspaceId + "/metadata/csv";

    const requestOptions = {
      data: requestsConfig[env].inFlightPayload,
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + accessToken
      },
    };

    const response = await request.post(baseURL, requestOptions);
    const responseBody = await response.text();

    const {numberOfLines, platformOneConfig, platformTwoConfig, platformThreeConfig, platformFourConfig} = expectedResponse[env].inFlight;
    const [platformOneCount, platformTwoCount, platformThreeCount, platformFourCount] =
      [platformOneConfig, platformTwoConfig, platformThreeConfig, platformFourConfig]
        .map(platformConfig => platformConfig.value)
        .map(platform => countSubstringOccurrences(responseBody, platform));

    const csvLines = responseBody.split('\n');

    expect(response.status()).toBe(201);
    expect(response.headers()['content-type']).toContain('text/csv');
    expect(csvLines[0]).toContain('"Platform","Brand","Market","Workspace Name","Workspace ID","Ad Account Name","Ad Account ID","Campaign Name","Campaign ID","Campaign Objective","Ad Group/Set Name","Ad Group/Set ID","Media ID","Platform Media ID","Media Name","Link to Download","Link Expiration Date","Media Type","Asset Duration","Date","Impressions","Ad Name","Ad ID","Ad Types","Overall Score"');
    expect(csvLines.length).toBe(numberOfLines);

    expect(platformOneCount).toBe(platformOneConfig.count);
    expect(platformTwoCount).toBe(platformTwoConfig.count);
    expect(platformThreeCount).toBe(platformThreeConfig.count);
    expect(platformFourCount).toBe(platformFourConfig.count);
  }
);

test(
  "API test - Verify that adherence metadata fails when no data available @apiRegressionTest @apitest @adherenceReportMetadata",
  async ({ request }) => {
    await delay(3000);

    const { getURL, getLoginToken } = createPageInstances();
    const accessToken = await getLoginToken.getAccessToken(request);
    const workspaceId = requestsConfig[env].workspaceId;

    const baseURL: string = getURL.returnURL("bff-" + env) + "/v1/reports/adherence/workspace/" + workspaceId + "/metadata/csv";

    const requestOptions = {
        data: requestsConfig[env].emptyPayload,
        headers: {
            'Content-Type': 'application/json',
            Authorization: 'Bearer ' + accessToken
        },
    };

    const response = await request.post(baseURL, requestOptions);
    const responseBody = await response.json();

    expect(response.status()).toBe(500);
    expect(responseBody.status).toBe('ERROR');
    expect(responseBody.result).toBeUndefined();
    expect(responseBody.error.message).toBe('No data found for the given report settings and row selection');
    expect(responseBody.error.identifier).toBe('vidmob.acs-bff-api.Error');
  }
);
