import {expect, test} from "@playwright/test";
import {createPageInstances} from "../../../../../utils/helper";
import * as DataExportOrganizations from "../../../../../payload/bff/data-export/dataExportOrganization.json"
import * as NoReportFilteredRequest
    from "../../../../../payload/bff/data-export/get-by-filter/filterDoesntReturnReports.json"
import * as ReportFilteredRequest
    from "../../../../../payload/bff/data-export/get-by-filter/filterReturnReports.json"

const env = process.env.TESTENV || "";
test(
    "API test - Should return 200 OK with empty report list @apiRegressionTest @apitest @dataExportTest",
    async ({request}) => {
        const {getURL, getLoginToken} = createPageInstances();
        const accessToken = await getLoginToken.getAccessTokenForOrgAdmin(request);
        const organizationId = DataExportOrganizations[env];
        const requestOptions = {
            data: NoReportFilteredRequest[env],
            headers: {
                'Content-Type': 'application/json',
                Authorization: 'Bearer ' + accessToken
            },
        };

        const baseURL: string = getURL.returnURL("bff-" + env) + `/v1/data-exports/organization/${organizationId}/report/filter`;
        const response = await request.post(baseURL, requestOptions)
        const responseBody = await response.json();
        expect(response.status()).toBe(201);
        expect(responseBody.status).toBe('OK');
        expect(responseBody.result.length).toEqual(0);
    }
);

test(
    "API test - Should return 200 OK with report list @apiRegressionTest @apitest @dataExportTest",
    async ({request}) => {
        const {getURL, getLoginToken} = createPageInstances();
        const accessToken = await getLoginToken.getAccessTokenForOrgAdmin(request);
        const organizationId = DataExportOrganizations[env];
        const requestOptions = {
            data: ReportFilteredRequest[env],
            headers: {
                'Content-Type': 'application/json',
                Authorization: 'Bearer ' + accessToken
            },
        };

        const baseURL: string = getURL.returnURL("bff-" + env) + `/v1/data-exports/organization/${organizationId}/report/filter`;
        const response = await request.post(baseURL, requestOptions)
        const responseBody = await response.json();
        expect(response.status()).toBe(201);
        expect(responseBody.status).toBe('OK');
        expect(responseBody.result.length > 0).toBeTruthy();
    }
);