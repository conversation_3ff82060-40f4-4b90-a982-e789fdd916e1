import {expect, test} from "@playwright/test";
import {createPageInstances} from "../../../../../utils/helper";
import * as DataExportOrganizations from "../../../../../../../api-playwright/tests/payload/bff/data-export/dataExportOrganization.json"
const env = process.env.TESTENV || "";
test(
    "API test - Should return 200 OK with correct response with filters @apiRegressionTest @apitest @dataExportTest",
    async ({request}) => {
        const {getURL, getLoginToken} = createPageInstances();
        const accessToken = await getLoginToken.getAccessTokenForOrgAdmin(request);
        const organizationId = DataExportOrganizations[env];
        const requestOptions = {
            headers: {
                'Content-Type': 'application/json',
                Authorization: 'Bearer ' + accessToken
            },
        };

        const baseURL: string = getURL.returnURL("bff-" + env) + `/v1/data-exports/filters/organization/${organizationId}`;
        const response = await request.get(baseURL, requestOptions)
        const responseBody = await response.json();
        expect(response.status()).toBe(200);
        expect(responseBody.status).toBe('OK');
    }
);