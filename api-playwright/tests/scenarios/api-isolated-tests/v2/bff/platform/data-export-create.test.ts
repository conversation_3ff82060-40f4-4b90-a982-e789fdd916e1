import {APIRequestContext, expect, test} from "@playwright/test";
import {createPageInstances} from "../../../../../utils/helper";
import * as DataExportOrganizations from "../../../../../payload/bff/data-export/dataExportOrganization.json";
import * as DataExportIncorrectNameDTO
    from "../../../../../payload/bff/data-export/create/createDataExportWithIncorrectName.json";
import * as DataExportIncorrectWorkspaceDTO
    from "../../../../../payload/bff/data-export/create/createDataExportWithWrongWorkspace.json";
import * as DataExportIncorrectAdAccountDTO
    from "../../../../../payload/bff/data-export/create/createDataExportWithWrongAdAccount.json"
import * as DataExportIncorrectBrandDTO
    from "../../../../../payload/bff/data-export/create/createDataExportWithWrongBrand.json"
import * as DataExportIncorrectMarketDTO
    from "../../../../../payload/bff/data-export/create/createDataExportWithWrongMarket.json"

const env = process.env.TESTENV || "";

const requestDataExportReport = async (request: APIRequestContext, createDto: object) => {
    const {getURL, getLoginToken} = createPageInstances();
    const organizationId = DataExportOrganizations[env];
    const accessToken = await getLoginToken.getAccessTokenForOrgAdmin(request);
    const requestOptions = {
        data: createDto,
        headers: {
            'Content-Type': 'application/json',
            Authorization: 'Bearer ' + accessToken
        },
    }
    const baseURL: string = getURL.returnURL("bff-" + env) + `/v1/data-exports/organization/${organizationId}`;
    return await request.post(baseURL, requestOptions)
}

const basic404Errors = async (request: APIRequestContext, body: object, identifier: string, message: string) => {
    const response = await requestDataExportReport(request, body)
    const responseBody = await response.json();
    expect(response.status()).toBe(404);
    expect(responseBody.status).toBe('ERROR');
    expect(responseBody.result).toBeUndefined();
    expect(responseBody.pagination).toBeUndefined();
    expect(responseBody.error.message).toBe(message);
    expect(responseBody.error.identifier).toBe(identifier);
}

const basic400Errors = async (request: APIRequestContext, body: object, identifier: string, message: string) => {
    const response = await requestDataExportReport(request, body)
    const responseBody = await response.json();
    expect(response.status()).toBe(400);
    expect(responseBody.status).toBe('ERROR');
    expect(responseBody.result).toBeUndefined();
    expect(responseBody.pagination).toBeUndefined();
    expect(responseBody.error.message).toBe(message);
    expect(responseBody.error.identifier).toBe(identifier);
}

test(
    "API test - Should not create a report when report name doesnt pass the regex check @apiRegressionTest @apitest @dataExportTest",
    async ({request}) => {
        const createDto = DataExportIncorrectNameDTO[env]
        const badName = createDto['reportName']
        const expectedMessage = `Invalid report name - ${badName}. Please provide a valid report name with characters, digits, and valid punctuation (-_:.,' ).`
        const expectedIdentifier = "vidmob.acs-bff-api.badrequestexception"
        await basic400Errors(request, createDto, expectedIdentifier, expectedMessage)
    }
);

test(
    "API test - Should not create a report when it tries to run a workspace not associated to organization @apiRegressionTest @apitest @dataExportTest",
    async ({request}) => {
        const createDto = DataExportIncorrectWorkspaceDTO[env]
        const badWorkspace = createDto['workspaces'][0];
        const expectedMessage = `Workspace with id ${badWorkspace} not found`
        const expectedIdentifier = "vidmob.acs-bff-api.notfoundexception"
        await basic404Errors(request, createDto, expectedIdentifier, expectedMessage)
    }
);

test(
    "API test - Should not create a report when it tries to run an ad account not associated to organization @apiRegressionTest @apitest @dataExportTest",
    async ({request}) => {
        const createDto = DataExportIncorrectAdAccountDTO[env]
        const badWorkspace = createDto['adAccounts'][0];
        const expectedMessage = `Ad Account with platform account id ${badWorkspace} not found`
        const expectedIdentifier = "vidmob.acs-bff-api.notfoundexception"
        await basic404Errors(request, createDto, expectedIdentifier, expectedMessage)
    }
);


test(
    "API test - Should not create a report when it tries to run a brand not associated to organization @apiRegressionTest @apitest @dataExportTest",
    async ({request}) => {
        const createDto = DataExportIncorrectBrandDTO[env]
        const badBrand = createDto['brands'][0];
        const expectedMessage = `Brand with id ${badBrand} not found`
        const expectedIdentifier = "vidmob.acs-bff-api.notfoundexception"
        await basic404Errors(request, createDto, expectedIdentifier, expectedMessage)
    }
);

test(
    "API test - Should not create a report when it tries to run a market not associated to organization @apiRegressionTest @apitest @dataExportTest",
    async ({request}) => {
        const createDto = DataExportIncorrectMarketDTO[env]
        const badMarket = createDto['markets'][0];
        const expectedMessage = `Market with id ${badMarket} not found`
        const expectedIdentifier = "vidmob.acs-bff-api.notfoundexception"
        await basic404Errors(request, createDto, expectedIdentifier, expectedMessage)
    }
);