import { expect, test } from "@playwright/test";
import { createPageInstances } from "../../../../../../utils/helper";
import * as ApiKeyManagementOrganizations from "../../../../../../payload/bff/api-key-management/apiKeyManagementOrganization.json";

const env = process.env.TESTENV || "";

test.describe("API Key Management Tests", () => {

    test(
        "API test - Should return 200 OK with 10 records in correct structure @apiKeyManagement @api @positive @status200 @apiKeyManagement-getAll",
        async ({ request }) => {
            const { getURL, getLoginToken } = createPageInstances();
            const accessToken = await getLoginToken.getAccessTokenForOrgAdmin(request);
            const organizationId = ApiKeyManagementOrganizations[env];
            const requestOptions = {
                headers: {
                    'Content-Type': 'application/json',
                    Authorization: 'Bearer ' + accessToken,
                },
            };

            const baseURL: string = getURL.returnURL("bff-" + env) + `/v1/api-key-management/organization/${organizationId}`;
            const response = await request.get(baseURL, requestOptions);
            const responseBody = await response.json();

            expect(response.status()).toBe(200);
            expect(responseBody.status).toBe('OK');
            expect(responseBody.result.length).toEqual(10);

            responseBody.result.forEach((apiKey) => {
                expect(apiKey).toHaveProperty("id");
                expect(apiKey).toHaveProperty("name");
                expect(apiKey).toHaveProperty("organizationId");
                expect(apiKey).toHaveProperty("expirationDate");
                expect(apiKey).toHaveProperty("createdBy");
                expect(apiKey).toHaveProperty("updatedBy");
                expect(apiKey).toHaveProperty("dateCreated");
                expect(apiKey).toHaveProperty("lastUpdated");
                expect(apiKey).toHaveProperty("scopes");

                if (apiKey.lastUsedDate) {
                    expect(apiKey).toHaveProperty("lastUsedDate");
                }

                expect(apiKey.createdBy).toHaveProperty("id");
                expect(apiKey.createdBy).toHaveProperty("displayName");
                expect(apiKey.updatedBy).toHaveProperty("id");
                expect(apiKey.updatedBy).toHaveProperty("displayName");

                expect(Array.isArray(apiKey.scopes)).toBe(true);
                apiKey.scopes.forEach((scope) => {
                    expect(scope).toHaveProperty("scope");
                    expect(scope).toHaveProperty("permission");
                });
            });
        }
    );

    test(
        "API test - Should return 403 Forbidden without a valid token @apiKeyManagement @api @negative @status403 @apiKeyManagement-getAll",
        async ({ request }) => {
            const organizationId = ApiKeyManagementOrganizations[env];
            const { getURL } = createPageInstances();
            const baseURL: string = getURL.returnURL("bff-" + env) + `/v1/api-key-management/organization/${organizationId}`;
            const requestOptions = {
                headers: {
                    'Content-Type': 'application/json',
                    Authorization: 'Bearer invalidToken',
                },
            };

            const response = await request.get(baseURL, requestOptions);
            expect(response.status()).toBe(403);
        }
    );

    test(
        "API test - Should return 403 Forbidden for an invalid organizationId @apiKeyManagement @api @negative @status403 @apiKeyManagement-getAll",
        async ({ request }) => {
            const { getURL, getLoginToken } = createPageInstances();
            const accessToken = await getLoginToken.getAccessTokenForOrgAdmin(request);

            const invalidOrganizationId = "invalid-org-id";
            const requestOptions = {
                headers: {
                    'Content-Type': 'application/json',
                    Authorization: 'Bearer ' + accessToken,
                },
            };

            const baseURL: string = getURL.returnURL("bff-" + env) + `/v1/api-key-management/organization/${invalidOrganizationId}`;
            const response = await request.get(baseURL, requestOptions);

            expect(response.status()).toBe(403);
        }
    );
});