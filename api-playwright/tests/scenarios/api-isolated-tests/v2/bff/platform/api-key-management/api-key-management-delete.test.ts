import { expect, test } from "@playwright/test";
import { createPageInstances } from "../../../../../../utils/helper";
import * as ApiKeyManagementOrganizations from "../../../../../../payload/bff/api-key-management/apiKeyManagementOrganization.json";

const env = process.env.TESTENV || "";

test("Should delete an API key successfully @api-key-management-delete", async ({ request }) => {
    const { getURL, getLoginToken } = createPageInstances();
    const accessToken = await getLoginToken.getAccessTokenForOrgAdmin(request);

    const organizationId = ApiKeyManagementOrganizations[env];
    const requestOptions = {
        headers: {
            "Content-Type": "application/json",
            Authorization: "Bearer " + accessToken,
        },
        data: {
            name: "Scoring read/write",
            scopes: [
                {
                    scope: "scoring",
                    permission: "read_write",
                },
            ],
        },
    };

    const createURL = getURL.returnURL("bff-" + env) + `/v1/api-key-management/organization/${organizationId}`;
    const createResponse = await request.post(createURL, requestOptions);
    const createResponseBody = await createResponse.json();

    expect(createResponse.status()).toBe(201);
    expect(createResponseBody).toHaveProperty("status", "OK");
    expect(createResponseBody.result).toHaveProperty("id");

    const apiKeyId = createResponseBody.result.id;

    const deleteURL = getURL.returnURL("bff-" + env) + `/v1/api-key-management/organization/${organizationId}/${apiKeyId}`;
    const deleteRequestOptions = {
        headers: {
            "Content-Type": "application/json",
            Authorization: "Bearer " + accessToken,
        },
    };

    const deleteResponse = await request.delete(deleteURL, deleteRequestOptions);
    const deleteResponseBody = await deleteResponse.json();

    expect(deleteResponse.status()).toBe(200);
    expect(deleteResponseBody).toHaveProperty("status", "OK");
});


test("Should return 401 Unauthorized for deleting with missing token @api-key-management-delete", async ({ request }) => {
    const organizationId = ApiKeyManagementOrganizations[env];
    const apiKeyId = "dummy-api-key-id";
    const { getURL } = createPageInstances();
    const deleteURL = getURL.returnURL("bff-" + env) + `/v1/api-key-management/organization/${organizationId}/${apiKeyId}`;

    const requestOptions = {
        headers: {
            "Content-Type": "application/json",
        },
    };

    const response = await request.delete(deleteURL, requestOptions);
    expect(response.status()).toBe(401); // Expecting 401 Unauthorized because of missing token
});

test("Should return 403 Forbidden for insufficient permissions @api-key-management @api-key-management-delete", async ({ request }) => {
    const { getURL, getLoginToken } = createPageInstances();
    const accessToken = await getLoginToken.getAccessToken(request);

    const organizationId = ApiKeyManagementOrganizations[env];
    const apiKeyId = "dummy-api-key-id";
    const deleteURL = getURL.returnURL("bff-" + env) + `/v1/api-key-management/organization/${organizationId}/${apiKeyId}`;

    const requestOptions = {
        headers: {
            "Content-Type": "application/json",
            Authorization: "Bearer " + accessToken,
        },
    };

    const response = await request.delete(deleteURL, requestOptions);
    expect(response.status()).toBe(403);
});

test("Should return 404 Not Found for non-existent API key @api-key-management-delete", async ({ request }) => {
    const { getURL, getLoginToken } = createPageInstances();
    const accessToken = await getLoginToken.getAccessTokenForOrgAdmin(request);

    const organizationId = ApiKeyManagementOrganizations[env];
    const nonExistentApiKeyId = "non-existent-api-key-id";
    const deleteURL = getURL.returnURL("bff-" + env) + `/v1/api-key-management/organization/${organizationId}/${nonExistentApiKeyId}`;

    const requestOptions = {
        headers: {
            "Content-Type": "application/json",
            Authorization: "Bearer " + accessToken,
        },
    };

    const response = await request.delete(deleteURL, requestOptions);
    expect(response.status()).toBe(404);
});

test("Should return 404 for empty organizationId @api-key-management-delete", async ({ request }) => {
    const { getURL, getLoginToken } = createPageInstances();
    const accessToken = await getLoginToken.getAccessTokenForOrgAdmin(request);

    const emptyOrganizationId = "";
    const apiKeyId = "dummy-api-key-id";
    const deleteURL = getURL.returnURL("bff-" + env) + `/v1/api-key-management/organization/${emptyOrganizationId}/${apiKeyId}`;

    const requestOptions = {
        headers: {
            "Content-Type": "application/json",
            Authorization: "Bearer " + accessToken,
        },
    };

    const response = await request.delete(deleteURL, requestOptions);
    expect(response.status()).toBe(404);
});