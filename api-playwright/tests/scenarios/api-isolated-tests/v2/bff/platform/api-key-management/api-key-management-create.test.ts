import { expect, test } from "@playwright/test";
import { createPageInstances } from "../../../../../../utils/helper";
import * as ApiKeyManagementOrganizations from "../../../../../../payload/bff/api-key-management/apiKeyManagementOrganization.json";

const env = process.env.TESTENV || "";

test.describe("API Key Management Create Tests", () => {
    test(
        "Should create an API key successfully @apiKeyManagement @apiKeyManagement-create @api @positive @status200",
        async ({ request }) => {
            const { getURL, getLoginToken } = createPageInstances();
            const accessToken = await getLoginToken.getAccessTokenForOrgAdmin(request);

            const organizationId = ApiKeyManagementOrganizations[env];
            const requestOptions = {
                headers: {
                    "Content-Type": "application/json",
                    Authorization: "Bearer " + accessToken,
                },
                data: {
                    name: "Scoring read/write",
                    scopes: [
                        {
                            scope: "scoring",
                            permission: "read_write",
                        },
                    ],
                },
            };

            const createURL = getURL.returnURL("bff-" + env) + `/v1/api-key-management/organization/${organizationId}`;
            const createResponse = await request.post(createURL, requestOptions);
            const createResponseBody = await createResponse.json();

            expect(createResponse.status()).toBe(201);
            expect(createResponseBody).toHaveProperty("status", "OK");
            expect(createResponseBody.result).toHaveProperty("id");

            const apiKeyId = createResponseBody.result.id;

            const deleteURL = getURL.returnURL("bff-" + env) + `/v1/api-key-management/organization/${organizationId}/${apiKeyId}`;
            const deleteRequestOptions = {
                headers: {
                    "Content-Type": "application/json",
                    Authorization: "Bearer " + accessToken,
                },
            };

            const deleteResponse = await request.delete(deleteURL, deleteRequestOptions);
            const deleteResponseBody = await deleteResponse.json();

            expect(deleteResponse.status()).toBe(200);
            expect(deleteResponseBody).toHaveProperty("status", "OK");
        }
    );

    test(
        "Should not create an API key without authorization @apiKeyManagement @apiKeyManagement-create @api @negative @status401",
        async ({ request }) => {
            const { getURL } = createPageInstances();
            const organizationId = ApiKeyManagementOrganizations[env];
            const requestOptions = {
                headers: {
                    "Content-Type": "application/json",
                },
                data: {
                    name: "Scoring read/write",
                    scopes: [
                        {
                            scope: "scoring",
                            permission: "read_write",
                        },
                    ],
                },
            };

            const createURL = getURL.returnURL("bff-" + env) + `/v1/api-key-management/organization/${organizationId}`;
            const createResponse = await request.post(createURL, requestOptions);

            expect(createResponse.status()).toBe(401);
        }
    );

    test(
        "Should not create an API key with invalid scope @apiKeyManagement @apiKeyManagement-create @api @negative @status400",
        async ({ request }) => {
            const { getURL, getLoginToken } = createPageInstances();
            const accessToken = await getLoginToken.getAccessTokenForOrgAdmin(request);

            const organizationId = ApiKeyManagementOrganizations[env];
            const requestOptions = {
                headers: {
                    "Content-Type": "application/json",
                    Authorization: "Bearer " + accessToken,
                },
                data: {
                    name: "Invalid Scope",
                    scopes: [
                        {
                            scope: "invalid_scope",
                            permission: "read_write",
                        },
                    ],
                },
            };

            const createURL = getURL.returnURL("bff-" + env) + `/v1/api-key-management/organization/${organizationId}`;
            const createResponse = await request.post(createURL, requestOptions);

            expect(createResponse.status()).toBe(400);
        }
    );

    test(
        "Should not create an API key without required fields @apiKeyManagement @apiKeyManagement-create @api @negative @status400",
        async ({ request }) => {
            const { getURL, getLoginToken } = createPageInstances();
            const accessToken = await getLoginToken.getAccessTokenForOrgAdmin(request);

            const organizationId = ApiKeyManagementOrganizations[env];
            const requestOptions = {
                headers: {
                    "Content-Type": "application/json",
                    Authorization: "Bearer " + accessToken,
                },
                data: {},
            };

            const createURL = getURL.returnURL("bff-" + env) + `/v1/api-key-management/organization/${organizationId}`;
            const createResponse = await request.post(createURL, requestOptions);

            expect(createResponse.status()).toBe(400);
        }
    );

    test(
        "Should not create an API key for a non-existent organization @apiKeyManagement @apiKeyManagement-create @api @negative @status403",
        async ({ request }) => {
            const { getURL, getLoginToken } = createPageInstances();
            const accessToken = await getLoginToken.getAccessTokenForOrgAdmin(request);

            const nonExistentOrganizationId = "non-existent-org";
            const requestOptions = {
                headers: {
                    "Content-Type": "application/json",
                    Authorization: "Bearer " + accessToken,
                },
                data: {
                    name: "Scoring read/write",
                    scopes: [
                        {
                            scope: "scoring",
                            permission: "read_write",
                        },
                    ],
                },
            };

            const createURL = getURL.returnURL("bff-" + env) + `/v1/api-key-management/organization/${nonExistentOrganizationId}`;
            const createResponse = await request.post(createURL, requestOptions);

            expect(createResponse.status()).toBe(403);
        }
    );

    test(
        "Should not create an API key with an invalid expiration date @apiKeyManagement @apiKeyManagement-create @api @negative @status400",
        async ({ request }) => {
            const { getURL, getLoginToken } = createPageInstances();
            const accessToken = await getLoginToken.getAccessTokenForOrgAdmin(request);

            const organizationId = ApiKeyManagementOrganizations[env];
            const requestOptions = {
                headers: {
                    "Content-Type": "application/json",
                    Authorization: "Bearer " + accessToken,
                },
                data: {
                    name: "Scoring read/write",
                    expirationDate: "invalid-date",
                    scopes: [
                        {
                            scope: "scoring",
                            permission: "read_write",
                        },
                    ],
                },
            };

            const createURL = getURL.returnURL("bff-" + env) + `/v1/api-key-management/organization/${organizationId}`;
            const createResponse = await request.post(createURL, requestOptions);

            expect(createResponse.status()).toBe(400);
        }
    );
});