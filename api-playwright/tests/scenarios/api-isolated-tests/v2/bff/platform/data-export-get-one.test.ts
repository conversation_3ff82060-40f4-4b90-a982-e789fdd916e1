import {expect, test} from "@playwright/test";
import {createPageInstances} from "../../../../../utils/helper";
import * as DataExportOrganizations from "../../../../../../../api-playwright/tests/payload/bff/data-export/dataExportOrganization.json"
import * as DataExportSuccessfulReportIds
    from "../../../../../../../api-playwright/tests/payload/bff/data-export/get-one/getOneReportSuccessfulResponseIds.json"
import * as DataExportNonExistingReportIds
    from "../../../../../../../api-playwright/tests/payload/bff/data-export/get-one/reportNotFoundIds.json"

const env = process.env.TESTENV || "";
test(
    "API test - Should return 200 OK with correct response with a downloadable record @apiRegressionTest @apitest @dataExportTest",
    async ({request}) => {
        const {getURL, getLoginToken} = createPageInstances();
        const accessToken = await getLoginToken.getAccessTokenForOrgAdmin(request);
        const organizationId = DataExportOrganizations[env];
        const reportId = DataExportSuccessfulReportIds[env];
        const requestOptions = {
            headers: {
                'Content-Type': 'application/json',
                Authorization: 'Bearer ' + accessToken
            },
        };

        const baseURL: string = getURL.returnURL("bff-" + env) + `/v1/data-exports/organization/${organizationId}/report/${reportId}`;
        const response = await request.get(baseURL, requestOptions)
        const responseBody = await response.json();

        expect(response.status()).toBe(200);
        expect(responseBody.status).toBe('OK');
        expect(responseBody.result.recordCount).toBeGreaterThan(0);
        expect(responseBody.result.downloadUrl).toBeDefined();
    }
);

test(
    "API test - Should return 404 Error with non existing record @apiRegressionTest @apitest @dataExportTest",
    async ({request}) => {
        const {getURL, getLoginToken} = createPageInstances();
        const accessToken = await getLoginToken.getAccessTokenForOrgAdmin(request);
        const organizationId = DataExportOrganizations[env];
        const reportId = DataExportNonExistingReportIds[env];
        const requestOptions = {
            headers: {
                'Content-Type': 'application/json',
                Authorization: 'Bearer ' + accessToken
            },
        };

        const baseURL: string = getURL.returnURL("bff-" + env) + `/v1/data-exports/organization/${organizationId}/report/${reportId}`;
        const response = await request.get(baseURL, requestOptions)
        const responseBody = await response.json();

        expect(response.status()).toBe(404);
        expect(responseBody.status).toBe('ERROR');
        expect(responseBody.error.identifier).toBe("vidmob.acs-bff-api.notfoundexception");
        expect(responseBody.error.message).toBe("Report not found");
    }
);