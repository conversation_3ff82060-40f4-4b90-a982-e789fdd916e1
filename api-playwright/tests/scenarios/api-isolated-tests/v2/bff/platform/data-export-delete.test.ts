import {expect, test} from "@playwright/test";
import {createPageInstances} from "../../../../../utils/helper";
import * as DataExportOrganizations from "../../../../../payload/bff/data-export/dataExportOrganization.json";
import * as DataExportNonExistingReportIds from "../../../../../payload/bff/data-export/get-one/reportNotFoundIds.json";

const env = process.env.TESTENV || "";
test(
    "API test - Should return 404 Error Record Not Found when deleting @apiRegressionTest @apitest @dataExportTest",
    async ({request}) => {
        const {getURL, getLoginToken} = createPageInstances();
        const accessToken = await getLoginToken.getAccessTokenForOrgAdmin(request);
        const organizationId = DataExportOrganizations[env];
        const reportId = DataExportNonExistingReportIds[env];
        const requestOptions = {
            headers: {
                'Content-Type': 'application/json',
                Authorization: 'Bearer ' + accessToken
            },
        };

        const baseURL: string = getURL.returnURL("bff-" + env) + `/v1/data-exports/organization/${organizationId}/report/${reportId}`;
        const response = await request.delete(baseURL, requestOptions)
        const responseBody = await response.json()
        expect(response.status()).toBe(404);
        expect(responseBody.status).toBe(`ERROR`)
        expect(responseBody.error.message).toBe(`Report ${reportId} doesn't exist for the org ${organizationId}.`)
    }
);