import { test, expect } from "@playwright/test";
import { createPageInstances } from "../../../../../utils/helper";
// @ts-ignore
import * as requestsConfig from "../../../../../payload/bff/getCreativeManagerPayload.json";
// @ts-ignore
import * as expectedResponse from "../../../../../payload/bff/getCreativeManagerExpectedResponse.json";

const env = process.env.TESTENV || "";

test(
  "API test - Verify the success of fetching creatives and creatives csv @apiRegressionTest @apitest @creativeManager",
  async ({ request }) => {
    const { getURL, getLoginToken } = createPageInstances();
    const accessToken = await getLoginToken.getAccessToken(request);
    const { organizationId, basePayload, creativeSearch } = requestsConfig[env];

    const getAllUrl: string = getURL.returnURL("bff-" + env) + "/v1/creative-manager/organization/" + organizationId + "/creatives";
    const getCSVUrl: string = getAllUrl + "/csv";
    const requestOptions = {
      data: {
        ...basePayload,
        search: creativeSearch,
      },
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + accessToken
      },
    };

    const [getAllResponse, getCSVResponse] = await Promise.all([request.post(getAllUrl, requestOptions), request.post(getCSVUrl, requestOptions)]);
    const getAllResponseBody = await getAllResponse.json();
    const getCSVResponseBody = await getCSVResponse.text();

    const {totalCreativeCount, firstCreative} = expectedResponse[env];

    expect(getAllResponse.status()).toBe(201);
    expect(getAllResponseBody.status).toBe('OK');
    expect(getAllResponseBody.pagination.totalSize).toBe(totalCreativeCount);
    expect(getAllResponseBody.result[0]).toStrictEqual(firstCreative);

    const numberOfCSVRows = getCSVResponseBody.split('\n').length;
    expect(getCSVResponse.status()).toBe(201);
    expect(numberOfCSVRows).toStrictEqual(totalCreativeCount + 2);
  }
);

test(
  "API test - Verify the success of fetching ads and ads csv @apiRegressionTest @apitest @creativeManager",
  async ({ request }) => {
    const { getURL, getLoginToken } = createPageInstances();
    const accessToken = await getLoginToken.getAccessToken(request);
    const { organizationId, basePayload, adSearch } = requestsConfig[env];

    const getAllUrl: string = getURL.returnURL("bff-" + env) + "/v1/creative-manager/organization/" + organizationId + "/ads";
    const getCSVUrl: string = getAllUrl + "/csv";
    const requestOptions = {
      data: {
        ...basePayload,
        search: adSearch,
      },
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + accessToken
      },
    };

    const [getAllResponse, getCSVResponse] = await Promise.all([request.post(getAllUrl, requestOptions), request.post(getCSVUrl, requestOptions)]);
    const getAllResponseBody = await getAllResponse.json();
    const getCSVResponseBody = await getCSVResponse.text();

    const {totalAdCount, firstAd} = expectedResponse[env];

    expect(getAllResponse.status()).toBe(201);
    expect(getAllResponseBody.status).toBe('OK');
    expect(getAllResponseBody.pagination.totalSize).toBe(totalAdCount);
    expect(getAllResponseBody.result[0]).toStrictEqual(firstAd);

    const numberOfCSVRows = getCSVResponseBody.split('\n').length;
    expect(getCSVResponse.status()).toBe(201);
    expect(numberOfCSVRows).toStrictEqual(totalAdCount + 2);
  }
);

test(
  "API test - Verify api fails if attempting to fetch all creatives with inapplicable search fields @apiRegressionTest @apitest @creativeManager",
  async ({ request }) => {
    const { getURL, getLoginToken } = createPageInstances();
    const accessToken = await getLoginToken.getAccessToken(request);
    const { organizationId, basePayload, adSearch } = requestsConfig[env];

    const getAllUrl: string = getURL.returnURL("bff-" + env) + "/v1/creative-manager/organization/" + organizationId + "/creatives";
    const requestOptions = {
      data: {
        ...basePayload,
        search: adSearch,
      },
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + accessToken
      },
    };

    const getAllResponse = await request.post(getAllUrl, requestOptions);
    const getAllResponseBody = await getAllResponse.json();

    expect(getAllResponse.status()).toBe(500);
    expect(getAllResponseBody.status).toBe('ERROR');
    expect(getAllResponseBody.error.message).toBe("Invalid search fields: creativeCount for dimension creative");
  }
);
