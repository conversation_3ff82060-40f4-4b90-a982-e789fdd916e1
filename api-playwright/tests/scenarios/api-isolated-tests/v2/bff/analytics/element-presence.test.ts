/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-var-requires */
import { test, expect } from "@playwright/test";
import { createPageInstances } from "../../../../../utils/helper";
const payload = require("../../../../../payload/bff/eprPayload.json");
const expectedResponse = require("../../../../../payload/bff/eprExpectedResponse.json");

const env = process.env.TESTENV;

test("API test - Verify the empty response from element presence media search api @apiRegressionTest @apitest @elementPresenceMediaSearch", async ({
  request,
}) => {
  const { getURL, getLoginToken } = createPageInstances();

  const accessToken = await getLoginToken.getAccessToken(request);

  const baseURL: string =
    getURL.returnURL("bff-" + env) + "/v1/analytics/platform-media/search";

  const requestOptions = {
    data: payload[env]["empty"],
    headers: {
      "Content-Type": "application/json",
      Authorization: "Bearer " + accessToken,
    },
  };
  const response = await request.post(baseURL, requestOptions);

  expect(response.status()).toBe(201);

  const responseBody = await response.json();
  expect(responseBody.status).toBe("OK");
  expect(responseBody.result.platformMediaIds).toHaveLength(0);
});

test("API test - Verify the not-empty response from element presence media search api @apiRegressionTest @apitest @elementPresenceMediaSearch", async ({
  request,
}) => {
  const { getURL, getLoginToken } = createPageInstances();

  const accessToken = await getLoginToken.getAccessToken(request);

  const baseURL: string =
    getURL.returnURL("bff-" + env) + "/v1/analytics/platform-media/search";

  const payloadWithoutAdvancedFilters = {
    ...payload[env]["notEmpty"],
    advancedFilters: undefined,
  };
  const requestOptions = {
    data: payloadWithoutAdvancedFilters,
    headers: {
      "Content-Type": "application/json",
      Authorization: "Bearer " + accessToken,
    },
  };
  const response = await request.post(baseURL, requestOptions);
  const responseBody = await response.json();

  expect(response.status()).toBe(201);
  expect(responseBody.status).toBe("OK");
  expect(responseBody.result.platformMediaIds.sort()).toStrictEqual(
    expectedResponse[env]["notEmptyPlatformMediaIds"].sort()
  );
});

test("API test - Verify the element presence media search api response with advanced filters @apiRegressionTest @apitest @elementPresenceMediaSearch", async ({
  request,
}) => {
  const { getURL, getLoginToken } = createPageInstances();

  const accessToken = await getLoginToken.getAccessToken(request);

  const baseURL: string =
    getURL.returnURL("bff-" + env) + "/v1/analytics/platform-media/search";

  const requestOptions = {
    data: payload[env]["notEmpty"],
    headers: {
      "Content-Type": "application/json",
      Authorization: "Bearer " + accessToken,
    },
  };
  const response = await request.post(baseURL, requestOptions);
  const responseBody = await response.json();

  expect(response.status()).toBe(201);
  expect(responseBody.status).toBe("OK");
  expect(responseBody.result.platformMediaIds).toStrictEqual(
    expectedResponse[env]["withAnalyticsFiltersPlatformMediaIds"]
  );
});

test("API test - Verify error response from element presence media search api with invalid ad account ids @apiRegressionTest @apitest @elementPresenceMediaSearch", async ({
  request,
}) => {
  const { getURL, getLoginToken } = createPageInstances();

  const accessToken = await getLoginToken.getAccessToken(request);

  const baseURL: string =
    getURL.returnURL("bff-" + env) + "/v1/analytics/platform-media/search";

  const payloadWithInvalidAdAccountIds = {
    ...payload[env]["notEmpty"],
    adAccountIds: ["**********"],
  };
  const requestOptions = {
    data: payloadWithInvalidAdAccountIds,
    headers: {
      "Content-Type": "application/json",
      Authorization: "Bearer " + accessToken,
    },
  };
  const response = await request.post(baseURL, requestOptions);
  const responseBody = await response.json();

  expect(response.status()).toBe(400);
  expect(responseBody.status).toBe("ERROR");
  expect(responseBody.result).toBe(undefined);
  expect(responseBody.error.type).toBe("BADREQUESTEXCEPTION");
  expect(
    responseBody.error.message.includes(
      "Ad account ids are not valid for organization"
    )
  ).toBeTruthy();
});
