/*import {APIResponse, expect, test} from '@playwright/test';
import {createPageInstances} from '../../../../../../utils/helper';
import {API_RESPONSE_STATUS, TEST_TYPES} from '../../../../../../utils/util-constants';
// @ts-ignore
import * as getReport from '../../../../../../payload/bff/criteria-performance/getReportX';
import {SuccessResponseBody} from '../../../../../../utils/scoring.types';
import {CriteriaPerformanceReportRow} from '../../../../../../utils/analytics.types';
import {assertCriteriaPerformanceResultShape} from "./verifyUtils";

const env = process.env.TESTENV || 'dev';

const organizationIdPerEnv = {
    dev: '27a7e882-43de-4bfa-8f53-3b62875b8432',
}

const KPI_TOTAL_VIDEO_PLAYBACK_STARTS_ID_DEV = '46';
const KPI_VTR_25_ID_DEV = '37';
const KPI_VIDEO_TOTAL_CLICKS_ID_DEV = '45';

const singleKpiIdPerEnv = {
    dev: KPI_TOTAL_VIDEO_PLAYBACK_STARTS_ID_DEV,
};

const singleKpiIdNoDataPerEnv = {
    dev: KPI_VIDEO_TOTAL_CLICKS_ID_DEV,
};

const multipleKpiIdsPerEnv = {
    dev: [KPI_TOTAL_VIDEO_PLAYBACK_STARTS_ID_DEV, KPI_VTR_25_ID_DEV, KPI_VIDEO_TOTAL_CLICKS_ID_DEV],
};

const campaignIdPerEnv = {
    dev: 'gf99n',
};

const objectiveIdPerEnv = {
    dev: 'WEBSITE_CLICKS',
};

const adIdPerEnv = {
    dev: 'c5b9n',
};

// const assertCriteriaPerformanceResultShape = (criteriaPerformanceReport: CriteriaPerformanceReportRow[]): void => {
//     expect(criteriaPerformanceReport).toBeDefined();
//     expect(Array.isArray(criteriaPerformanceReport)).toBeTruthy();
//     expect(criteriaPerformanceReport.length).toBeGreaterThan(0);
//     const filters = getReport[env].filters;
//     criteriaPerformanceReport.forEach((criteriaPerformanceReportRow, index) => {
//         expect(typeof criteriaPerformanceReportRow.id).toBe('string');
//         expect(criteriaPerformanceReportRow.id).toEqual(filters.criteriaGroupIds[index]);
//         expect(typeof criteriaPerformanceReportRow.identifier).toBe('string');
//         expect(criteriaPerformanceReportRow.id).toEqual(filters.criteriaGroupIds[index]);
//         const onlyResultParameters = JSON.stringify(criteriaPerformanceReportRow.parameters);
//         const criteriaGroupId = `${criteriaPerformanceReportRow.identifier}:${onlyResultParameters}`;
//         expect(criteriaGroupId).toContain(filters.criteriaGroupIds[index]);
//         expect(typeof criteriaPerformanceReportRow.parameters).toBe('object');
//         expect(typeof criteriaPerformanceReportRow.performanceByKpiId).toBe('object');
//         expect(typeof criteriaPerformanceReportRow.mediaCount).toBe('object');
//         expect(typeof criteriaPerformanceReportRow.impressions).toBe('object');
//     });
// };

const testScenarios = [
    {
        case: 'no KPI: success 201 response, actual values',
        type: TEST_TYPES.POSITIVE,
        payload: getReport[env],
        verify: async (response: APIResponse) => {
            const responseBody: SuccessResponseBody = await response.json();
            expect(response.status()).toBe(API_RESPONSE_STATUS.CREATED);
            assertCriteriaPerformanceResultShape(responseBody.result, getReport[env].filters);
            responseBody.result.forEach((responseRow) => {
                expect(responseRow).toBeDefined();
                expect(responseRow.performanceByKpiId).toEqual({});
                expect(responseRow.mediaCount.met).toEqual(1);
                expect(responseRow.mediaCount.failed).toEqual(33);
                expect(responseRow.mediaCount.percentageMet).toEqual(2.941176470588235);
                expect(responseRow.impressions.met).toEqual(65854);
                expect(responseRow.impressions.failed).toEqual(4642540);
                expect(responseRow.impressions.total).toEqual(4708394);
            });
        },
    },
    {
        case: 'no KPI: success 201, no data',
        type: TEST_TYPES.POSITIVE,
        payload: {
            ...getReport[env],
            "filters": {
                "criteriaGroupIds": ['unknown']
            },
        },
        verify: async (response: APIResponse) => {
            const responseBody: SuccessResponseBody = await response.json();
            expect(response.status()).toBe(API_RESPONSE_STATUS.CREATED);
            expect(responseBody.result).toEqual([]);
        },
    },
    {
        case: '1 KPI: success 201 response, actual values',
        type: TEST_TYPES.POSITIVE,
        payload: {
            ...getReport[env],
            kpiIds: [singleKpiIdPerEnv[env]],
        },
        verify: async (response: APIResponse) => {
            const responseBody: SuccessResponseBody = await response.json();
            expect(response.status()).toBe(API_RESPONSE_STATUS.CREATED);
            assertCriteriaPerformanceResultShape(responseBody.result, getReport[env].filters);

            responseBody.result.forEach((responseRow) => {
                const kpiId = singleKpiIdPerEnv[env];
                expect(responseRow).toBeDefined();
                expect(Object.keys(responseRow.performanceByKpiId[kpiId])).toHaveLength(4);
                expect(responseRow.performanceByKpiId[kpiId].met).toEqual(89.039390166125);
                expect(responseRow.performanceByKpiId[kpiId].failed).toEqual(87.381067260594);
                expect(responseRow.performanceByKpiId[kpiId].percentLift).toEqual(1.8978057347199029);
                expect(responseRow.performanceByKpiId[kpiId].isStatisticallySignificant).toEqual(true);
            });
        },
    },
    {
        case: '1 KPI: success 201 response, no data',
        type: TEST_TYPES.POSITIVE,
        payload: {
            ...getReport[env],
            kpiIds: [singleKpiIdNoDataPerEnv[env]],
        },
        verify: async (response: APIResponse) => {
            const responseBody: SuccessResponseBody = await response.json();
            expect(response.status()).toBe(API_RESPONSE_STATUS.CREATED);
            assertCriteriaPerformanceResultShape(responseBody.result, getReport[env].filters);

            responseBody.result.forEach((responseRow) => {
                const kpiId = singleKpiIdNoDataPerEnv[env];
                expect(responseRow).toBeDefined();
                expect(Object.keys(responseRow.performanceByKpiId[kpiId])).toHaveLength(4);
                expect(responseRow.performanceByKpiId[kpiId].met).toEqual(null);
                expect(responseRow.performanceByKpiId[kpiId].failed).toEqual(null);
                expect(responseRow.performanceByKpiId[kpiId].percentLift).toEqual(null);
                expect(responseRow.performanceByKpiId[kpiId].isStatisticallySignificant).toEqual(false);
            });
        },
    },
    {
        case: 'multiple KPIs: success 201 response',
        type: TEST_TYPES.POSITIVE,
        payload: {
            ...getReport[env],
            kpiIds: multipleKpiIdsPerEnv[env],
        },
        verify: async (response: APIResponse) => {
            const responseBody: SuccessResponseBody = await response.json();
            expect(response.status()).toBe(API_RESPONSE_STATUS.CREATED);
            assertCriteriaPerformanceResultShape(responseBody.result, getReport[env].filters);

            responseBody.result.forEach((responseRow) => {
                expect(responseRow).toBeDefined();
                expect(Object.keys(responseRow.performanceByKpiId)).toHaveLength(3);
            });
        },
    },
    {
        case: 'Campaign Identifier filter: success 201 response, actual values',
        type: TEST_TYPES.POSITIVE,
        payload: {
            ...getReport[env],
            kpiIds: multipleKpiIdsPerEnv[env],
            advancedFilters: {
                campaignIdentifiers: [campaignIdPerEnv[env]],
            },
        },
        verify: async (response: APIResponse) => {
            const responseBody: SuccessResponseBody = await response.json();
            expect(response.status()).toBe(API_RESPONSE_STATUS.CREATED);
            assertCriteriaPerformanceResultShape(responseBody.result, getReport[env].filters);
            const firstKpiId = multipleKpiIdsPerEnv[env][0];
            const lastKpiId = multipleKpiIdsPerEnv[env][multipleKpiIdsPerEnv[env].length - 1];

            responseBody.result.forEach((responseRow) => {
                expect(responseRow).toBeDefined();
                expect(responseRow.mediaCount.met).toEqual(0);
                expect(responseRow.mediaCount.failed).toEqual(2);
                expect(responseRow.mediaCount.percentageMet).toEqual(0);
                expect(responseRow.impressions.met).toEqual(0);
                expect(responseRow.impressions.failed).toEqual(145830);
                expect(responseRow.impressions.total).toEqual(145830);
                expect(responseRow.performanceByKpiId[firstKpiId].met).toEqual(null);
                expect(responseRow.performanceByKpiId[firstKpiId].failed).toEqual(86.667352396626);
                expect(responseRow.performanceByKpiId[firstKpiId].percentLift).toEqual(null);
                expect(responseRow.performanceByKpiId[firstKpiId].isStatisticallySignificant).toEqual(true);
                expect(responseRow.performanceByKpiId[lastKpiId].met).toEqual(null);
                expect(responseRow.performanceByKpiId[lastKpiId].failed).toEqual(null);
                expect(responseRow.performanceByKpiId[lastKpiId].percentLift).toEqual(null);
                expect(responseRow.performanceByKpiId[lastKpiId].isStatisticallySignificant).toEqual(false);
            });
        },
    },
    {
        case: 'Campaign Objective filter: success 201 response',
        type: TEST_TYPES.POSITIVE,
        payload: {
            ...getReport[env],
            kpiIds: multipleKpiIdsPerEnv[env],
            advancedFilters: {
                campaignObjectives: [objectiveIdPerEnv[env]],
            },
        },
        verify: async (response: APIResponse) => {
            const responseBody: SuccessResponseBody = await response.json();
            expect(response.status()).toBe(API_RESPONSE_STATUS.CREATED);
            assertCriteriaPerformanceResultShape(responseBody.result, getReport[env].filters);

            responseBody.result.forEach((responseRow) => {
                expect(responseRow).toBeDefined();
                expect(Object.keys(responseRow.performanceByKpiId)).toHaveLength(3);
            });
        },
    },
    {
        case: 'Ad Impression filter: success 201 response, actual values',
        type: TEST_TYPES.POSITIVE,
        payload: {
            ...getReport[env],
            kpiIds: multipleKpiIdsPerEnv[env],
            advancedFilters: {
                adImpressions: {greaterThanOrEqualTo: 1000, lessThanOrEqualTo: 5000},
            },
        },
        verify: async (response: APIResponse) => {
            const responseBody: SuccessResponseBody = await response.json();
            expect(response.status()).toBe(API_RESPONSE_STATUS.CREATED);
            assertCriteriaPerformanceResultShape(responseBody.result, getReport[env].filters);
            const firstKpiId = multipleKpiIdsPerEnv[env][0];
            const lastKpiId = multipleKpiIdsPerEnv[env][multipleKpiIdsPerEnv[env].length - 1];

            responseBody.result.forEach((responseRow) => {
                expect(responseRow).toBeDefined();
                expect(responseRow.mediaCount.met).toEqual(0);
                expect(responseRow.mediaCount.failed).toEqual(9);
                expect(responseRow.mediaCount.percentageMet).toEqual(0);
                expect(responseRow.impressions.met).toEqual(0);
                expect(responseRow.impressions.failed).toEqual(17499);
                expect(responseRow.impressions.total).toEqual(17499);
                expect(responseRow.performanceByKpiId[firstKpiId].met).toEqual(null);
                expect(responseRow.performanceByKpiId[firstKpiId].failed).toEqual(97.005543173896);
                expect(responseRow.performanceByKpiId[firstKpiId].percentLift).toEqual(null);
                expect(responseRow.performanceByKpiId[firstKpiId].isStatisticallySignificant).toEqual(true);
                expect(responseRow.performanceByKpiId[lastKpiId].met).toEqual(null);
                expect(responseRow.performanceByKpiId[lastKpiId].failed).toEqual(null);
                expect(responseRow.performanceByKpiId[lastKpiId].percentLift).toEqual(null);
                expect(responseRow.performanceByKpiId[lastKpiId].isStatisticallySignificant).toEqual(false);
            });
        },
    },
    {
        case: 'Ad Identifier filter: success 201 response, actual values',
        type: TEST_TYPES.POSITIVE,
        payload: {
            ...getReport[env],
            kpiIds: multipleKpiIdsPerEnv[env],
            advancedFilters: {
                adIdentifiers: [adIdPerEnv[env]],
            },
        },
        verify: async (response: APIResponse) => {
            const responseBody: SuccessResponseBody = await response.json();
            expect(response.status()).toBe(API_RESPONSE_STATUS.CREATED);
            assertCriteriaPerformanceResultShape(responseBody.result, getReport[env].filters);
            const firstKpiId = multipleKpiIdsPerEnv[env][0];
            const secondKpiId = multipleKpiIdsPerEnv[env][1];
            const lastKpiId = multipleKpiIdsPerEnv[env][multipleKpiIdsPerEnv[env].length - 1];

            responseBody.result.forEach((responseRow) => {
                expect(responseRow).toBeDefined();
                expect(responseRow.mediaCount.met).toEqual(0);
                expect(responseRow.mediaCount.failed).toEqual(2);
                expect(responseRow.mediaCount.percentageMet).toEqual(0);
                expect(responseRow.impressions.met).toEqual(0);
                expect(responseRow.impressions.failed).toEqual(155325);
                expect(responseRow.impressions.total).toEqual(155325);
                expect(responseRow.performanceByKpiId[firstKpiId].met).toEqual(null);
                expect(responseRow.performanceByKpiId[firstKpiId].failed).toEqual(93.097054563013);
                expect(responseRow.performanceByKpiId[firstKpiId].percentLift).toEqual(null);
                expect(responseRow.performanceByKpiId[firstKpiId].isStatisticallySignificant).toEqual(true);
                expect(responseRow.performanceByKpiId[secondKpiId].met).toEqual(null);
                expect(responseRow.performanceByKpiId[secondKpiId].failed).toEqual(58.360856269113);
                expect(responseRow.performanceByKpiId[secondKpiId].percentLift).toEqual(null);
                expect(responseRow.performanceByKpiId[secondKpiId].isStatisticallySignificant).toEqual(true);
                expect(responseRow.performanceByKpiId[lastKpiId].met).toEqual(null);
                expect(responseRow.performanceByKpiId[lastKpiId].failed).toEqual(null);
                expect(responseRow.performanceByKpiId[lastKpiId].percentLift).toEqual(null);
                expect(responseRow.performanceByKpiId[lastKpiId].isStatisticallySignificant).toEqual(false);
            });
        },
    },
    {
        case: 'Created by Vidmob filter (true): success 201 response, actual values',
        type: TEST_TYPES.POSITIVE,
        payload: {
            ...getReport[env],
            kpiIds: multipleKpiIdsPerEnv[env],
            advancedFilters: {
                createdByVidmob: true,
            },
        },
        verify: async (response: APIResponse) => {
            const responseBody: SuccessResponseBody = await response.json();
            expect(response.status()).toBe(API_RESPONSE_STATUS.CREATED);
            assertCriteriaPerformanceResultShape(responseBody.result, getReport[env].filters);
            const firstKpiId = multipleKpiIdsPerEnv[env][0];
            const secondKpiId = multipleKpiIdsPerEnv[env][1];
            const lastKpiId = multipleKpiIdsPerEnv[env][multipleKpiIdsPerEnv[env].length - 1];

            responseBody.result.forEach((responseRow) => {
                expect(responseRow).toBeDefined();
                expect(responseRow.mediaCount.met).toEqual(1);
                expect(responseRow.mediaCount.failed).toEqual(4);
                expect(responseRow.mediaCount.percentageMet).toEqual(20);
                expect(responseRow.impressions.met).toEqual(65854);
                expect(responseRow.impressions.failed).toEqual(1654429);
                expect(responseRow.impressions.total).toEqual(1720283);
                expect(responseRow.performanceByKpiId[firstKpiId].met).toEqual(89.039390166125);
                expect(responseRow.performanceByKpiId[firstKpiId].failed).toEqual(90.354799148226);
                expect(responseRow.performanceByKpiId[firstKpiId].percentLift).toEqual(-1.455826358424058);
                expect(responseRow.performanceByKpiId[firstKpiId].isStatisticallySignificant).toEqual(true);
                expect(responseRow.performanceByKpiId[secondKpiId].met).toEqual(64.204148571081);
                expect(responseRow.performanceByKpiId[secondKpiId].failed).toEqual(56.567008919694);
                expect(responseRow.performanceByKpiId[secondKpiId].percentLift).toEqual(13.501049104839824);
                expect(responseRow.performanceByKpiId[secondKpiId].isStatisticallySignificant).toEqual(true);
                expect(responseRow.performanceByKpiId[lastKpiId].met).toEqual(null);
                expect(responseRow.performanceByKpiId[lastKpiId].failed).toEqual(null);
                expect(responseRow.performanceByKpiId[lastKpiId].percentLift).toEqual(null);
                expect(responseRow.performanceByKpiId[lastKpiId].isStatisticallySignificant).toEqual(false);
            });
        },
    },
    {
        case: 'Created by Vidmob filter (false): success 201 response, actual values',
        type: TEST_TYPES.POSITIVE,
        payload: {
            ...getReport[env],
            kpiIds: multipleKpiIdsPerEnv[env],
            advancedFilters: {
                createdByVidmob: false,
            },
        },
        verify: async (response: APIResponse) => {
            const responseBody: SuccessResponseBody = await response.json();
            expect(response.status()).toBe(API_RESPONSE_STATUS.CREATED);
            assertCriteriaPerformanceResultShape(responseBody.result, getReport[env].filters);
            const firstKpiId = multipleKpiIdsPerEnv[env][0];
            const secondKpiId = multipleKpiIdsPerEnv[env][1];
            const lastKpiId = multipleKpiIdsPerEnv[env][multipleKpiIdsPerEnv[env].length - 1];

            responseBody.result.forEach((responseRow) => {
                expect(responseRow).toBeDefined();
                expect(responseRow.mediaCount.met).toEqual(0);
                expect(responseRow.mediaCount.failed).toEqual(29);
                expect(responseRow.mediaCount.percentageMet).toEqual(0);
                expect(responseRow.impressions.met).toEqual(0);
                expect(responseRow.impressions.failed).toEqual(2988111);
                expect(responseRow.impressions.total).toEqual(2988111);
                expect(responseRow.performanceByKpiId[firstKpiId].met).toEqual(null);
                expect(responseRow.performanceByKpiId[firstKpiId].failed).toEqual(85.734599551355);
                expect(responseRow.performanceByKpiId[firstKpiId].percentLift).toEqual(null);
                expect(responseRow.performanceByKpiId[firstKpiId].isStatisticallySignificant).toEqual(true);
                expect(responseRow.performanceByKpiId[secondKpiId].met).toEqual(null);
                expect(responseRow.performanceByKpiId[secondKpiId].failed).toEqual(48.875527047021);
                expect(responseRow.performanceByKpiId[secondKpiId].percentLift).toEqual(null);
                expect(responseRow.performanceByKpiId[secondKpiId].isStatisticallySignificant).toEqual(true);
                expect(responseRow.performanceByKpiId[lastKpiId].met).toEqual(null);
                expect(responseRow.performanceByKpiId[lastKpiId].failed).toEqual(null);
                expect(responseRow.performanceByKpiId[lastKpiId].percentLift).toEqual(null);
                expect(responseRow.performanceByKpiId[lastKpiId].isStatisticallySignificant).toEqual(false);
            });
        },
    },
    {
        case: 'Creative Impressions: success 201 response, actual values',
        type: TEST_TYPES.POSITIVE,
        payload: {
            ...getReport[env],
            kpiIds: [
                singleKpiIdPerEnv[env],
                singleKpiIdNoDataPerEnv[env],
            ],
            advancedFilters: {
                creativeImpressions: {
                    greaterThanOrEqualTo: 200000
                },
            },
        },
        verify: async (response: APIResponse) => {
            const responseBody: SuccessResponseBody = await response.json();
            expect(response.status()).toBe(API_RESPONSE_STATUS.CREATED);
            assertCriteriaPerformanceResultShape(responseBody.result, getReport[env].filters);
            const kpiId = singleKpiIdPerEnv[env];
            const kpiNoData = singleKpiIdNoDataPerEnv[env];
            responseBody.result.forEach((responseRow) => {
                expect(responseRow).toBeDefined();
                expect(responseRow.mediaCount.met).toEqual(0);
                expect(responseRow.mediaCount.failed).toEqual(6);
                expect(responseRow.mediaCount.percentageMet).toEqual(0);
                expect(responseRow.impressions.met).toEqual(0);
                expect(responseRow.impressions.failed).toEqual(3561378);
                expect(responseRow.impressions.total).toEqual( 3561378);
                expect(responseRow.performanceByKpiId[kpiId].met).toEqual( null);
                expect(responseRow.performanceByKpiId[kpiId].failed).toEqual( 87.619314770856);
                expect(responseRow.performanceByKpiId[kpiId].percentLift).toEqual(null);
                expect(responseRow.performanceByKpiId[kpiId].isStatisticallySignificant).toEqual(true);
                expect(responseRow.performanceByKpiId[kpiNoData].met).toEqual(null);
                expect(responseRow.performanceByKpiId[kpiNoData].failed).toEqual(null);
                expect(responseRow.performanceByKpiId[kpiNoData].percentLift).toEqual(null);
                expect(responseRow.performanceByKpiId[kpiNoData].isStatisticallySignificant).toEqual(false);
            });
        }
    },
    {
        case: 'Media Type[IMAGE]: success 201 response, no data',
        type: TEST_TYPES.POSITIVE,
        payload: {
            ...getReport[env],
            kpiIds: [
                singleKpiIdPerEnv[env],
                singleKpiIdNoDataPerEnv[env],
            ],
            filters: {
                ...getReport[env].filters,
                creativeMediaType: [
                    "IMAGE"
                ]
            }
        },
        verify: async (response: APIResponse) => {
            const responseBody: SuccessResponseBody = await response.json();
            expect(response.status()).toBe(API_RESPONSE_STATUS.CREATED);
            expect(responseBody.result).toHaveLength(0)
        }
    },
    {
        case: 'Media Type[VIDEO]: success 201 response, actual values',
        type: TEST_TYPES.POSITIVE,
        payload: {
            ...getReport[env],
            kpiIds: [
                singleKpiIdPerEnv[env],
                singleKpiIdNoDataPerEnv[env],
            ],
            filters: {
                ...getReport[env].filters,
                creativeMediaType: [
                    "VIDEO"
                ]
            }
        },
        verify: async (response: APIResponse) => {
            const responseBody: SuccessResponseBody = await response.json();
            expect(response.status()).toBe(API_RESPONSE_STATUS.CREATED);
            assertCriteriaPerformanceResultShape(responseBody.result, getReport[env].filters);
            const singleKpi = singleKpiIdPerEnv[env];
            const singleKpiNoData = singleKpiIdNoDataPerEnv[env];

            responseBody.result.forEach((responseRow) => {
                expect(responseRow).toBeDefined();
                expect(responseRow.mediaCount.met).toEqual(1);
                expect(responseRow.mediaCount.failed).toEqual(33);
                expect(responseRow.mediaCount.percentageMet).toEqual(2.941176470588235);
                expect(responseRow.impressions.met).toEqual(  65854);
                expect(responseRow.impressions.failed).toEqual(4642540);
                expect(responseRow.impressions.total).toEqual( 4708394);
                expect(responseRow.performanceByKpiId[singleKpi].met).toEqual( 89.039390166125);
                expect(responseRow.performanceByKpiId[singleKpi].failed).toEqual(87.381067260594);
                expect(responseRow.performanceByKpiId[singleKpi].percentLift).toEqual( 1.8978057347199029);
                expect(responseRow.performanceByKpiId[singleKpi].isStatisticallySignificant).toEqual(true);
                expect(responseRow.performanceByKpiId[singleKpiNoData].met).toEqual(null);
                expect(responseRow.performanceByKpiId[singleKpiNoData].failed).toEqual(null);
                expect(responseRow.performanceByKpiId[singleKpiNoData].percentLift).toEqual(null);
                expect(responseRow.performanceByKpiId[singleKpiNoData].isStatisticallySignificant).toEqual(false);
            });
        },
    }
];

testScenarios.forEach((scenario) => {
    test(`API test POST Criteria Performance report: X - ${scenario.case}, 
        type: ${scenario.type}, 
        tags: @apiRegressionTest @apitest @criteriaPerformance @x`,
        async ({ request }) => {
            const { getURL, getLoginToken } = createPageInstances();
            const accessToken = await getLoginToken.getAccessToken(request);
            const baseURL = `${getURL.returnURL("bff-" + env)}/v1/criteria-group-report/organization/${organizationIdPerEnv[env]}`;

            const requestOptions = {
                headers: {
                    'Content-Type': 'application/json',
                    Authorization: `Bearer ${accessToken}`,
                },
                data: scenario.payload,
            }

            const response = await request.post(baseURL, requestOptions);
            await scenario.verify(response);
        });
});*/
