/*import {APIResponse, expect, test} from '@playwright/test';
import {createPageInstances} from '../../../../../../utils/helper';
import {API_RESPONSE_STATUS, TEST_TYPES} from '../../../../../../utils/util-constants';
// @ts-ignore
import * as getReport from '../../../../../../payload/bff/criteria-performance/getReportPinterest';
import {SuccessResponseBody} from '../../../../../../utils/scoring.types';
import {CriteriaPerformanceReportRow} from '../../../../../../utils/analytics.types';
import {assertCriteriaPerformanceResultShape} from "./verifyUtils";

const env = process.env.TESTENV || 'dev';

const organizationIdPerEnv = {
    dev: '27a7e882-43de-4bfa-8f53-3b62875b8432',
}

const KPI_PIN_CLICK_RATE_ID_DEV = '141';
const KPI_VTR_25_ID_DEV = '137';
const KPI_SAVE_RATE_ID_DEV = '144';

const adAccountIdsPerEnv = {
    dev: ['************', '************'],
};

const singleKpiIdPerEnv = {
    dev: KPI_PIN_CLICK_RATE_ID_DEV,
};

const singleKpiIdNoDataPerEnv = {
    dev: KPI_SAVE_RATE_ID_DEV,
};

const multipleKpiIdsPerEnv = {
    dev: [KPI_PIN_CLICK_RATE_ID_DEV, KPI_VTR_25_ID_DEV, KPI_SAVE_RATE_ID_DEV],
};

const campaignIdPerEnv = {
    dev: '************',
};

const objectiveIdPerEnv = {
    dev: 'APP_INSTALLS'
};

const adIdPerEnv = {
    dev: '************',
};

const adPlacementsPerEnv = {
    dev: 'SEARCH',
};

const testScenarios = [
    {
        case: 'no KPI: success 201 response, actual values',
        type: TEST_TYPES.POSITIVE,
        payload: getReport[env],
        verify: async (response: APIResponse) => {
            const responseBody: SuccessResponseBody = await response.json();
            expect(response.status()).toBe(API_RESPONSE_STATUS.CREATED);
            assertCriteriaPerformanceResultShape(responseBody.result, getReport[env].filters);
            responseBody.result.forEach((responseRow) => {
                expect(responseRow).toBeDefined();
                expect(responseRow.performanceByKpiId).toEqual({});
                expect(responseRow.mediaCount.met).toEqual(1);
                expect(responseRow.mediaCount.failed).toEqual(0);
                expect(responseRow.mediaCount.percentageMet).toEqual(100);
                expect(responseRow.impressions.met).toEqual(23516);
                expect(responseRow.impressions.failed).toEqual(0);
                expect(responseRow.impressions.total).toEqual(23516);
            });
        },
    },
    {
        case: 'no KPI: success 201, no data',
        type: TEST_TYPES.POSITIVE,
        payload: {
            ...getReport[env],
            "filters": {
                "criteriaGroupIds": ['unknown']
            },
        },
        verify: async (response: APIResponse) => {
            const responseBody: SuccessResponseBody = await response.json();
            expect(response.status()).toBe(API_RESPONSE_STATUS.CREATED);
            expect(responseBody.result).toEqual([]);
        },
    },
    {
        case: '1 KPI: success 201 response, actual values',
        type: TEST_TYPES.POSITIVE,
        payload: {
            ...getReport[env],
            kpiIds: [singleKpiIdPerEnv[env]],
        },
        verify: async (response: APIResponse) => {
            const responseBody: SuccessResponseBody = await response.json();
            expect(response.status()).toBe(API_RESPONSE_STATUS.CREATED);
            assertCriteriaPerformanceResultShape(responseBody.result, getReport[env].filters);

            responseBody.result.forEach((responseRow) => {
                const kpiId = singleKpiIdPerEnv[env];
                expect(responseRow).toBeDefined();
                expect(Object.keys(responseRow.performanceByKpiId[kpiId])).toHaveLength(4);
                expect(responseRow.performanceByKpiId[kpiId].met).toEqual(0.************);
                expect(responseRow.performanceByKpiId[kpiId].failed).toEqual(null);
                expect(responseRow.performanceByKpiId[kpiId].percentLift).toEqual(null);
                expect(responseRow.performanceByKpiId[kpiId].isStatisticallySignificant).toEqual(true);
            });
        },
    },
    {
        case: '1 KPI: success 201 response, actual values - no data',
        type: TEST_TYPES.POSITIVE,
        payload: {
            ...getReport[env],
            kpiIds: [singleKpiIdNoDataPerEnv[env]],
        },
        verify: async (response: APIResponse) => {
            const responseBody: SuccessResponseBody = await response.json();
            expect(response.status()).toBe(API_RESPONSE_STATUS.CREATED);
            assertCriteriaPerformanceResultShape(responseBody.result, getReport[env].filters);

            responseBody.result.forEach((responseRow) => {
                const kpiId = singleKpiIdNoDataPerEnv[env];
                expect(responseRow).toBeDefined();
                expect(Object.keys(responseRow.performanceByKpiId[kpiId])).toHaveLength(4);
                expect(responseRow.performanceByKpiId[kpiId].met).toEqual(null);
                expect(responseRow.performanceByKpiId[kpiId].failed).toEqual(null);
                expect(responseRow.performanceByKpiId[kpiId].percentLift).toEqual(null);
                expect(responseRow.performanceByKpiId[kpiId].isStatisticallySignificant).toEqual(false);
            });
        },
    },
    {
        case: 'multiple KPIs: success 201 response',
        type: TEST_TYPES.POSITIVE,
        payload: {
            ...getReport[env],
            kpiIds: multipleKpiIdsPerEnv[env],
        },
        verify: async (response: APIResponse) => {
            const responseBody: SuccessResponseBody = await response.json();
            expect(response.status()).toBe(API_RESPONSE_STATUS.CREATED);
            assertCriteriaPerformanceResultShape(responseBody.result, getReport[env].filters);

            responseBody.result.forEach((responseRow) => {
                expect(responseRow).toBeDefined();
                expect(Object.keys(responseRow.performanceByKpiId)).toHaveLength(3);
            });
        },
    },
    {
        case: 'Campaign Identifier filter: success 201 response, no data',
        type: TEST_TYPES.POSITIVE,
        payload: {
            ...getReport[env],
            kpiIds: multipleKpiIdsPerEnv[env],
            advancedFilters: {
                campaignIdentifiers: [campaignIdPerEnv[env]],
            },
        },
        verify: async (response: APIResponse) => {
            const responseBody: SuccessResponseBody = await response.json();
            expect(response.status()).toBe(API_RESPONSE_STATUS.CREATED);
            expect(responseBody.result).toEqual([]);
        },
    },
    {
        case: 'Campaign Objective filter: success 201 response, no data',
        type: TEST_TYPES.POSITIVE,
        payload: {
            ...getReport[env],
            kpiIds: multipleKpiIdsPerEnv[env],
            advancedFilters: {
                campaignObjectives: [objectiveIdPerEnv[env]],
            },
        },
        verify: async (response: APIResponse) => {
            const responseBody: SuccessResponseBody = await response.json();
            expect(response.status()).toBe(API_RESPONSE_STATUS.CREATED);
            expect(responseBody.result).toEqual([]);
        },
    },
    {
        case: 'Ad Impression filter: success 201 response, no data',
        type: TEST_TYPES.POSITIVE,
        payload: {
            ...getReport[env],
            kpiIds: multipleKpiIdsPerEnv[env],
            advancedFilters: {
                adImpressions: {greaterThanOrEqualTo: 50000},
            },
        },
        verify: async (response: APIResponse) => {
            const responseBody: SuccessResponseBody = await response.json();
            expect(response.status()).toBe(API_RESPONSE_STATUS.CREATED);
            expect(responseBody.result).toEqual([]);
        },
    },
    {
        case: 'Ad Identifier filter: success 201 response, actual values',
        type: TEST_TYPES.POSITIVE,
        payload: {
            ...getReport[env],
            kpiIds: [singleKpiIdPerEnv[env]],
            advancedFilters: {
                adIdentifiers: [adIdPerEnv[env]],
            },
        },
        verify: async (response: APIResponse) => {
            const responseBody: SuccessResponseBody = await response.json();
            expect(response.status()).toBe(API_RESPONSE_STATUS.CREATED);
            assertCriteriaPerformanceResultShape(responseBody.result, getReport[env].filters);

            responseBody.result.forEach((responseRow) => {
                const kpiId = singleKpiIdPerEnv[env];
                expect(responseRow).toBeDefined();
                expect(Object.keys(responseRow.performanceByKpiId[kpiId])).toHaveLength(4);
                expect(responseRow.performanceByKpiId[kpiId].met).toEqual(0.************);
                expect(responseRow.performanceByKpiId[kpiId].failed).toEqual(null);
                expect(responseRow.performanceByKpiId[kpiId].percentLift).toEqual(null);
                expect(responseRow.performanceByKpiId[kpiId].isStatisticallySignificant).toEqual(true);
            });
        },
    },
    {
        case: 'Ad Placement filter: success 201 response, actual values',
        type: TEST_TYPES.POSITIVE,
        payload: {
            ...getReport[env],
            adAccountIds: adAccountIdsPerEnv[env],
            advancedFilters: {
                adPlacements: [adPlacementsPerEnv[env]],
            },
        },
        verify: async (response: APIResponse) => {
            const responseBody: SuccessResponseBody = await response.json();
            expect(response.status()).toBe(API_RESPONSE_STATUS.CREATED);
            assertCriteriaPerformanceResultShape(responseBody.result, getReport[env].filters);

            responseBody.result.forEach((responseRow) => {
                expect(responseRow).toBeDefined();
                expect(responseRow.impressions.met).toEqual(11371);
                expect(responseRow.impressions.failed).toEqual(0);
                expect(responseRow.impressions.total).toEqual(11371);
            });
        },
    },
    {
        case: 'Created by Vidmob filter (true): success 201 response, no data',
        type: TEST_TYPES.POSITIVE,
        payload: {
            ...getReport[env],
            kpiIds: multipleKpiIdsPerEnv[env],
            advancedFilters: {
                createdByVidmob: true,
            },
        },
        verify: async (response: APIResponse) => {
            const responseBody: SuccessResponseBody = await response.json();
            expect(response.status()).toBe(API_RESPONSE_STATUS.CREATED);
            expect(responseBody.result).toEqual([]);
        },
    },
    {
        case: 'Creative Impressions filter: success 201 response, no data',
        type: TEST_TYPES.POSITIVE,
        payload: {
            ...getReport[env],
            kpiIds: multipleKpiIdsPerEnv[env],
            advancedFilters: {
                creativeImpressions: {
                    greaterThanOrEqualTo: 25000
                },
            },
        },
        verify: async (response: APIResponse) => {
            const responseBody: SuccessResponseBody = await response.json();
            expect(response.status()).toBe(API_RESPONSE_STATUS.CREATED);
            expect(responseBody.result).toEqual([]);
        },
    },
    {
        case: 'Media Type[IMAGE]: success 201 response, no data',
        type: TEST_TYPES.POSITIVE,
        payload: {
            ...getReport[env],
            kpiIds: multipleKpiIdsPerEnv[env],
            filters: {
                ...getReport[env].filters,
                creativeMediaType: [
                    "IMAGE"
                ]
            }
        },
        verify: async (response: APIResponse) => {
            const responseBody: SuccessResponseBody = await response.json();
            expect(response.status()).toBe(API_RESPONSE_STATUS.CREATED);
            // The criteria is in these tests is sound on which is video onl
            expect(responseBody.result).toEqual([]);
        },
    },
    {
        case: 'Media Type[VIDEO]: success 201 response, actual values',
        type: TEST_TYPES.POSITIVE,
        payload: {
            ...getReport[env],
            kpiIds: multipleKpiIdsPerEnv[env],
            filters: {
                ...getReport[env].filters,
                creativeMediaType: [
                    "VIDEO"
                ]
            }
        },
        verify: async (response: APIResponse) => {
            const responseBody: SuccessResponseBody = await response.json();
            expect(response.status()).toBe(API_RESPONSE_STATUS.CREATED);
            assertCriteriaPerformanceResultShape(responseBody.result, getReport[env].filters);
            const firstKpiId = multipleKpiIdsPerEnv[env][0];
            const secondKpiId = multipleKpiIdsPerEnv[env][1];
            const lastKpiId = multipleKpiIdsPerEnv[env][multipleKpiIdsPerEnv[env].length - 1];

            responseBody.result.forEach((responseRow) => {
                expect(responseRow).toBeDefined();
                expect(responseRow.mediaCount.met).toEqual(1);
                expect(responseRow.mediaCount.failed).toEqual(0);
                expect(responseRow.mediaCount.percentageMet).toEqual(  100);
                expect(responseRow.impressions.met).toEqual(  23516);
                expect(responseRow.impressions.failed).toEqual(0);
                expect(responseRow.impressions.total).toEqual( 23516);
                expect(responseRow.performanceByKpiId[firstKpiId].met).toEqual(  0.************);
                expect(responseRow.performanceByKpiId[firstKpiId].failed).toEqual(null);
                expect(responseRow.performanceByKpiId[firstKpiId].percentLift).toEqual(null);
                expect(responseRow.performanceByKpiId[firstKpiId].isStatisticallySignificant).toEqual(true);
                expect(responseRow.performanceByKpiId[secondKpiId].met).toEqual(38.050688892669);
                expect(responseRow.performanceByKpiId[secondKpiId].failed).toEqual(  null);
                expect(responseRow.performanceByKpiId[secondKpiId].percentLift).toEqual( null);
                expect(responseRow.performanceByKpiId[secondKpiId].isStatisticallySignificant).toEqual(true);
                expect(responseRow.performanceByKpiId[lastKpiId].met).toEqual(null);
                expect(responseRow.performanceByKpiId[lastKpiId].failed).toEqual(null);
                expect(responseRow.performanceByKpiId[lastKpiId].percentLift).toEqual(null);
                expect(responseRow.performanceByKpiId[lastKpiId].isStatisticallySignificant).toEqual(false);
            });
        },
    }
];

testScenarios.forEach((scenario) => {
    test(`API test POST Criteria Performance report: Pinterest - ${scenario.case}, 
        type: ${scenario.type}, 
        tags: @apiRegressionTest @apitest @criteriaPerformance @pinterest`,
        async ({ request }) => {
            const { getURL, getLoginToken } = createPageInstances();
            const accessToken = await getLoginToken.getAccessToken(request);
            const baseURL = `${getURL.returnURL("bff-" + env)}/v1/criteria-group-report/organization/${organizationIdPerEnv[env]}`;

            const requestOptions = {
                headers: {
                    'Content-Type': 'application/json',
                    Authorization: `Bearer ${accessToken}`,
                },
                data: scenario.payload,
            }

            const response = await request.post(baseURL, requestOptions);
            await scenario.verify(response);
        });
});*/
