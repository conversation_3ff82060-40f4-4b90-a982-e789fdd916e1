/*import {APIResponse, expect, test} from "@playwright/test";
import {createPageInstances} from "../../../../../../utils/helper";
import {API_RESPONSE_STATUS, TEST_TYPES} from "../../../../../../utils/util-constants";

// @ts-ignore
import * as getReport from '../../../../../../payload/bff/criteria-performance/getReportTiktok';
import {SuccessResponseBody} from "../../../../../../utils/scoring.types";
import {assertCriteriaPerformanceResultShape, organizationIdPerEnv} from "./verifyUtils";

const env = process.env.TESTENV || 'dev';

const KPI_CLICK_THROUGH_RATE_ID_DEV = '383';
const KPI_LIKE_RATE_ID_DEV = '386';
const KPI_COST_PER_REGISTRATION_ID_DEV = '411';

const singleKpiIdPerEnv = {
    dev: KPI_CLICK_THROUGH_RATE_ID_DEV,
};

const singleKpiIdNoDataPerEnv = {
    dev: KPI_COST_PER_REGISTRATION_ID_DEV,
};

const multipleKpiIdsPerEnv = {
    dev: [KPI_CLICK_THROUGH_RATE_ID_DEV, KPI_LIKE_RATE_ID_DEV, KPI_COST_PER_REGISTRATION_ID_DEV],
}

const campaignIdPerEnv = {
    dev: '1714343883908113'
}

const adIdentifierPerEnv = {
    dev: '1709272420967458'
}
const testScenarios = [
    {
        case: 'no KPI: success 201 response, actual values',
        type: TEST_TYPES.POSITIVE,
        payload: getReport[env],
        verify: async (response: APIResponse) => {
            const responseBody: SuccessResponseBody = await response.json();
            expect(response.status()).toBe(API_RESPONSE_STATUS.CREATED);
            assertCriteriaPerformanceResultShape(responseBody.result, getReport[env].filters);
            responseBody.result.forEach((responseRow) => {
                expect(responseRow).toBeDefined();
                expect(responseRow.performanceByKpiId).toEqual({});
                expect(responseRow.mediaCount.met).toEqual(9);
                expect(responseRow.mediaCount.failed).toEqual(18);
                expect(responseRow.mediaCount.percentageMet).toEqual(33.33333333333333);
                expect(responseRow.impressions.met).toEqual(7916716);
                expect(responseRow.impressions.failed).toEqual(22103955);
                expect(responseRow.impressions.total).toEqual(30020671);
            });
        }
    },
    {
        case: 'no KPI: success 201 response, no data',
        type: TEST_TYPES.POSITIVE,
        payload: {
            ...getReport[env],
            "filters": {
                "criteriaGroupIds": ['unknown']
            },
        },
        verify: async (response: APIResponse) => {
            const responseBody: SuccessResponseBody = await response.json();
            expect(response.status()).toBe(API_RESPONSE_STATUS.CREATED);
            expect(responseBody.result).toEqual([]);
        }
    },
    {
        case: '1 KPI: success 201 response, actual values',
        type: TEST_TYPES.POSITIVE,
        payload: {
            ...getReport[env],
            kpiIds: [singleKpiIdPerEnv[env]],
        },
        verify: async (response: APIResponse) => {
            const responseBody: SuccessResponseBody = await response.json();
            expect(response.status()).toBe(API_RESPONSE_STATUS.CREATED);
            assertCriteriaPerformanceResultShape(responseBody.result, getReport[env].filters );

            responseBody.result.forEach((responseRow) => {
                const kpiId = singleKpiIdPerEnv[env];
                expect(responseRow).toBeDefined();
                expect(Object.keys(responseRow.performanceByKpiId[kpiId])).toHaveLength(4);
                expect(responseRow.performanceByKpiId[kpiId].met).toEqual(0.971008685925);
                expect(responseRow.performanceByKpiId[kpiId].failed).toEqual(1.802084740039);
                expect(responseRow.performanceByKpiId[kpiId].percentLift).toEqual( -46.117479142296844);
                expect(responseRow.performanceByKpiId[kpiId].isStatisticallySignificant).toEqual(true);
            });
        },
    },
    {
        case: '1 KPI: success 201 response, no data',
        type: TEST_TYPES.POSITIVE,
        payload: {
            ...getReport[env],
            kpiIds: [singleKpiIdNoDataPerEnv[env]],
        },
        verify: async (response: APIResponse) => {
            const responseBody: SuccessResponseBody = await response.json();
            expect(response.status()).toBe(API_RESPONSE_STATUS.CREATED);
            assertCriteriaPerformanceResultShape(responseBody.result, getReport[env].filters);

            responseBody.result.forEach((responseRow) => {
                const kpiId = singleKpiIdNoDataPerEnv[env];
                expect(responseRow).toBeDefined();
                expect(Object.keys(responseRow.performanceByKpiId[kpiId])).toHaveLength(4);
                expect(responseRow.performanceByKpiId[kpiId].met).toEqual(null);
                expect(responseRow.performanceByKpiId[kpiId].failed).toEqual(null);
                expect(responseRow.performanceByKpiId[kpiId].percentLift).toEqual(null);
                expect(responseRow.performanceByKpiId[kpiId].isStatisticallySignificant).toEqual(false);
            });
        },
    },
    {
        case: 'multiple KPIs: success 201 response',
        type: TEST_TYPES.POSITIVE,
        payload: {
            ...getReport[env],
            kpiIds: multipleKpiIdsPerEnv[env],
        },
        verify: async (response: APIResponse) => {
            const responseBody: SuccessResponseBody = await response.json();
            expect(response.status()).toBe(API_RESPONSE_STATUS.CREATED);
            assertCriteriaPerformanceResultShape(responseBody.result, getReport[env].filters);

            responseBody.result.forEach((responseRow) => {
                expect(responseRow).toBeDefined();
                expect(Object.keys(responseRow.performanceByKpiId)).toHaveLength(3);
            });
        },
    },
    {
        case: 'Campaign Identifier filter: success 201 response, actual values',
        type: TEST_TYPES.POSITIVE,
        payload: {
            ...getReport[env],
            kpiIds: [
                singleKpiIdPerEnv[env],
                singleKpiIdNoDataPerEnv[env],
            ],
            advancedFilters: {
                campaignIdentifiers: [campaignIdPerEnv[env]],
            },
        },
        verify: async (response: APIResponse) => {
            const responseBody: SuccessResponseBody = await response.json();
            expect(response.status()).toBe(API_RESPONSE_STATUS.CREATED);
            assertCriteriaPerformanceResultShape(responseBody.result, getReport[env].filters);
            const kpiId = singleKpiIdPerEnv[env];
            const kpiNoData = singleKpiIdNoDataPerEnv[env];
            responseBody.result.forEach((responseRow) => {
                expect(responseRow).toBeDefined();
                expect(responseRow.mediaCount.met).toEqual(1);
                expect(responseRow.mediaCount.failed).toEqual(2);
                expect(responseRow.mediaCount.percentageMet).toEqual(33.33333333333333);
                expect(responseRow.impressions.met).toEqual(207080);
                expect(responseRow.impressions.failed).toEqual(293113);
                expect(responseRow.impressions.total).toEqual(500193);
                expect(responseRow.performanceByKpiId[kpiId].met).toEqual(1.516322194321);
                expect(responseRow.performanceByKpiId[kpiId].failed).toEqual(1.578913251886);
                expect(responseRow.performanceByKpiId[kpiId].percentLift).toEqual(-3.964185967166696);
                expect(responseRow.performanceByKpiId[kpiId].isStatisticallySignificant).toEqual(false);
                expect(responseRow.performanceByKpiId[kpiNoData].met).toEqual(null);
                expect(responseRow.performanceByKpiId[kpiNoData].failed).toEqual(null);
                expect(responseRow.performanceByKpiId[kpiNoData].percentLift).toEqual(null);
                expect(responseRow.performanceByKpiId[kpiNoData].isStatisticallySignificant).toEqual(false);
            });
        },
    },
    {
        case: 'Ad Impression filter: success 201 response, actual values',
        type: TEST_TYPES.POSITIVE,
        payload: {
            ...getReport[env],
            kpiIds: [
                singleKpiIdPerEnv[env],
                singleKpiIdNoDataPerEnv[env],
            ],
            advancedFilters: {
                adImpressions: {greaterThanOrEqualTo: 60000},
            },
        },
        verify: async (response: APIResponse) => {
            const responseBody: SuccessResponseBody = await response.json();
            expect(response.status()).toBe(API_RESPONSE_STATUS.CREATED);
            assertCriteriaPerformanceResultShape(responseBody.result, getReport[env].filters);
            const kpiId = singleKpiIdPerEnv[env];
            const kpiNoData = singleKpiIdNoDataPerEnv[env];
            responseBody.result.forEach((responseRow) => {
                expect(responseRow).toBeDefined();
                expect(responseRow.mediaCount.met).toEqual(8);
                expect(responseRow.mediaCount.failed).toEqual(15);
                expect(responseRow.mediaCount.percentageMet).toEqual(34.78260869565217);
                expect(responseRow.impressions.met).toEqual(7863331);
                expect(responseRow.impressions.failed).toEqual(21988560);
                expect(responseRow.impressions.total).toEqual(29851891);
                expect(responseRow.performanceByKpiId[kpiId].met).toEqual(0.878113868029);
                expect(responseRow.performanceByKpiId[kpiId].failed).toEqual(1.772226103028);
                expect(responseRow.performanceByKpiId[kpiId].percentLift).toEqual(-50.45136359696614);
                expect(responseRow.performanceByKpiId[kpiId].isStatisticallySignificant).toEqual(true);
                expect(responseRow.performanceByKpiId[kpiNoData].met).toEqual(null);
                expect(responseRow.performanceByKpiId[kpiNoData].failed).toEqual(null);
                expect(responseRow.performanceByKpiId[kpiNoData].percentLift).toEqual(null);
                expect(responseRow.performanceByKpiId[kpiNoData].isStatisticallySignificant).toEqual(false);
            });
        },
    },
    {
        case: 'Ad Identifier filter: success 201 response, actual values',
        type: TEST_TYPES.POSITIVE,
        payload: {
            ...getReport[env],
            kpiIds: [
                singleKpiIdPerEnv[env],
                singleKpiIdNoDataPerEnv[env],
            ],
            advancedFilters: {
                adIdentifiers: [adIdentifierPerEnv[env]],
            },
        },
        verify: async (response: APIResponse) => {
            const responseBody: SuccessResponseBody = await response.json();
            expect(response.status()).toBe(API_RESPONSE_STATUS.CREATED);
            assertCriteriaPerformanceResultShape(responseBody.result, getReport[env].filters);
            const kpiId = singleKpiIdPerEnv[env];
            const kpiNoData = singleKpiIdNoDataPerEnv[env];
            responseBody.result.forEach((responseRow) => {
                expect(responseRow).toBeDefined();
                expect(responseRow.mediaCount.met).toEqual(1);
                expect(responseRow.mediaCount.failed).toEqual(1);
                expect(responseRow.mediaCount.percentageMet).toEqual(50);
                expect(responseRow.impressions.met).toEqual(852276);
                expect(responseRow.impressions.failed).toEqual(852276);
                expect(responseRow.impressions.total).toEqual(1704552);
                expect(responseRow.performanceByKpiId[kpiId].met).toEqual(0.286526899737);
                expect(responseRow.performanceByKpiId[kpiId].failed).toEqual(0.286526899737);
                expect(responseRow.performanceByKpiId[kpiId].percentLift).toEqual(0);
                expect(responseRow.performanceByKpiId[kpiId].isStatisticallySignificant).toEqual(false);
                expect(responseRow.performanceByKpiId[kpiNoData].met).toEqual(null);
                expect(responseRow.performanceByKpiId[kpiNoData].failed).toEqual(null);
                expect(responseRow.performanceByKpiId[kpiNoData].percentLift).toEqual(null);
                expect(responseRow.performanceByKpiId[kpiNoData].isStatisticallySignificant).toEqual(false);
            });
        },
    },
    {
        case: 'Created By Vidmob[true]: success 201 response, actual values',
        type: TEST_TYPES.POSITIVE,
        payload: {
            ...getReport[env],
            kpiIds: [
                singleKpiIdPerEnv[env],
                singleKpiIdNoDataPerEnv[env],
            ],
            advancedFilters: {
                createdByVidmob: true,
            },
        },
        verify: async (response: APIResponse) => {
            const responseBody: SuccessResponseBody = await response.json();
            expect(response.status()).toBe(API_RESPONSE_STATUS.CREATED);
            assertCriteriaPerformanceResultShape(responseBody.result, getReport[env].filters);
            const kpiId = singleKpiIdPerEnv[env];
            const kpiNoData = singleKpiIdNoDataPerEnv[env];
            responseBody.result.forEach((responseRow) => {
                expect(responseRow).toBeDefined();
                expect(responseRow.mediaCount.met).toEqual(3);
                expect(responseRow.mediaCount.failed).toEqual(5);
                expect(responseRow.mediaCount.percentageMet).toEqual(37.5);
                expect(responseRow.impressions.met).toEqual(2644831);
                expect(responseRow.impressions.failed).toEqual(4671554);
                expect(responseRow.impressions.total).toEqual( 7316385);
                expect(responseRow.performanceByKpiId[kpiId].met).toEqual(0.279488557114);
                expect(responseRow.performanceByKpiId[kpiId].failed).toEqual(0.286863857295);
                expect(responseRow.performanceByKpiId[kpiId].percentLift).toEqual(-2.5710106008285107);
                expect(responseRow.performanceByKpiId[kpiId].isStatisticallySignificant).toEqual(false);
                expect(responseRow.performanceByKpiId[kpiNoData].met).toEqual(null);
                expect(responseRow.performanceByKpiId[kpiNoData].failed).toEqual(null);
                expect(responseRow.performanceByKpiId[kpiNoData].percentLift).toEqual(null);
                expect(responseRow.performanceByKpiId[kpiNoData].isStatisticallySignificant).toEqual(false);
            });
        },
    },
    {
        case: 'Created By Vidmob[false]: success 201 response, actual values',
        type: TEST_TYPES.POSITIVE,
        payload: {
            ...getReport[env],
            kpiIds: [
                singleKpiIdPerEnv[env],
                singleKpiIdNoDataPerEnv[env],
            ],
            advancedFilters: {
                createdByVidmob: false,
            },
        },
        verify: async (response: APIResponse) => {
            const responseBody: SuccessResponseBody = await response.json();
            expect(response.status()).toBe(API_RESPONSE_STATUS.CREATED);
            assertCriteriaPerformanceResultShape(responseBody.result, getReport[env].filters);
            const kpiId = singleKpiIdPerEnv[env];
            const kpiNoData = singleKpiIdNoDataPerEnv[env];
            responseBody.result.forEach((responseRow) => {
                expect(responseRow).toBeDefined();
                expect(responseRow.mediaCount.met).toEqual(6);
                expect(responseRow.mediaCount.failed).toEqual(13);
                expect(responseRow.mediaCount.percentageMet).toEqual(31.57894736842105);
                expect(responseRow.impressions.met).toEqual(5271885);
                expect(responseRow.impressions.failed).toEqual(17432401);
                expect(responseRow.impressions.total).toEqual( 22704286);
                expect(responseRow.performanceByKpiId[kpiId].met).toEqual(1.317934666633);
                expect(responseRow.performanceByKpiId[kpiId].failed).toEqual( 2.20813529932);
                expect(responseRow.performanceByKpiId[kpiId].percentLift).toEqual(-40.314587288249);
                expect(responseRow.performanceByKpiId[kpiId].isStatisticallySignificant).toEqual(true);
                expect(responseRow.performanceByKpiId[kpiNoData].met).toEqual(null);
                expect(responseRow.performanceByKpiId[kpiNoData].failed).toEqual(null);
                expect(responseRow.performanceByKpiId[kpiNoData].percentLift).toEqual(null);
                expect(responseRow.performanceByKpiId[kpiNoData].isStatisticallySignificant).toEqual(false);
            });
        },
    },
    {
        case: 'Creative Impressions: success 201 response, actual values',
        type: TEST_TYPES.POSITIVE,
        payload: {
            ...getReport[env],
            kpiIds: [
                singleKpiIdPerEnv[env],
                singleKpiIdNoDataPerEnv[env],
            ],
            advancedFilters: {
                creativeImpressions: {
                    greaterThanOrEqualTo: 60000
                },
            },
        },
        verify: async (response: APIResponse) => {
            const responseBody: SuccessResponseBody = await response.json();
            expect(response.status()).toBe(API_RESPONSE_STATUS.CREATED);
            assertCriteriaPerformanceResultShape(responseBody.result, getReport[env].filters);
            const kpiId = singleKpiIdPerEnv[env];
            const kpiNoData = singleKpiIdNoDataPerEnv[env];
            responseBody.result.forEach((responseRow) => {
                expect(responseRow).toBeDefined();
                expect(responseRow.mediaCount.met).toEqual(8);
                expect(responseRow.mediaCount.failed).toEqual(15);
                expect(responseRow.mediaCount.percentageMet).toEqual(34.78260869565217);
                expect(responseRow.impressions.met).toEqual(7863331);
                expect(responseRow.impressions.failed).toEqual(22015271);
                expect(responseRow.impressions.total).toEqual( 29878602);
                expect(responseRow.performanceByKpiId[kpiId].met).toEqual( 0.878113868029);
                expect(responseRow.performanceByKpiId[kpiId].failed).toEqual( 1.772138076338);
                expect(responseRow.performanceByKpiId[kpiId].percentLift).toEqual(-50.4489023878116);
                expect(responseRow.performanceByKpiId[kpiId].isStatisticallySignificant).toEqual(true);
                expect(responseRow.performanceByKpiId[kpiNoData].met).toEqual(null);
                expect(responseRow.performanceByKpiId[kpiNoData].failed).toEqual(null);
                expect(responseRow.performanceByKpiId[kpiNoData].percentLift).toEqual(null);
                expect(responseRow.performanceByKpiId[kpiNoData].isStatisticallySignificant).toEqual(false);
            });
        }
    },
    {
        case: 'Media Type[IMAGE]: success 201 response, no data',
        type: TEST_TYPES.POSITIVE,
        payload: {
            ...getReport[env],
            kpiIds: [
                singleKpiIdPerEnv[env],
                singleKpiIdNoDataPerEnv[env],
            ],
            filters: {
                ...getReport[env].filters,
                creativeMediaType: [
                    "IMAGE"
                ]
            }
        },
        verify: async (response: APIResponse) => {
            const responseBody: SuccessResponseBody = await response.json();
            expect(response.status()).toBe(API_RESPONSE_STATUS.CREATED);
            expect(responseBody.result).toHaveLength(0)
        }
    },
    {
        case: 'Media Type[VIDEO]: success 201 response, actual values',
        type: TEST_TYPES.POSITIVE,
        payload: {
            ...getReport[env],
            kpiIds: [
                singleKpiIdPerEnv[env],
                singleKpiIdNoDataPerEnv[env],
            ],
            filters: {
                ...getReport[env].filters,
                creativeMediaType: [
                    "VIDEO"
                ]
            }
        },
        verify: async (response: APIResponse) => {
            const responseBody: SuccessResponseBody = await response.json();
            expect(response.status()).toBe(API_RESPONSE_STATUS.CREATED);
            assertCriteriaPerformanceResultShape(responseBody.result, getReport[env].filters);
            const singleKpi = singleKpiIdPerEnv[env];
            const singleKpiNoData = singleKpiIdNoDataPerEnv[env];

            responseBody.result.forEach((responseRow) => {
                expect(responseRow).toBeDefined();
                expect(responseRow.mediaCount.met).toEqual(9);
                expect(responseRow.mediaCount.failed).toEqual(18);
                expect(responseRow.mediaCount.percentageMet).toEqual(33.33333333333333);
                expect(responseRow.impressions.met).toEqual( 7916716);
                expect(responseRow.impressions.failed).toEqual(22103955);
                expect(responseRow.impressions.total).toEqual( 30020671);
                expect(responseRow.performanceByKpiId[singleKpi].met).toEqual(0.971008685925);
                expect(responseRow.performanceByKpiId[singleKpi].failed).toEqual(1.802084740039);
                expect(responseRow.performanceByKpiId[singleKpi].percentLift).toEqual(-46.117479142296844);
                expect(responseRow.performanceByKpiId[singleKpi].isStatisticallySignificant).toEqual(true);
                expect(responseRow.performanceByKpiId[singleKpiNoData].met).toEqual(null);
                expect(responseRow.performanceByKpiId[singleKpiNoData].failed).toEqual(null);
                expect(responseRow.performanceByKpiId[singleKpiNoData].percentLift).toEqual(null);
                expect(responseRow.performanceByKpiId[singleKpiNoData].isStatisticallySignificant).toEqual(false);
            });
        },
    }
];


testScenarios.forEach((scenario) => {
    test(`API test POST Criteria Performance report: Tiktok - ${scenario.case}, 
        type: ${scenario.type}, 
        tags: @apiRegressionTest @apitest @criteriaPerformance @tiktok`,
        async ({ request }) => {
            const { getURL, getLoginToken } = createPageInstances();
            const accessToken = await getLoginToken.getAccessToken(request);
            const baseURL = `${getURL.returnURL("bff-" + env)}/v1/criteria-group-report/organization/${organizationIdPerEnv[env]}`;

            const requestOptions = {
                headers: {
                    'Content-Type': 'application/json',
                    Authorization: `Bearer ${accessToken}`,
                },
                data: scenario.payload,
            }

            const response = await request.post(baseURL, requestOptions);
            await scenario.verify(response);
        });
});*/