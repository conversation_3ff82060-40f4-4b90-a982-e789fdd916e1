/*import {APIResponse, expect, test} from '@playwright/test';
import {createPageInstances} from '../../../../../../utils/helper';
import {API_RESPONSE_STATUS, TEST_TYPES} from '../../../../../../utils/util-constants';
// @ts-ignore
import * as getReport from '../../../../../../payload/bff/criteria-performance/getReportSnapchat';
import {SuccessResponseBody} from '../../../../../../utils/scoring.types';
import {CriteriaPerformanceReportRow} from '../../../../../../utils/analytics.types';
import {assertCriteriaPerformanceResultShape} from "./verifyUtils";

const env = process.env.TESTENV || 'dev';

const organizationIdPerEnv = {
    dev: '27a7e882-43de-4bfa-8f53-3b62875b8432',
}

const KPI_SWIPE_THROUGH_RATE_ID_DEV = '31';
const KPI_VTR_25_ID_DEV = '27';
const KPI_UNIQUES_ID_DEV = '324';

const singleKpiIdPerEnv = {
    dev: KPI_SWIPE_THROUGH_RATE_ID_DEV,
};

const singleKpiIdNoDataPerEnv = {
    dev: KPI_UNIQUES_ID_DEV,
};

const multipleKpiIdsPerEnv = {
    dev: [KPI_SWIPE_THROUGH_RATE_ID_DEV, KPI_VTR_25_ID_DEV, KPI_UNIQUES_ID_DEV],
};

const campaignIdPerEnv = {
    dev: 'fccf3c8e-6bc2-457c-87a4-df94150ad7cc',
};

const objectiveIdPerEnv = {
    dev: 'BRAND_AWARENESS'
};

const adIdPerEnv = {
    dev: '092fee23-e0c2-4f36-a20f-d4ad669bfd42',
};

const testScenarios = [
    {
        case: 'no KPI: success 201 response, actual values',
        type: TEST_TYPES.POSITIVE,
        payload: getReport[env],
        verify: async (response: APIResponse) => {
            const responseBody: SuccessResponseBody = await response.json();
            expect(response.status()).toBe(API_RESPONSE_STATUS.CREATED);
            assertCriteriaPerformanceResultShape(responseBody.result, getReport[env].filters);
            responseBody.result.forEach((responseRow) => {
                expect(responseRow).toBeDefined();
                expect(responseRow.performanceByKpiId).toEqual({});
                expect(responseRow.mediaCount.met).toEqual(1);
                expect(responseRow.mediaCount.failed).toEqual(11);
                expect(responseRow.mediaCount.percentageMet).toEqual(8.333333333333332);
                expect(responseRow.impressions.met).toEqual(231303);
                expect(responseRow.impressions.failed).toEqual(3599289);
                expect(responseRow.impressions.total).toEqual(3830592);
            });
        },
    },
    {
        case: 'no KPI: success 201, no data',
        type: TEST_TYPES.POSITIVE,
        payload: {
            ...getReport[env],
            "filters": {
                "criteriaGroupIds": ['unknown']
            },
        },
        verify: async (response: APIResponse) => {
            const responseBody: SuccessResponseBody = await response.json();
            expect(response.status()).toBe(API_RESPONSE_STATUS.CREATED);
            expect(responseBody.result).toEqual([]);
        },
    },
    {
        case: '1 KPI: success 201 response, actual values',
        type: TEST_TYPES.POSITIVE,
        payload: {
            ...getReport[env],
            kpiIds: [singleKpiIdPerEnv[env]],
        },
        verify: async (response: APIResponse) => {
            const responseBody: SuccessResponseBody = await response.json();
            expect(response.status()).toBe(API_RESPONSE_STATUS.CREATED);
            assertCriteriaPerformanceResultShape(responseBody.result, getReport[env].filters);

            responseBody.result.forEach((responseRow) => {
                const kpiId = singleKpiIdPerEnv[env];
                expect(responseRow).toBeDefined();
                expect(Object.keys(responseRow.performanceByKpiId[kpiId])).toHaveLength(4);
                expect(responseRow.performanceByKpiId[kpiId].met).toEqual(0.760042022801);
                expect(responseRow.performanceByKpiId[kpiId].failed).toEqual(0.754843526041);
                expect(responseRow.performanceByKpiId[kpiId].percentLift).toEqual(0.6886853474474374);
                expect(responseRow.performanceByKpiId[kpiId].isStatisticallySignificant).toEqual(false);
            });
        },
    },
    {
        case: '1 KPI: success 201 response, actual values - no data',
        type: TEST_TYPES.POSITIVE,
        payload: {
            ...getReport[env],
            kpiIds: [singleKpiIdNoDataPerEnv[env]],
        },
        verify: async (response: APIResponse) => {
            const responseBody: SuccessResponseBody = await response.json();
            expect(response.status()).toBe(API_RESPONSE_STATUS.CREATED);
            assertCriteriaPerformanceResultShape(responseBody.result, getReport[env].filters);

            responseBody.result.forEach((responseRow) => {
                const kpiId = singleKpiIdNoDataPerEnv[env];
                expect(responseRow).toBeDefined();
                expect(Object.keys(responseRow.performanceByKpiId[kpiId])).toHaveLength(4);
                expect(responseRow.performanceByKpiId[kpiId].met).toEqual(null);
                expect(responseRow.performanceByKpiId[kpiId].failed).toEqual(null);
                expect(responseRow.performanceByKpiId[kpiId].percentLift).toEqual(null);
                expect(responseRow.performanceByKpiId[kpiId].isStatisticallySignificant).toEqual(false);
            });
        },
    },
    {
        case: 'multiple KPIs: success 201 response',
        type: TEST_TYPES.POSITIVE,
        payload: {
            ...getReport[env],
            kpiIds: multipleKpiIdsPerEnv[env],
        },
        verify: async (response: APIResponse) => {
            const responseBody: SuccessResponseBody = await response.json();
            expect(response.status()).toBe(API_RESPONSE_STATUS.CREATED);
            assertCriteriaPerformanceResultShape(responseBody.result, getReport[env].filters);

            responseBody.result.forEach((responseRow) => {
                expect(responseRow).toBeDefined();
                expect(Object.keys(responseRow.performanceByKpiId)).toHaveLength(3);
            });
        },
    },
    {
        case: 'Campaign Identifier filter: success 201 response, actual values',
        type: TEST_TYPES.POSITIVE,
        payload: {
            ...getReport[env],
            kpiIds: multipleKpiIdsPerEnv[env],
            advancedFilters: {
                campaignIdentifiers: [campaignIdPerEnv[env]],
            },
        },
        verify: async (response: APIResponse) => {
            const responseBody: SuccessResponseBody = await response.json();
            expect(response.status()).toBe(API_RESPONSE_STATUS.CREATED);
            assertCriteriaPerformanceResultShape(responseBody.result, getReport[env].filters);
            const firstKpiId = multipleKpiIdsPerEnv[env][0];
            const lastKpiId = multipleKpiIdsPerEnv[env][multipleKpiIdsPerEnv[env].length - 1];

            responseBody.result.forEach((responseRow) => {
                expect(responseRow).toBeDefined();
                expect(responseRow.mediaCount.met).toEqual(1);
                expect(responseRow.mediaCount.failed).toEqual(1);
                expect(responseRow.mediaCount.percentageMet).toEqual(50);
                expect(responseRow.impressions.met).toEqual(231303);
                expect(responseRow.impressions.failed).toEqual(231303);
                expect(responseRow.impressions.total).toEqual(462606);
                expect(responseRow.performanceByKpiId[firstKpiId].met).toEqual(0.760042022801);
                expect(responseRow.performanceByKpiId[firstKpiId].failed).toEqual(0.760042022801);
                expect(responseRow.performanceByKpiId[firstKpiId].percentLift).toEqual(0);
                expect(responseRow.performanceByKpiId[firstKpiId].isStatisticallySignificant).toEqual(false);
                expect(responseRow.performanceByKpiId[lastKpiId].met).toEqual(null);
                expect(responseRow.performanceByKpiId[lastKpiId].failed).toEqual(null);
                expect(responseRow.performanceByKpiId[lastKpiId].percentLift).toEqual(null);
                expect(responseRow.performanceByKpiId[lastKpiId].isStatisticallySignificant).toEqual(false);
            });
        },
    },
    {
        case: 'Campaign Objective filter: success 201 response, actual values',
        type: TEST_TYPES.POSITIVE,
        payload: {
            ...getReport[env],
            kpiIds: multipleKpiIdsPerEnv[env],
            advancedFilters: {
                campaignObjectives: [objectiveIdPerEnv[env]],
            },
        },
        verify: async (response: APIResponse) => {
            const responseBody: SuccessResponseBody = await response.json();
            expect(response.status()).toBe(API_RESPONSE_STATUS.CREATED);
            assertCriteriaPerformanceResultShape(responseBody.result, getReport[env].filters);
            const firstKpiId = multipleKpiIdsPerEnv[env][0];
            const lastKpiId = multipleKpiIdsPerEnv[env][multipleKpiIdsPerEnv[env].length - 1];

            responseBody.result.forEach((responseRow) => {
                expect(responseRow).toBeDefined();
                expect(responseRow.mediaCount.met).toEqual(0);
                expect(responseRow.mediaCount.failed).toEqual(10);
                expect(responseRow.mediaCount.percentageMet).toEqual(0);
                expect(responseRow.impressions.met).toEqual(0);
                expect(responseRow.impressions.failed).toEqual(3367986);
                expect(responseRow.impressions.total).toEqual(3367986);
                expect(responseRow.performanceByKpiId[firstKpiId].met).toEqual(null);
                expect(responseRow.performanceByKpiId[firstKpiId].failed).toEqual(0.754486509148);
                expect(responseRow.performanceByKpiId[firstKpiId].percentLift).toEqual(null);
                expect(responseRow.performanceByKpiId[firstKpiId].isStatisticallySignificant).toEqual(true);
                expect(responseRow.performanceByKpiId[lastKpiId].met).toEqual(null);
                expect(responseRow.performanceByKpiId[lastKpiId].failed).toEqual(null);
                expect(responseRow.performanceByKpiId[lastKpiId].percentLift).toEqual(null);
                expect(responseRow.performanceByKpiId[lastKpiId].isStatisticallySignificant).toEqual(false);
            });
        },
    },
    {
        case: 'Ad Impression filter: success 201 response, actual values',
        type: TEST_TYPES.POSITIVE,
        payload: {
            ...getReport[env],
            kpiIds: multipleKpiIdsPerEnv[env],
            advancedFilters: {
                adImpressions: {greaterThanOrEqualTo: 10000, lessThanOrEqualTo: 250000},
            },
        },
        verify: async (response: APIResponse) => {
            const responseBody: SuccessResponseBody = await response.json();
            expect(response.status()).toBe(API_RESPONSE_STATUS.CREATED);
            assertCriteriaPerformanceResultShape(responseBody.result, getReport[env].filters);
            const firstKpiId = multipleKpiIdsPerEnv[env][0];
            const lastKpiId = multipleKpiIdsPerEnv[env][multipleKpiIdsPerEnv[env].length - 1];

            responseBody.result.forEach((responseRow) => {
                expect(responseRow).toBeDefined();
                expect(responseRow.mediaCount.met).toEqual(1);
                expect(responseRow.mediaCount.failed).toEqual(9);
                expect(responseRow.mediaCount.percentageMet).toEqual(10);
                expect(responseRow.impressions.met).toEqual(231303);
                expect(responseRow.impressions.failed).toEqual(2823666);
                expect(responseRow.impressions.total).toEqual(3054969);
                expect(responseRow.performanceByKpiId[firstKpiId].met).toEqual(0.760042022801);
                expect(responseRow.performanceByKpiId[firstKpiId].failed).toEqual(0.757773759361);
                expect(responseRow.performanceByKpiId[firstKpiId].percentLift).toEqual(0.29933253982200553);
                expect(responseRow.performanceByKpiId[firstKpiId].isStatisticallySignificant).toEqual(false);
                expect(responseRow.performanceByKpiId[lastKpiId].met).toEqual(null);
                expect(responseRow.performanceByKpiId[lastKpiId].failed).toEqual(null);
                expect(responseRow.performanceByKpiId[lastKpiId].percentLift).toEqual(null);
                expect(responseRow.performanceByKpiId[lastKpiId].isStatisticallySignificant).toEqual(false);
            });
        },
    },
    {
        case: 'Ad Identifier filter: success 201 response, actual values',
        type: TEST_TYPES.POSITIVE,
        payload: {
            ...getReport[env],
            kpiIds: multipleKpiIdsPerEnv[env],
            advancedFilters: {
                adIdentifiers: [adIdPerEnv[env]],
            },
        },
        verify: async (response: APIResponse) => {
            const responseBody: SuccessResponseBody = await response.json();
            expect(response.status()).toBe(API_RESPONSE_STATUS.CREATED);
            assertCriteriaPerformanceResultShape(responseBody.result, getReport[env].filters);
            const firstKpiId = multipleKpiIdsPerEnv[env][0];
            const secondKpiId = multipleKpiIdsPerEnv[env][1];
            const lastKpiId = multipleKpiIdsPerEnv[env][multipleKpiIdsPerEnv[env].length - 1];

            responseBody.result.forEach((responseRow) => {
                expect(responseRow).toBeDefined();
                expect(responseRow.mediaCount.met).toEqual(0);
                expect(responseRow.mediaCount.failed).toEqual(1);
                expect(responseRow.mediaCount.percentageMet).toEqual(0);
                expect(responseRow.impressions.met).toEqual(0);
                expect(responseRow.impressions.failed).toEqual(258755);
                expect(responseRow.impressions.total).toEqual(258755);
                expect(responseRow.performanceByKpiId[firstKpiId].met).toEqual(null);
                expect(responseRow.performanceByKpiId[firstKpiId].failed).toEqual(0.754768023806);
                expect(responseRow.performanceByKpiId[firstKpiId].percentLift).toEqual(null);
                expect(responseRow.performanceByKpiId[firstKpiId].isStatisticallySignificant).toEqual(true);
                expect(responseRow.performanceByKpiId[secondKpiId].met).toEqual(null);
                expect(responseRow.performanceByKpiId[secondKpiId].failed).toEqual(45.981720159997);
                expect(responseRow.performanceByKpiId[secondKpiId].percentLift).toEqual(null);
                expect(responseRow.performanceByKpiId[secondKpiId].isStatisticallySignificant).toEqual(true);
                expect(responseRow.performanceByKpiId[lastKpiId].met).toEqual(null);
                expect(responseRow.performanceByKpiId[lastKpiId].failed).toEqual(null);
                expect(responseRow.performanceByKpiId[lastKpiId].percentLift).toEqual(null);
                expect(responseRow.performanceByKpiId[lastKpiId].isStatisticallySignificant).toEqual(false);
            });
        },
    },
    {
        case: 'Created by Vidmob filter (true): success 201 response, actual values',
        type: TEST_TYPES.POSITIVE,
        payload: {
            ...getReport[env],
            kpiIds: multipleKpiIdsPerEnv[env],
            advancedFilters: {
                createdByVidmob: true,
            },
        },
        verify: async (response: APIResponse) => {
            const responseBody: SuccessResponseBody = await response.json();
            expect(response.status()).toBe(API_RESPONSE_STATUS.CREATED);
            assertCriteriaPerformanceResultShape(responseBody.result, getReport[env].filters);
            const firstKpiId = multipleKpiIdsPerEnv[env][0];
            const secondKpiId = multipleKpiIdsPerEnv[env][1];
            const lastKpiId = multipleKpiIdsPerEnv[env][multipleKpiIdsPerEnv[env].length - 1];

            responseBody.result.forEach((responseRow) => {
                expect(responseRow).toBeDefined();
                expect(responseRow.mediaCount.met).toEqual(1);
                expect(responseRow.mediaCount.failed).toEqual(3);
                expect(responseRow.mediaCount.percentageMet).toEqual(25);
                expect(responseRow.impressions.met).toEqual(231303);
                expect(responseRow.impressions.failed).toEqual(709298);
                expect(responseRow.impressions.total).toEqual(940601);
                expect(responseRow.performanceByKpiId[firstKpiId].met).toEqual(0.760042022801);
                expect(responseRow.performanceByKpiId[firstKpiId].failed).toEqual(0.756240677402);
                expect(responseRow.performanceByKpiId[firstKpiId].percentLift).toEqual(0.5026634393774224);
                expect(responseRow.performanceByKpiId[firstKpiId].isStatisticallySignificant).toEqual(false);
                expect(responseRow.performanceByKpiId[secondKpiId].met).toEqual(47.185726082238);
                expect(responseRow.performanceByKpiId[secondKpiId].failed).toEqual(47.497243753683);
                expect(responseRow.performanceByKpiId[secondKpiId].percentLift).toEqual(-0.6558647340896439);
                expect(responseRow.performanceByKpiId[secondKpiId].isStatisticallySignificant).toEqual(true);
                expect(responseRow.performanceByKpiId[lastKpiId].met).toEqual(null);
                expect(responseRow.performanceByKpiId[lastKpiId].failed).toEqual(null);
                expect(responseRow.performanceByKpiId[lastKpiId].percentLift).toEqual(null);
                expect(responseRow.performanceByKpiId[lastKpiId].isStatisticallySignificant).toEqual(false);
            });
        },
    },
    {
        case: 'Created by Vidmob filter (false): success 201 response, actual values',
        type: TEST_TYPES.POSITIVE,
        payload: {
            ...getReport[env],
            kpiIds: multipleKpiIdsPerEnv[env],
            advancedFilters: {
                createdByVidmob: false,
            },
        },
        verify: async (response: APIResponse) => {
            const responseBody: SuccessResponseBody = await response.json();
            expect(response.status()).toBe(API_RESPONSE_STATUS.CREATED);
            assertCriteriaPerformanceResultShape(responseBody.result, getReport[env].filters);
            const firstKpiId = multipleKpiIdsPerEnv[env][0];
            const secondKpiId = multipleKpiIdsPerEnv[env][1];
            const lastKpiId = multipleKpiIdsPerEnv[env][multipleKpiIdsPerEnv[env].length - 1];

            responseBody.result.forEach((responseRow) => {
                expect(responseRow).toBeDefined();
                expect(responseRow.mediaCount.met).toEqual(0);
                expect(responseRow.mediaCount.failed).toEqual(8);
                expect(responseRow.mediaCount.percentageMet).toEqual(0);
                expect(responseRow.impressions.met).toEqual(0);
                expect(responseRow.impressions.failed).toEqual(2889991);
                expect(responseRow.impressions.total).toEqual(2889991);
                expect(responseRow.performanceByKpiId[firstKpiId].met).toEqual(null);
                expect(responseRow.performanceByKpiId[firstKpiId].failed).toEqual(0.754500619552);
                expect(responseRow.performanceByKpiId[firstKpiId].percentLift).toEqual(null);
                expect(responseRow.performanceByKpiId[firstKpiId].isStatisticallySignificant).toEqual(true);
                expect(responseRow.performanceByKpiId[secondKpiId].met).toEqual(null);
                expect(responseRow.performanceByKpiId[secondKpiId].failed).toEqual(46.721737195721);
                expect(responseRow.performanceByKpiId[secondKpiId].percentLift).toEqual(null);
                expect(responseRow.performanceByKpiId[secondKpiId].isStatisticallySignificant).toEqual(true);
                expect(responseRow.performanceByKpiId[lastKpiId].met).toEqual(null);
                expect(responseRow.performanceByKpiId[lastKpiId].failed).toEqual(null);
                expect(responseRow.performanceByKpiId[lastKpiId].percentLift).toEqual(null);
                expect(responseRow.performanceByKpiId[lastKpiId].isStatisticallySignificant).toEqual(false);
            });
        },
    },
    {
        case: 'Creative Impressions: success 201 response, actual values',
        type: TEST_TYPES.POSITIVE,
        payload: {
            ...getReport[env],
            kpiIds: [
                singleKpiIdPerEnv[env],
                singleKpiIdNoDataPerEnv[env],
            ],
            advancedFilters: {
                creativeImpressions: {
                    greaterThanOrEqualTo: 235000
                },
            },
        },
        verify: async (response: APIResponse) => {
            const responseBody: SuccessResponseBody = await response.json();
            expect(response.status()).toBe(API_RESPONSE_STATUS.CREATED);
            assertCriteriaPerformanceResultShape(responseBody.result, getReport[env].filters);
            const kpiId = singleKpiIdPerEnv[env];
            const kpiNoData = singleKpiIdNoDataPerEnv[env];
            responseBody.result.forEach((responseRow) => {
                expect(responseRow).toBeDefined();
                expect(responseRow.mediaCount.met).toEqual(0);
                expect(responseRow.mediaCount.failed).toEqual(9);
                expect(responseRow.mediaCount.percentageMet).toEqual(0);
                expect(responseRow.impressions.met).toEqual(0);
                expect(responseRow.impressions.failed).toEqual(3139766);
                expect(responseRow.impressions.total).toEqual( 3139766);
                expect(responseRow.performanceByKpiId[kpiId].met).toEqual( null);
                expect(responseRow.performanceByKpiId[kpiId].failed).toEqual( 0.752922351538);
                expect(responseRow.performanceByKpiId[kpiId].percentLift).toEqual(null);
                expect(responseRow.performanceByKpiId[kpiId].isStatisticallySignificant).toEqual(true);
                expect(responseRow.performanceByKpiId[kpiNoData].met).toEqual(null);
                expect(responseRow.performanceByKpiId[kpiNoData].failed).toEqual(null);
                expect(responseRow.performanceByKpiId[kpiNoData].percentLift).toEqual(null);
                expect(responseRow.performanceByKpiId[kpiNoData].isStatisticallySignificant).toEqual(false);
            });
        }
    },
    {
        case: 'Media Type[IMAGE]: success 201 response, actual values',
        type: TEST_TYPES.POSITIVE,
        payload: {
            ...getReport[env],
            kpiIds: [
                singleKpiIdPerEnv[env],
                singleKpiIdNoDataPerEnv[env],
            ],
            filters: {
                ...getReport[env].filters,
                creativeMediaType: [
                    "IMAGE"
                ]
            }
        },
        verify: async (response: APIResponse) => {
            const responseBody: SuccessResponseBody = await response.json();
            expect(response.status()).toBe(API_RESPONSE_STATUS.CREATED);
            assertCriteriaPerformanceResultShape(responseBody.result, getReport[env].filters);
            const singleKpi = singleKpiIdPerEnv[env];
            const singleKpiNoData = singleKpiIdNoDataPerEnv[env];

            responseBody.result.forEach((responseRow) => {
                expect(responseRow).toBeDefined();
                expect(responseRow.mediaCount.met).toEqual(0);
                expect(responseRow.mediaCount.failed).toEqual(8);
                expect(responseRow.mediaCount.percentageMet).toEqual( 0);
                expect(responseRow.impressions.met).toEqual(  0);
                expect(responseRow.impressions.failed).toEqual(2892123);
                expect(responseRow.impressions.total).toEqual( 2892123);
                expect(responseRow.performanceByKpiId[singleKpi].met).toEqual( null);
                expect(responseRow.performanceByKpiId[singleKpi].failed).toEqual(0.755120027744);
                expect(responseRow.performanceByKpiId[singleKpi].percentLift).toEqual(null);
                expect(responseRow.performanceByKpiId[singleKpi].isStatisticallySignificant).toEqual(true);
                expect(responseRow.performanceByKpiId[singleKpiNoData].met).toEqual(null);
                expect(responseRow.performanceByKpiId[singleKpiNoData].failed).toEqual(null);
                expect(responseRow.performanceByKpiId[singleKpiNoData].percentLift).toEqual(null);
                expect(responseRow.performanceByKpiId[singleKpiNoData].isStatisticallySignificant).toEqual(false);
            });
        }
    },
    {
        case: 'Media Type[VIDEO]: success 201 response, actual values',
        type: TEST_TYPES.POSITIVE,
        payload: {
            ...getReport[env],
            kpiIds: [
                singleKpiIdPerEnv[env],
                singleKpiIdNoDataPerEnv[env],
            ],
            filters: {
                ...getReport[env].filters,
                creativeMediaType: [
                    "VIDEO"
                ]
            }
        },
        verify: async (response: APIResponse) => {
            const responseBody: SuccessResponseBody = await response.json();
            expect(response.status()).toBe(API_RESPONSE_STATUS.CREATED);
            assertCriteriaPerformanceResultShape(responseBody.result, getReport[env].filters);
            const singleKpi = singleKpiIdPerEnv[env];
            const singleKpiNoData = singleKpiIdNoDataPerEnv[env];


            responseBody.result.forEach((responseRow) => {
                expect(responseRow).toBeDefined();
                expect(responseRow.mediaCount.met).toEqual(1);
                expect(responseRow.mediaCount.failed).toEqual(3);
                expect(responseRow.mediaCount.percentageMet).toEqual(25);
                expect(responseRow.impressions.met).toEqual( 231303);
                expect(responseRow.impressions.failed).toEqual(707166);
                expect(responseRow.impressions.total).toEqual( 938469);
                expect(responseRow.performanceByKpiId[singleKpi].met).toEqual(0.760042022801);
                expect(responseRow.performanceByKpiId[singleKpi].failed).toEqual(0.753712706776);
                expect(responseRow.performanceByKpiId[singleKpi].percentLift).toEqual(0.8397518004006559);
                expect(responseRow.performanceByKpiId[singleKpi].isStatisticallySignificant).toEqual(false);
                expect(responseRow.performanceByKpiId[singleKpiNoData].met).toEqual(null);
                expect(responseRow.performanceByKpiId[singleKpiNoData].failed).toEqual(null);
                expect(responseRow.performanceByKpiId[singleKpiNoData].percentLift).toEqual(null);
                expect(responseRow.performanceByKpiId[singleKpiNoData].isStatisticallySignificant).toEqual(false);
            });
        },
    }
];

testScenarios.forEach((scenario) => {
    test(`API test POST Criteria Performance report: Snapchat - ${scenario.case}, 
        type: ${scenario.type}, 
        tags: @apiRegressionTest @apitest @criteriaPerformance @snapchat`,
        async ({ request }) => {
            const { getURL, getLoginToken } = createPageInstances();
            const accessToken = await getLoginToken.getAccessToken(request);
            const baseURL = `${getURL.returnURL("bff-" + env)}/v1/criteria-group-report/organization/${organizationIdPerEnv[env]}`;

            const requestOptions = {
                headers: {
                    'Content-Type': 'application/json',
                    Authorization: `Bearer ${accessToken}`,
                },
                data: scenario.payload,
            }

            const response = await request.post(baseURL, requestOptions);
            await scenario.verify(response);
        });
});*/
