/*import {APIResponse, expect, test} from '@playwright/test';
import {createPageInstances} from '../../../../../../utils/helper';
import {API_RESPONSE_STATUS, TEST_TYPES} from '../../../../../../utils/util-constants';
// @ts-ignore
import * as getReport from '../../../../../../payload/bff/criteria-performance/getReportFacebook';
import {SuccessResponseBody} from '../../../../../../utils/scoring.types';
import {CriteriaPerformanceReportRow} from '../../../../../../utils/analytics.types';
import {assertCriteriaPerformanceResultShape} from "./verifyUtils";

const env = process.env.TESTENV || 'dev';

const organizationIdPerEnv = {
    dev: '27a7e882-43de-4bfa-8f53-3b62875b8432',
}

const KPI_CLICK_THROUGH_RATE_ID_DEV = '7';
const KPI_VTR_25_ID_DEV = '1';
const KPI_COST_PER_SUBSCRIPTION_ID_DEV = '405';

const singleKpiIdPerEnv = {
    dev: KPI_CLICK_THROUGH_RATE_ID_DEV,
};

const singleKpiIdNoDataPerEnv = {
    dev: KPI_COST_PER_SUBSCRIPTION_ID_DEV,
};

const multipleKpiIdsPerEnv = {
    dev: [KPI_CLICK_THROUGH_RATE_ID_DEV, KPI_VTR_25_ID_DEV, KPI_COST_PER_SUBSCRIPTION_ID_DEV],
};

const campaignIdPerEnv = {
    dev: '6291137663002',
};

const objectiveIdPerEnv = {
    dev: 'OUTCOME_TRAFFIC'
};

const adSetIdPerEnv = {
    dev: '6294868207002',
};

const adIdPerEnv = {
    dev: '6299984982202',
};

const adTypesPerEnv = {
    dev: [
        "desktop:facebook:video_feeds",
        "mobile_app:facebook:video_feeds",
        "mobile:facebook:video_feeds",
        "unknown:facebook:video_feeds",
    ],
};

const adPlacementsPerEnv = {
    dev: 'desktop:facebook:feed',
};

const testScenarios = [
    {
        case: 'no KPI: success 201 response, actual values',
        type: TEST_TYPES.POSITIVE,
        payload: getReport[env],
        verify: async (response: APIResponse) => {
            const responseBody: SuccessResponseBody = await response.json();
            expect(response.status()).toBe(API_RESPONSE_STATUS.CREATED);
            assertCriteriaPerformanceResultShape(responseBody.result, getReport[env].filters);
            responseBody.result.forEach((responseRow) => {
                expect(responseRow).toBeDefined();
                expect(responseRow.performanceByKpiId).toEqual({});
                expect(responseRow.mediaCount.met).toEqual(6);
                expect(responseRow.mediaCount.failed).toEqual(5);
                expect(responseRow.mediaCount.percentageMet).toEqual(54.54545454545454);
                expect(responseRow.impressions.met).toEqual(49788);
                expect(responseRow.impressions.failed).toEqual(51990);
                expect(responseRow.impressions.total).toEqual(101778);
            });
        },
    },
    {
        case: 'no KPI: success 201, no data',
        type: TEST_TYPES.POSITIVE,
        payload: {
            ...getReport[env],
            "filters": {
                "criteriaGroupIds": ['unknown']
            },
        },
        verify: async (response: APIResponse) => {
            const responseBody: SuccessResponseBody = await response.json();
            expect(response.status()).toBe(API_RESPONSE_STATUS.CREATED);
            expect(responseBody.result).toEqual([]);
        },
    },
    {
        case: '1 KPI: success 201 response, actual values',
        type: TEST_TYPES.POSITIVE,
        payload: {
            ...getReport[env],
            kpiIds: [singleKpiIdPerEnv[env]],
        },
        verify: async (response: APIResponse) => {
            const responseBody: SuccessResponseBody = await response.json();
            expect(response.status()).toBe(API_RESPONSE_STATUS.CREATED);
            assertCriteriaPerformanceResultShape(responseBody.result, getReport[env].filters);

            responseBody.result.forEach((responseRow) => {
                const kpiId = singleKpiIdPerEnv[env];
                expect(responseRow).toBeDefined();
                expect(Object.keys(responseRow.performanceByKpiId[kpiId])).toHaveLength(4);
                expect(responseRow.performanceByKpiId[kpiId].met).toEqual( 0.642725154656);
                expect(responseRow.performanceByKpiId[kpiId].failed).toEqual(1.696480092325);
                expect(responseRow.performanceByKpiId[kpiId].percentLift).toEqual(-62.11419411499518);
                expect(responseRow.performanceByKpiId[kpiId].isStatisticallySignificant).toEqual(true);
            });
        },
    },
    {
        case: '1 KPI: success 201 response, no data',
        type: TEST_TYPES.POSITIVE,
        payload: {
            ...getReport[env],
            kpiIds: [singleKpiIdNoDataPerEnv[env]],
        },
        verify: async (response: APIResponse) => {
            const responseBody: SuccessResponseBody = await response.json();
            expect(response.status()).toBe(API_RESPONSE_STATUS.CREATED);
            assertCriteriaPerformanceResultShape(responseBody.result, getReport[env].filters);

            responseBody.result.forEach((responseRow) => {
                const kpiId = singleKpiIdNoDataPerEnv[env];
                expect(responseRow).toBeDefined();
                expect(Object.keys(responseRow.performanceByKpiId[kpiId])).toHaveLength(4);
                expect(responseRow.performanceByKpiId[kpiId].met).toEqual(null);
                expect(responseRow.performanceByKpiId[kpiId].failed).toEqual(null);
                expect(responseRow.performanceByKpiId[kpiId].percentLift).toEqual(null);
                expect(responseRow.performanceByKpiId[kpiId].isStatisticallySignificant).toEqual(false);
            });
        },
    },
    {
        case: 'multiple KPIs: success 201 response',
        type: TEST_TYPES.POSITIVE,
        payload: {
            ...getReport[env],
            kpiIds: multipleKpiIdsPerEnv[env],
        },
        verify: async (response: APIResponse) => {
            const responseBody: SuccessResponseBody = await response.json();
            expect(response.status()).toBe(API_RESPONSE_STATUS.CREATED);
            assertCriteriaPerformanceResultShape(responseBody.result, getReport[env].filters);

            responseBody.result.forEach((responseRow) => {
                expect(responseRow).toBeDefined();
                expect(Object.keys(responseRow.performanceByKpiId)).toHaveLength(3);
            });
        },
    },
    {
        case: 'Campaign Identifier filter: success 201 response, actual values',
        type: TEST_TYPES.POSITIVE,
        payload: {
            ...getReport[env],
            kpiIds: multipleKpiIdsPerEnv[env],
            advancedFilters: {
                campaignIdentifiers: [campaignIdPerEnv[env]],
            },
        },
        verify: async (response: APIResponse) => {
            const responseBody: SuccessResponseBody = await response.json();
            expect(response.status()).toBe(API_RESPONSE_STATUS.CREATED);
            assertCriteriaPerformanceResultShape(responseBody.result, getReport[env].filters);
            const firstKpiId = multipleKpiIdsPerEnv[env][0];
            const lastKpiId = multipleKpiIdsPerEnv[env][multipleKpiIdsPerEnv[env].length - 1];

            responseBody.result.forEach((responseRow) => {
                expect(responseRow).toBeDefined();
                expect(responseRow.mediaCount.met).toEqual(5);
                expect(responseRow.mediaCount.failed).toEqual(0);
                expect(responseRow.mediaCount.percentageMet).toEqual(100);
                expect(responseRow.impressions.met).toEqual(41639);
                expect(responseRow.impressions.failed).toEqual(0);
                expect(responseRow.impressions.total).toEqual(41639);
                expect(responseRow.performanceByKpiId[firstKpiId].met).toEqual(0.535555608924);
                expect(responseRow.performanceByKpiId[firstKpiId].failed).toEqual(null);
                expect(responseRow.performanceByKpiId[firstKpiId].percentLift).toEqual(null);
                expect(responseRow.performanceByKpiId[firstKpiId].isStatisticallySignificant).toEqual(true);
                expect(responseRow.performanceByKpiId[lastKpiId].met).toEqual(null);
                expect(responseRow.performanceByKpiId[lastKpiId].failed).toEqual(null);
                expect(responseRow.performanceByKpiId[lastKpiId].percentLift).toEqual(null);
                expect(responseRow.performanceByKpiId[lastKpiId].isStatisticallySignificant).toEqual(false);
            });
        },
    },
    {
        case: 'Campaign Objective filter: success 201 response, actual values',
        type: TEST_TYPES.POSITIVE,
        payload: {
            ...getReport[env],
            kpiIds: multipleKpiIdsPerEnv[env],
            advancedFilters: {
                campaignObjectives: [objectiveIdPerEnv[env]],
            },
        },
        verify: async (response: APIResponse) => {
            const responseBody: SuccessResponseBody = await response.json();
            expect(response.status()).toBe(API_RESPONSE_STATUS.CREATED);
            assertCriteriaPerformanceResultShape(responseBody.result, getReport[env].filters);
            const firstKpiId = multipleKpiIdsPerEnv[env][0];
            const lastKpiId = multipleKpiIdsPerEnv[env][multipleKpiIdsPerEnv[env].length - 1];

            responseBody.result.forEach((responseRow) => {
                expect(responseRow).toBeDefined();
                expect(responseRow.mediaCount.met).toEqual(0);
                expect(responseRow.mediaCount.failed).toEqual(4);
                expect(responseRow.mediaCount.percentageMet).toEqual(0);
                expect(responseRow.impressions.met).toEqual(0);
                expect(responseRow.impressions.failed).toEqual(32880);
                expect(responseRow.impressions.total).toEqual(32880);
                expect(responseRow.performanceByKpiId[firstKpiId].met).toEqual(null);
                expect(responseRow.performanceByKpiId[firstKpiId].failed).toEqual(1.441605839416);
                expect(responseRow.performanceByKpiId[firstKpiId].percentLift).toEqual(null);
                expect(responseRow.performanceByKpiId[firstKpiId].isStatisticallySignificant).toEqual(true);
                expect(responseRow.performanceByKpiId[lastKpiId].met).toEqual(null);
                expect(responseRow.performanceByKpiId[lastKpiId].failed).toEqual(null);
                expect(responseRow.performanceByKpiId[lastKpiId].percentLift).toEqual(null);
                expect(responseRow.performanceByKpiId[lastKpiId].isStatisticallySignificant).toEqual(false);
            });
        },
    },
    {
        case: 'Ad Set Identifier filter: success 201 response, actual values',
        type: TEST_TYPES.POSITIVE,
        payload: {
            ...getReport[env],
            kpiIds: multipleKpiIdsPerEnv[env],
            advancedFilters: {
                adsetIdentifiers: [adSetIdPerEnv[env]],
            },
        },
        verify: async (response: APIResponse) => {
            const responseBody: SuccessResponseBody = await response.json();
            expect(response.status()).toBe(API_RESPONSE_STATUS.CREATED);
            assertCriteriaPerformanceResultShape(responseBody.result, getReport[env].filters);
            const firstKpiId = multipleKpiIdsPerEnv[env][0];
            const lastKpiId = multipleKpiIdsPerEnv[env][multipleKpiIdsPerEnv[env].length - 1];

            responseBody.result.forEach((responseRow) => {
                expect(responseRow).toBeDefined();
                expect(responseRow.mediaCount.met).toEqual(4);
                expect(responseRow.mediaCount.failed).toEqual(0);
                expect(responseRow.mediaCount.percentageMet).toEqual(100);
                expect(responseRow.impressions.met).toEqual( 36970);
                expect(responseRow.impressions.failed).toEqual(0);
                expect(responseRow.impressions.total).toEqual(36970);
                expect(responseRow.performanceByKpiId[firstKpiId].met).toEqual( 0.53827427644,);
                expect(responseRow.performanceByKpiId[firstKpiId].failed).toEqual(null);
                expect(responseRow.performanceByKpiId[firstKpiId].percentLift).toEqual(null);
                expect(responseRow.performanceByKpiId[firstKpiId].isStatisticallySignificant).toEqual(true);
                expect(responseRow.performanceByKpiId[lastKpiId].met).toEqual(null);
                expect(responseRow.performanceByKpiId[lastKpiId].failed).toEqual(null);
                expect(responseRow.performanceByKpiId[lastKpiId].percentLift).toEqual(null);
                expect(responseRow.performanceByKpiId[lastKpiId].isStatisticallySignificant).toEqual(false);
            });
        },
    },
    {
        case: 'Ad Impression filter: success 201 response, actual values',
        type: TEST_TYPES.POSITIVE,
        payload: {
            ...getReport[env],
            kpiIds: multipleKpiIdsPerEnv[env],
            advancedFilters: {
                adImpressions: {greaterThanOrEqualTo: 1000, lessThanOrEqualTo: 50000},
            },
        },
        verify: async (response: APIResponse) => {
            const responseBody: SuccessResponseBody = await response.json();
            expect(response.status()).toBe(API_RESPONSE_STATUS.CREATED);
            assertCriteriaPerformanceResultShape(responseBody.result, getReport[env].filters);
            const firstKpiId = multipleKpiIdsPerEnv[env][0];
            const lastKpiId = multipleKpiIdsPerEnv[env][multipleKpiIdsPerEnv[env].length - 1];

            responseBody.result.forEach((responseRow) => {
                expect(responseRow).toBeDefined();
                expect(responseRow.mediaCount.met).toEqual(5);
                expect(responseRow.mediaCount.failed).toEqual(5);
                expect(responseRow.mediaCount.percentageMet).toEqual(50);
                expect(responseRow.impressions.met).toEqual(47525);
                expect(responseRow.impressions.failed).toEqual( 49884);
                expect(responseRow.impressions.total).toEqual(97409);
                expect(responseRow.performanceByKpiId[firstKpiId].met).toEqual( 0.66280904787);
                expect(responseRow.performanceByKpiId[firstKpiId].failed).toEqual(1.752064790314);
                expect(responseRow.performanceByKpiId[firstKpiId].percentLift).toEqual(-62.169832329590214);
                expect(responseRow.performanceByKpiId[firstKpiId].isStatisticallySignificant).toEqual(true);
                expect(responseRow.performanceByKpiId[lastKpiId].met).toEqual(null);
                expect(responseRow.performanceByKpiId[lastKpiId].failed).toEqual(null);
                expect(responseRow.performanceByKpiId[lastKpiId].percentLift).toEqual(null);
                expect(responseRow.performanceByKpiId[lastKpiId].isStatisticallySignificant).toEqual(false);
            });
        },
    },
    {
        case: 'Ad Identifier filter: success 201 response, actual values',
        type: TEST_TYPES.POSITIVE,
        payload: {
            ...getReport[env],
            kpiIds: multipleKpiIdsPerEnv[env],
            advancedFilters: {
                adIdentifiers: [adIdPerEnv[env]],
            },
        },
        verify: async (response: APIResponse) => {
            const responseBody: SuccessResponseBody = await response.json();
            expect(response.status()).toBe(API_RESPONSE_STATUS.CREATED);
            assertCriteriaPerformanceResultShape(responseBody.result, getReport[env].filters);
            const firstKpiId = multipleKpiIdsPerEnv[env][0];
            const secondKpiId = multipleKpiIdsPerEnv[env][1];
            const lastKpiId = multipleKpiIdsPerEnv[env][multipleKpiIdsPerEnv[env].length - 1];

            responseBody.result.forEach((responseRow) => {
                expect(responseRow).toBeDefined();
                expect(responseRow.mediaCount.met).toEqual(1);
                expect(responseRow.mediaCount.failed).toEqual(0);
                expect(responseRow.mediaCount.percentageMet).toEqual(100);
                expect(responseRow.impressions.met).toEqual(125);
                expect(responseRow.impressions.failed).toEqual(0);
                expect(responseRow.impressions.total).toEqual(125);
                expect(responseRow.performanceByKpiId[firstKpiId].met).toEqual(0);
                expect(responseRow.performanceByKpiId[firstKpiId].failed).toEqual(null);
                expect(responseRow.performanceByKpiId[firstKpiId].percentLift).toEqual(null);
                expect(responseRow.performanceByKpiId[firstKpiId].isStatisticallySignificant).toEqual(false);
                expect(responseRow.performanceByKpiId[secondKpiId].met).toEqual(9.6);
                expect(responseRow.performanceByKpiId[secondKpiId].failed).toEqual(null);
                expect(responseRow.performanceByKpiId[secondKpiId].percentLift).toEqual(null);
                expect(responseRow.performanceByKpiId[secondKpiId].isStatisticallySignificant).toEqual(true);
                expect(responseRow.performanceByKpiId[lastKpiId].met).toEqual(null);
                expect(responseRow.performanceByKpiId[lastKpiId].failed).toEqual(null);
                expect(responseRow.performanceByKpiId[lastKpiId].percentLift).toEqual(null);
                expect(responseRow.performanceByKpiId[lastKpiId].isStatisticallySignificant).toEqual(false);
            });
        },
    },
    {
        case: 'Ad Type filter: success 201 response, actual values',
        type: TEST_TYPES.POSITIVE,
        payload: {
            ...getReport[env],
            kpiIds: multipleKpiIdsPerEnv[env],
            advancedFilters: {
                adTypes: adTypesPerEnv[env],
            },
        },
        verify: async (response: APIResponse) => {
            const responseBody: SuccessResponseBody = await response.json();
            expect(response.status()).toBe(API_RESPONSE_STATUS.CREATED);
            assertCriteriaPerformanceResultShape(responseBody.result, getReport[env].filters);
            const firstKpiId = multipleKpiIdsPerEnv[env][0];
            const secondKpiId = multipleKpiIdsPerEnv[env][1];
            const lastKpiId = multipleKpiIdsPerEnv[env][multipleKpiIdsPerEnv[env].length - 1];

            responseBody.result.forEach((responseRow) => {
                expect(responseRow).toBeDefined();
                expect(responseRow.mediaCount.met).toEqual(1);
                expect(responseRow.mediaCount.failed).toEqual(1);
                expect(responseRow.mediaCount.percentageMet).toEqual(50);
                expect(responseRow.impressions.met).toEqual(71);
                expect(responseRow.impressions.failed).toEqual(700);
                expect(responseRow.impressions.total).toEqual(771);
                expect(responseRow.performanceByKpiId[firstKpiId].met).toEqual(0);
                expect(responseRow.performanceByKpiId[firstKpiId].failed).toEqual( 6.142857142857);
                expect(responseRow.performanceByKpiId[firstKpiId].percentLift).toEqual(null);
                expect(responseRow.performanceByKpiId[firstKpiId].isStatisticallySignificant).toEqual(true);
                expect(responseRow.performanceByKpiId[secondKpiId].met).toEqual( 5.633802817);
                expect(responseRow.performanceByKpiId[secondKpiId].failed).toEqual(8);
                expect(responseRow.performanceByKpiId[secondKpiId].percentLift).toEqual(-29.577464787499995);
                expect(responseRow.performanceByKpiId[secondKpiId].isStatisticallySignificant).toEqual(false);
                expect(responseRow.performanceByKpiId[lastKpiId].met).toEqual(null);
                expect(responseRow.performanceByKpiId[lastKpiId].failed).toEqual(null);
                expect(responseRow.performanceByKpiId[lastKpiId].percentLift).toEqual(null);
                expect(responseRow.performanceByKpiId[lastKpiId].isStatisticallySignificant).toEqual(false);
            });
        },
    },
    {
        case: 'Ad Placement filter: success 201 response, actual values',
        type: TEST_TYPES.POSITIVE,
        payload: {
            ...getReport[env],
            kpiIds: multipleKpiIdsPerEnv[env],
            advancedFilters: {
                adPlacements: [adPlacementsPerEnv[env]],
            },
        },
        verify: async (response: APIResponse) => {
            const responseBody: SuccessResponseBody = await response.json();
            expect(response.status()).toBe(API_RESPONSE_STATUS.CREATED);
            assertCriteriaPerformanceResultShape(responseBody.result, getReport[env].filters);
            const firstKpiId = multipleKpiIdsPerEnv[env][0];
            const secondKpiId = multipleKpiIdsPerEnv[env][1];
            const lastKpiId = multipleKpiIdsPerEnv[env][multipleKpiIdsPerEnv[env].length - 1];

            responseBody.result.forEach((responseRow) => {
                expect(responseRow).toBeDefined();
                expect(responseRow.mediaCount.met).toEqual(6);
                expect(responseRow.mediaCount.failed).toEqual(2);
                expect(responseRow.mediaCount.percentageMet).toEqual(75);
                expect(responseRow.impressions.met).toEqual(804);
                expect(responseRow.impressions.failed).toEqual(765);
                expect(responseRow.impressions.total).toEqual(1569);
                expect(responseRow.performanceByKpiId[firstKpiId].met).toEqual(0.248756218905);
                expect(responseRow.performanceByKpiId[firstKpiId].failed).toEqual( 2.222222222222);
                expect(responseRow.performanceByKpiId[firstKpiId].percentLift).toEqual(-88.80597014927388);
                expect(responseRow.performanceByKpiId[firstKpiId].isStatisticallySignificant).toEqual(true);
                expect(responseRow.performanceByKpiId[secondKpiId].met).toEqual(11.44278607);
                expect(responseRow.performanceByKpiId[secondKpiId].failed).toEqual(7.712418301);
                expect(responseRow.performanceByKpiId[secondKpiId].percentLift).toEqual(48.36832785011567);
                expect(responseRow.performanceByKpiId[secondKpiId].isStatisticallySignificant).toEqual(true);
                expect(responseRow.performanceByKpiId[lastKpiId].met).toEqual(null);
                expect(responseRow.performanceByKpiId[lastKpiId].failed).toEqual(null);
                expect(responseRow.performanceByKpiId[lastKpiId].percentLift).toEqual(null);
                expect(responseRow.performanceByKpiId[lastKpiId].isStatisticallySignificant).toEqual(false);
            });
        },
    },
    {
        case: 'Created by Vidmob filter (true): success 201 response, actual values',
        type: TEST_TYPES.POSITIVE,
        payload: {
            ...getReport[env],
            kpiIds: multipleKpiIdsPerEnv[env],
            startDate: '2018-01-01',
            advancedFilters: {
                createdByVidmob: true,
            },
        },
        verify: async (response: APIResponse) => {
            const responseBody: SuccessResponseBody = await response.json();
            expect(response.status()).toBe(API_RESPONSE_STATUS.CREATED);
            assertCriteriaPerformanceResultShape(responseBody.result, getReport[env].filters);
            const firstKpiId = multipleKpiIdsPerEnv[env][0];
            const secondKpiId = multipleKpiIdsPerEnv[env][1];
            const lastKpiId = multipleKpiIdsPerEnv[env][multipleKpiIdsPerEnv[env].length - 1];

            responseBody.result.forEach((responseRow) => {
                expect(responseRow).toBeDefined();
                expect(responseRow.mediaCount.met).toEqual(4);
                expect(responseRow.mediaCount.failed).toEqual(46);
                expect(responseRow.mediaCount.percentageMet).toEqual(8);
                expect(responseRow.impressions.met).toEqual(20620);
                expect(responseRow.impressions.failed).toEqual(2201238);
                expect(responseRow.impressions.total).toEqual(2221858);
                expect(responseRow.performanceByKpiId[firstKpiId].met).toEqual(1.008729388943);
                expect(responseRow.performanceByKpiId[firstKpiId].failed).toEqual(1.397622610549);
                expect(responseRow.performanceByKpiId[firstKpiId].percentLift).toEqual(-27.82533844763994);
                expect(responseRow.performanceByKpiId[firstKpiId].isStatisticallySignificant).toEqual(true);
                expect(responseRow.performanceByKpiId[secondKpiId].met).toEqual(null);
                expect(responseRow.performanceByKpiId[secondKpiId].failed).toEqual(3.289376251);
                expect(responseRow.performanceByKpiId[secondKpiId].percentLift).toEqual(null);
                expect(responseRow.performanceByKpiId[secondKpiId].isStatisticallySignificant).toEqual(true);
                expect(responseRow.performanceByKpiId[lastKpiId].met).toEqual(null);
                expect(responseRow.performanceByKpiId[lastKpiId].failed).toEqual(null);
                expect(responseRow.performanceByKpiId[lastKpiId].percentLift).toEqual(null);
                expect(responseRow.performanceByKpiId[lastKpiId].isStatisticallySignificant).toEqual(false);
            });
        },
    },
    {
        case: 'Created by Vidmob filter (false): success 201 response, actual values',
        type: TEST_TYPES.POSITIVE,
        payload: {
            ...getReport[env],
            kpiIds: multipleKpiIdsPerEnv[env],
            startDate: '2018-01-01',
            advancedFilters: {
                createdByVidmob: false,
            },
        },
        verify: async (response: APIResponse) => {
            const responseBody: SuccessResponseBody = await response.json();
            expect(response.status()).toBe(API_RESPONSE_STATUS.CREATED);
            assertCriteriaPerformanceResultShape(responseBody.result, getReport[env].filters);
            const firstKpiId = multipleKpiIdsPerEnv[env][0];
            const secondKpiId = multipleKpiIdsPerEnv[env][1];
            const lastKpiId = multipleKpiIdsPerEnv[env][multipleKpiIdsPerEnv[env].length - 1];

            responseBody.result.forEach((responseRow) => {
                expect(responseRow).toBeDefined();
                expect(responseRow.mediaCount.met).toEqual(118);
                expect(responseRow.mediaCount.failed).toEqual(108);
                expect(responseRow.mediaCount.percentageMet).toEqual( 52.21238938053098);
                expect(responseRow.impressions.met).toEqual( 9808217);
                expect(responseRow.impressions.failed).toEqual(14581943);
                expect(responseRow.impressions.total).toEqual(24390160);
                expect(responseRow.performanceByKpiId[firstKpiId].met).toEqual(1.010265168481);
                expect(responseRow.performanceByKpiId[firstKpiId].failed).toEqual(1.022264316902);
                expect(responseRow.performanceByKpiId[firstKpiId].percentLift).toEqual(-1.17378140101415);
                expect(responseRow.performanceByKpiId[firstKpiId].isStatisticallySignificant).toEqual(true);
                expect(responseRow.performanceByKpiId[secondKpiId].met).toEqual(5.175731736);
                expect(responseRow.performanceByKpiId[secondKpiId].failed).toEqual( 4.563500214);
                expect(responseRow.performanceByKpiId[secondKpiId].percentLift).toEqual(13.41583199934523);
                expect(responseRow.performanceByKpiId[secondKpiId].isStatisticallySignificant).toEqual(true);
                expect(responseRow.performanceByKpiId[lastKpiId].met).toEqual(null);
                expect(responseRow.performanceByKpiId[lastKpiId].failed).toEqual(null);
                expect(responseRow.performanceByKpiId[lastKpiId].percentLift).toEqual(null);
                expect(responseRow.performanceByKpiId[lastKpiId].isStatisticallySignificant).toEqual(false);
            });
        },
    },
    {
        case: 'Creative Impressions filter: success 201 response, actual values',
        type: TEST_TYPES.POSITIVE,
        payload: {
            ...getReport[env],
            kpiIds: multipleKpiIdsPerEnv[env],
            startDate: '2018-01-01',
            advancedFilters: {
                creativeImpressions: {
                    greaterThanOrEqualTo: 10000
                },
            },
        },
        verify: async (response: APIResponse) => {
            const responseBody: SuccessResponseBody = await response.json();
            expect(response.status()).toBe(API_RESPONSE_STATUS.CREATED);
            assertCriteriaPerformanceResultShape(responseBody.result, getReport[env].filters);
            const firstKpiId = multipleKpiIdsPerEnv[env][0];
            const secondKpiId = multipleKpiIdsPerEnv[env][1];
            const lastKpiId = multipleKpiIdsPerEnv[env][multipleKpiIdsPerEnv[env].length - 1];

            responseBody.result.forEach((responseRow) => {
                expect(responseRow).toBeDefined();
                expect(responseRow.mediaCount.met).toEqual(48);
                expect(responseRow.mediaCount.failed).toEqual(56);
                expect(responseRow.mediaCount.percentageMet).toEqual( 46.15384615384615);
                expect(responseRow.impressions.met).toEqual( 7775842);
                expect(responseRow.impressions.failed).toEqual(10650561);
                expect(responseRow.impressions.total).toEqual( 18426403);
                expect(responseRow.performanceByKpiId[firstKpiId].met).toEqual( 0.927372238273);
                expect(responseRow.performanceByKpiId[firstKpiId].failed).toEqual(1.139686444686);
                expect(responseRow.performanceByKpiId[firstKpiId].percentLift).toEqual(-18.62917712173857);
                expect(responseRow.performanceByKpiId[firstKpiId].isStatisticallySignificant).toEqual(true);
                expect(responseRow.performanceByKpiId[secondKpiId].met).toEqual(6.330118333);
                expect(responseRow.performanceByKpiId[secondKpiId].failed).toEqual( 6.743992171);
                expect(responseRow.performanceByKpiId[secondKpiId].percentLift).toEqual( -6.13692643030799);
                expect(responseRow.performanceByKpiId[secondKpiId].isStatisticallySignificant).toEqual(true);
                expect(responseRow.performanceByKpiId[lastKpiId].met).toEqual(null);
                expect(responseRow.performanceByKpiId[lastKpiId].failed).toEqual(null);
                expect(responseRow.performanceByKpiId[lastKpiId].percentLift).toEqual(null);
                expect(responseRow.performanceByKpiId[lastKpiId].isStatisticallySignificant).toEqual(false);
            });
        },
    },
    {
        case: 'Media Type[IMAGE]: success 201 response, no data',
        type: TEST_TYPES.POSITIVE,
        payload: {
            ...getReport[env],
            kpiIds: multipleKpiIdsPerEnv[env],
            startDate: '2018-01-01',
            filters: {
                ...getReport[env].filters,
                creativeMediaType: [
                    "IMAGE"
                ]
            }
        },
        verify: async (response: APIResponse) => {
            const responseBody: SuccessResponseBody = await response.json();
            expect(response.status()).toBe(API_RESPONSE_STATUS.CREATED);
            // The criteria is in these tests is sound on which is video onl
            expect(responseBody.result).toEqual([]);
        },
    },
    {
        case: 'Media Type[VIDEO]: success 201 response, actual values',
        type: TEST_TYPES.POSITIVE,
        payload: {
            ...getReport[env],
            kpiIds: multipleKpiIdsPerEnv[env],
            startDate: '2018-01-01',
            filters: {
                ...getReport[env].filters,
                creativeMediaType: [
                    "VIDEO"
                ]
            }
        },
        verify: async (response: APIResponse) => {
            const responseBody: SuccessResponseBody = await response.json();
            expect(response.status()).toBe(API_RESPONSE_STATUS.CREATED);
            assertCriteriaPerformanceResultShape(responseBody.result, getReport[env].filters);
            const firstKpiId = multipleKpiIdsPerEnv[env][0];
            const secondKpiId = multipleKpiIdsPerEnv[env][1];
            const lastKpiId = multipleKpiIdsPerEnv[env][multipleKpiIdsPerEnv[env].length - 1];

            responseBody.result.forEach((responseRow) => {
                expect(responseRow).toBeDefined();
                expect(responseRow.mediaCount.met).toEqual(118);
                expect(responseRow.mediaCount.failed).toEqual(140);
                expect(responseRow.mediaCount.percentageMet).toEqual( 45.73643410852713);
                expect(responseRow.impressions.met).toEqual(  7920545);
                expect(responseRow.impressions.failed).toEqual(10919559);
                expect(responseRow.impressions.total).toEqual( 18840104);
                expect(responseRow.performanceByKpiId[firstKpiId].met).toEqual( 0.925466618774);
                expect(responseRow.performanceByKpiId[firstKpiId].failed).toEqual(1.139926987894);
                expect(responseRow.performanceByKpiId[firstKpiId].percentLift).toEqual(-18.813518005763914);
                expect(responseRow.performanceByKpiId[firstKpiId].isStatisticallySignificant).toEqual(true);
                expect(responseRow.performanceByKpiId[secondKpiId].met).toEqual(6.395052361);
                expect(responseRow.performanceByKpiId[secondKpiId].failed).toEqual(  6.733669373);
                expect(responseRow.performanceByKpiId[secondKpiId].percentLift).toEqual( -5.028714557292527);
                expect(responseRow.performanceByKpiId[secondKpiId].isStatisticallySignificant).toEqual(true);
                expect(responseRow.performanceByKpiId[lastKpiId].met).toEqual(null);
                expect(responseRow.performanceByKpiId[lastKpiId].failed).toEqual(null);
                expect(responseRow.performanceByKpiId[lastKpiId].percentLift).toEqual(null);
                expect(responseRow.performanceByKpiId[lastKpiId].isStatisticallySignificant).toEqual(false);
            });
        },
    }
];

testScenarios.forEach((scenario) => {
    test(`API test POST Criteria Performance report: Facebook - ${scenario.case}, 
        type: ${scenario.type}, 
        tags: @apiRegressionTest @apitest @criteriaPerformance @facebook`,
    async ({ request }) => {
        const { getURL, getLoginToken } = createPageInstances();
        const accessToken = await getLoginToken.getAccessToken(request);
        const baseURL = `${getURL.returnURL("bff-" + env)}/v1/criteria-group-report/organization/${organizationIdPerEnv[env]}`;

        const requestOptions = {
            headers: {
                'Content-Type': 'application/json',
                Authorization: `Bearer ${accessToken}`,
            },
            data: scenario.payload,
        }

        const response = await request.post(baseURL, requestOptions);
        await scenario.verify(response);
    });
});*/
