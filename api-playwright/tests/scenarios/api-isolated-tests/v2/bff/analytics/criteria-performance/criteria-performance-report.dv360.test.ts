/*import {APIResponse, expect, test} from '@playwright/test';
import {createPageInstances} from '../../../../../../utils/helper';
import {API_RESPONSE_STATUS, TEST_TYPES} from '../../../../../../utils/util-constants';
// @ts-ignore
import * as getReport from '../../../../../../payload/bff/criteria-performance/getReportDV360';
import {SuccessResponseBody} from '../../../../../../utils/scoring.types';
import {CriteriaPerformanceReportRow} from '../../../../../../utils/analytics.types';
import {assertCriteriaPerformanceResultShape} from "./verifyUtils";

const env = process.env.TESTENV || 'dev';

const organizationIdPerEnv = {
    dev: '27a7e882-43de-4bfa-8f53-3b62875b8432',
}

const KPI_CLICK_THROUGH_RATE_ID_DEV = '114';
const KPI_TRUEVIEW_VIEW_RATE_ID_DEV = '323';

const singleKpiIdPerEnv = {
    dev: KPI_CLICK_THROUGH_RATE_ID_DEV,
};

const multipleKpiIdsPerEnv = {
    dev: [KPI_CLICK_THROUGH_RATE_ID_DEV, KPI_TRUEVIEW_VIEW_RATE_ID_DEV],
};

const campaignIdPerEnv = {
    dev: '53418724',
};

const objectiveIdPerEnv = {
    dev: 'Raise awareness of my brand or product',
};

const adIdPerEnv = {
    dev: '682698704532',
};

const adTypePerEnv = {
    dev: 'Efficient reach',
};


const testScenarios = [
    {
        case: 'no KPI: success 201 response, actual values',
        type: TEST_TYPES.POSITIVE,
        payload: getReport[env],
        verify: async (response: APIResponse) => {
            const responseBody: SuccessResponseBody = await response.json();
            expect(response.status()).toBe(API_RESPONSE_STATUS.CREATED);
            assertCriteriaPerformanceResultShape(responseBody.result, getReport[env].filters);
            responseBody.result.forEach((responseRow) => {
                expect(responseRow).toBeDefined();
                expect(responseRow.performanceByKpiId).toEqual({});
                expect(responseRow.mediaCount.met).toEqual(18);
                expect(responseRow.mediaCount.failed).toEqual(1);
                expect(responseRow.mediaCount.percentageMet).toEqual(94.73684210526315);
                expect(responseRow.impressions.met).toEqual(62219758);
                expect(responseRow.impressions.failed).toEqual(3610437);
                expect(responseRow.impressions.total).toEqual(65830195);
            });
        },
    },
    {
        case: 'no KPI: success 201, no data',
        type: TEST_TYPES.POSITIVE,
        payload: {
            ...getReport[env],
            "filters": {
                "criteriaGroupIds": ['unknown']
            },
        },
        verify: async (response: APIResponse) => {
            const responseBody: SuccessResponseBody = await response.json();
            expect(response.status()).toBe(API_RESPONSE_STATUS.CREATED);
            expect(responseBody.result).toEqual([]);
        },
    },
    {
        case: '1 KPI: success 201 response, actual values',
        type: TEST_TYPES.POSITIVE,
        payload: {
            ...getReport[env],
            kpiIds: [singleKpiIdPerEnv[env]],
        },
        verify: async (response: APIResponse) => {
            const responseBody: SuccessResponseBody = await response.json();
            expect(response.status()).toBe(API_RESPONSE_STATUS.CREATED);
            assertCriteriaPerformanceResultShape(responseBody.result, getReport[env].filters);

            responseBody.result.forEach((responseRow) => {
                const kpiId = singleKpiIdPerEnv[env];
                expect(responseRow).toBeDefined();
                expect(Object.keys(responseRow.performanceByKpiId[kpiId])).toHaveLength(4);
                expect(responseRow.performanceByKpiId[kpiId].met).toEqual(0.072356437002);
                expect(responseRow.performanceByKpiId[kpiId].failed).toEqual(0.192109708603);
                expect(responseRow.performanceByKpiId[kpiId].percentLift).toEqual(-62.33587696937975);
                expect(responseRow.performanceByKpiId[kpiId].isStatisticallySignificant).toEqual(true);
            });
        },
    },
    {
        case: 'multiple KPIs: success 201 response',
        type: TEST_TYPES.POSITIVE,
        payload: {
            ...getReport[env],
            kpiIds: multipleKpiIdsPerEnv[env],
        },
        verify: async (response: APIResponse) => {
            const responseBody: SuccessResponseBody = await response.json();
            expect(response.status()).toBe(API_RESPONSE_STATUS.CREATED);
            assertCriteriaPerformanceResultShape(responseBody.result, getReport[env].filters);

            responseBody.result.forEach((responseRow) => {
                expect(responseRow).toBeDefined();
                expect(Object.keys(responseRow.performanceByKpiId)).toHaveLength(2);
            });
        },
    },
    {
        case: 'Campaign Identifier filter: success 201 response, actual values',
        type: TEST_TYPES.POSITIVE,
        payload: {
            ...getReport[env],
            kpiIds: multipleKpiIdsPerEnv[env],
            advancedFilters: {
                campaignIdentifiers: [campaignIdPerEnv[env]],
            },
        },
        verify: async (response: APIResponse) => {
            const responseBody: SuccessResponseBody = await response.json();
            expect(response.status()).toBe(API_RESPONSE_STATUS.CREATED);
            assertCriteriaPerformanceResultShape(responseBody.result, getReport[env].filters);
            const firstKpiId = multipleKpiIdsPerEnv[env][0];
            const lastKpiId = multipleKpiIdsPerEnv[env][multipleKpiIdsPerEnv[env].length - 1];

            responseBody.result.forEach((responseRow) => {
                expect(responseRow).toBeDefined();
                expect(responseRow.mediaCount.met).toEqual(5);
                expect(responseRow.mediaCount.failed).toEqual(1);
                expect(responseRow.mediaCount.percentageMet).toEqual(83.33333333333334);
                expect(responseRow.impressions.met).toEqual(31994527);
                expect(responseRow.impressions.failed).toEqual(3610437);
                expect(responseRow.impressions.total).toEqual(35604964);
                expect(responseRow.performanceByKpiId[firstKpiId].met).toEqual(0.096825935261);
                expect(responseRow.performanceByKpiId[firstKpiId].failed).toEqual(0.192109708603);
                expect(responseRow.performanceByKpiId[firstKpiId].percentLift).toEqual(-49.59862467904032);
                expect(responseRow.performanceByKpiId[firstKpiId].isStatisticallySignificant).toEqual(true);
                expect(responseRow.performanceByKpiId[lastKpiId].met).toEqual(0);
                expect(responseRow.performanceByKpiId[lastKpiId].failed).toEqual(0);
                expect(responseRow.performanceByKpiId[lastKpiId].percentLift).toEqual(null);
                expect(responseRow.performanceByKpiId[lastKpiId].isStatisticallySignificant).toEqual(false);
            });
        },
    },
    {
        case: 'Campaign Objective filter: success 201 response',
        type: TEST_TYPES.POSITIVE,
        payload: {
            ...getReport[env],
            kpiIds: multipleKpiIdsPerEnv[env],
            advancedFilters: {
                campaignObjectives: [objectiveIdPerEnv[env]],
            },
        },
        verify: async (response: APIResponse) => {
            const responseBody: SuccessResponseBody = await response.json();
            expect(response.status()).toBe(API_RESPONSE_STATUS.CREATED);
            assertCriteriaPerformanceResultShape(responseBody.result, getReport[env].filters);

            responseBody.result.forEach((responseRow) => {
                expect(responseRow).toBeDefined();
                expect(Object.keys(responseRow.performanceByKpiId)).toHaveLength(2);
            });
        },
    },
    {
        case: 'Ad Impression filter: success 201 response, actual values',
        type: TEST_TYPES.POSITIVE,
        payload: {
            ...getReport[env],
            kpiIds: multipleKpiIdsPerEnv[env],
            advancedFilters: {
                adImpressions: {greaterThanOrEqualTo: 500000, lessThanOrEqualTo: 6000000},
            },
        },
        verify: async (response: APIResponse) => {
            const responseBody: SuccessResponseBody = await response.json();
            expect(response.status()).toBe(API_RESPONSE_STATUS.CREATED);
            assertCriteriaPerformanceResultShape(responseBody.result, getReport[env].filters);
            const firstKpiId = multipleKpiIdsPerEnv[env][0];
            const lastKpiId = multipleKpiIdsPerEnv[env][multipleKpiIdsPerEnv[env].length - 1];

            responseBody.result.forEach((responseRow) => {
                expect(responseRow).toBeDefined();
                expect(responseRow.mediaCount.met).toEqual(14);
                expect(responseRow.mediaCount.failed).toEqual(1);
                expect(responseRow.mediaCount.percentageMet).toEqual(93.33333333333333);
                expect(responseRow.impressions.met).toEqual(52117763);
                expect(responseRow.impressions.failed).toEqual(2934917);
                expect(responseRow.impressions.total).toEqual(55052680);
                expect(responseRow.performanceByKpiId[firstKpiId].met).toEqual(0.078197907305);
                expect(responseRow.performanceByKpiId[firstKpiId].failed).toEqual(0.194008893608);
                expect(responseRow.performanceByKpiId[firstKpiId].percentLift).toEqual(-59.693648136048395);
                expect(responseRow.performanceByKpiId[firstKpiId].isStatisticallySignificant).toEqual(true);
                expect(responseRow.performanceByKpiId[lastKpiId].met).toEqual(0);
                expect(responseRow.performanceByKpiId[lastKpiId].failed).toEqual(0);
                expect(responseRow.performanceByKpiId[lastKpiId].percentLift).toEqual(null);
                expect(responseRow.performanceByKpiId[lastKpiId].isStatisticallySignificant).toEqual(false);
            });
        },
    },
    {
        case: 'Ad Identifier filter: success 201 response, actual values',
        type: TEST_TYPES.POSITIVE,
        payload: {
            ...getReport[env],
            kpiIds: multipleKpiIdsPerEnv[env],
            advancedFilters: {
                adIdentifiers: [adIdPerEnv[env]],
            },
        },
        verify: async (response: APIResponse) => {
            const responseBody: SuccessResponseBody = await response.json();
            expect(response.status()).toBe(API_RESPONSE_STATUS.CREATED);
            assertCriteriaPerformanceResultShape(responseBody.result, getReport[env].filters);
            const firstKpiId = multipleKpiIdsPerEnv[env][0];
            const lastKpiId = multipleKpiIdsPerEnv[env][multipleKpiIdsPerEnv[env].length - 1];

            responseBody.result.forEach((responseRow) => {
                expect(responseRow).toBeDefined();
                expect(responseRow.mediaCount.met).toEqual(1);
                expect(responseRow.mediaCount.failed).toEqual(0);
                expect(responseRow.mediaCount.percentageMet).toEqual(100);
                expect(responseRow.impressions.met).toEqual(3008769);
                expect(responseRow.impressions.failed).toEqual(0);
                expect(responseRow.impressions.total).toEqual(3008769);
                expect(responseRow.performanceByKpiId[firstKpiId].met).toEqual(0.067303272534);
                expect(responseRow.performanceByKpiId[firstKpiId].failed).toEqual(null);
                expect(responseRow.performanceByKpiId[firstKpiId].percentLift).toEqual(null);
                expect(responseRow.performanceByKpiId[firstKpiId].isStatisticallySignificant).toEqual(true);
                expect(responseRow.performanceByKpiId[lastKpiId].met).toEqual(0);
                expect(responseRow.performanceByKpiId[lastKpiId].failed).toEqual(null);
                expect(responseRow.performanceByKpiId[lastKpiId].percentLift).toEqual(null);
                expect(responseRow.performanceByKpiId[lastKpiId].isStatisticallySignificant).toEqual(false);
            });
        },
    },
    {
        case: 'Ad Type filter: success 201 response, actual values',
        type: TEST_TYPES.POSITIVE,
        payload: {
            ...getReport[env],
            kpiIds: multipleKpiIdsPerEnv[env],
            advancedFilters: {
                adTypes: [adTypePerEnv[env]],
            },
        },
        verify: async (response: APIResponse) => {
            const responseBody: SuccessResponseBody = await response.json();
            expect(response.status()).toBe(API_RESPONSE_STATUS.CREATED);
            assertCriteriaPerformanceResultShape(responseBody.result, getReport[env].filters);
            const firstKpiId = multipleKpiIdsPerEnv[env][0];
            const lastKpiId = multipleKpiIdsPerEnv[env][multipleKpiIdsPerEnv[env].length - 1];

            responseBody.result.forEach((responseRow) => {
                expect(responseRow).toBeDefined();
                expect(responseRow.mediaCount.met).toEqual(7);
                expect(responseRow.mediaCount.failed).toEqual(0);
                expect(responseRow.mediaCount.percentageMet).toEqual(100);
                expect(responseRow.impressions.met).toEqual(27847399);
                expect(responseRow.impressions.failed).toEqual(0);
                expect(responseRow.impressions.total).toEqual(27847399);
                expect(responseRow.performanceByKpiId[firstKpiId].met).toEqual(0.090356733137);
                expect(responseRow.performanceByKpiId[firstKpiId].failed).toEqual(null);
                expect(responseRow.performanceByKpiId[firstKpiId].percentLift).toEqual(null);
                expect(responseRow.performanceByKpiId[firstKpiId].isStatisticallySignificant).toEqual(true);
                expect(responseRow.performanceByKpiId[lastKpiId].met).toEqual(0);
                expect(responseRow.performanceByKpiId[lastKpiId].failed).toEqual(null);
                expect(responseRow.performanceByKpiId[lastKpiId].percentLift).toEqual(null);
                expect(responseRow.performanceByKpiId[lastKpiId].isStatisticallySignificant).toEqual(false);
            });
        },
    },
    {
        case: 'Created by Vidmob filter (true): success 201 response, no data',
        type: TEST_TYPES.POSITIVE,
        payload: {
            ...getReport[env],
            kpiIds: multipleKpiIdsPerEnv[env],
            advancedFilters: {
                createdByVidmob: true,
            },
        },
        verify: async (response: APIResponse) => {
            const responseBody: SuccessResponseBody = await response.json();
            expect(response.status()).toBe(API_RESPONSE_STATUS.CREATED);
            expect(responseBody.result).toEqual([]);
        },
    },
    {
        case: 'Creative Impressions filter: success 201 response, actual values',
        type: TEST_TYPES.POSITIVE,
        payload: {
            ...getReport[env],
            kpiIds: multipleKpiIdsPerEnv[env],
            advancedFilters: {
                creativeImpressions: {
                    greaterThanOrEqualTo: 2000000
                },
            },
        },
        verify: async (response: APIResponse) => {
            const responseBody: SuccessResponseBody = await response.json();
            expect(response.status()).toBe(API_RESPONSE_STATUS.CREATED);
            assertCriteriaPerformanceResultShape(responseBody.result, getReport[env].filters);
            const firstKpiId = multipleKpiIdsPerEnv[env][0];
            const lastKpiId = multipleKpiIdsPerEnv[env][multipleKpiIdsPerEnv[env].length - 1];

            responseBody.result.forEach((responseRow) => {
                expect(responseRow).toBeDefined();
                expect(responseRow.mediaCount.met).toEqual(11);
                expect(responseRow.mediaCount.failed).toEqual(1);
                expect(responseRow.mediaCount.percentageMet).toEqual(91.66666666666666);
                expect(responseRow.impressions.met).toEqual(57106127);
                expect(responseRow.impressions.failed).toEqual(3610437);
                expect(responseRow.impressions.total).toEqual(60716564);
                expect(responseRow.performanceByKpiId[firstKpiId].met).toEqual(0.072589408839);
                expect(responseRow.performanceByKpiId[firstKpiId].failed).toEqual(0.192109708603);
                expect(responseRow.performanceByKpiId[firstKpiId].percentLift).toEqual(-62.21460676461281);
                expect(responseRow.performanceByKpiId[firstKpiId].isStatisticallySignificant).toEqual(true);
                expect(responseRow.performanceByKpiId[lastKpiId].met).toEqual(0);
                expect(responseRow.performanceByKpiId[lastKpiId].failed).toEqual(0);
                expect(responseRow.performanceByKpiId[lastKpiId].percentLift).toEqual(null);
                expect(responseRow.performanceByKpiId[lastKpiId].isStatisticallySignificant).toEqual(false);
            });
        },
    },
    {
        case: 'Media Type[Image]: success 201 response, actual values',
        type: TEST_TYPES.POSITIVE,
        payload: {
            ...getReport[env],
            kpiIds: multipleKpiIdsPerEnv[env],
            startDate: '2018-01-01',
            filters: {
                ...getReport[env].filters,
                creativeMediaType: [
                    "IMAGE"
                ]
            }
        },
        verify: async (response: APIResponse) => {
            const responseBody: SuccessResponseBody = await response.json();
            expect(response.status()).toBe(API_RESPONSE_STATUS.CREATED);
            // The kpi in the test report is video only, so filterig for images should return no data here
            expect(responseBody.result).toEqual([]);
        },
    },
    {
        case: 'Media Type[Video]: success 201 response, actual values',
        type: TEST_TYPES.POSITIVE,
        payload: {
            ...getReport[env],
            kpiIds: multipleKpiIdsPerEnv[env],
            startDate: '2018-01-01',
            filters: {
                ...getReport[env].filters,
                creativeMediaType: [
                    "VIDEO"
                ]
            }
        },
        verify: async (response: APIResponse) => {
            const responseBody: SuccessResponseBody = await response.json();
            expect(response.status()).toBe(API_RESPONSE_STATUS.CREATED);
            assertCriteriaPerformanceResultShape(responseBody.result, getReport[env].filters);
            const firstKpiId = multipleKpiIdsPerEnv[env][0];
            const secondKpiId = multipleKpiIdsPerEnv[env][1];
            const lastKpiId = multipleKpiIdsPerEnv[env][multipleKpiIdsPerEnv[env].length - 1];


            // The criteria in this request is video only, which means it's already sort of not including images
            // expect these results to exactly match the version of this report with no filter
            responseBody.result.forEach((responseRow) => {
                expect(responseRow).toBeDefined();
                expect(responseRow.mediaCount.met).toEqual(18);
                expect(responseRow.mediaCount.failed).toEqual(4);
                expect(responseRow.mediaCount.percentageMet).toEqual( 81.81818181818183);
                expect(responseRow.impressions.met).toEqual( 62219758);
                expect(responseRow.impressions.failed).toEqual(3629162);
                expect(responseRow.impressions.total).toEqual(65848920);
                expect(responseRow.performanceByKpiId[firstKpiId].met).toEqual(0.072356437002);
                expect(responseRow.performanceByKpiId[firstKpiId].failed).toEqual(0.191669592044);
                expect(responseRow.performanceByKpiId[firstKpiId].percentLift).toEqual(-62.24939165864676);
                expect(responseRow.performanceByKpiId[firstKpiId].isStatisticallySignificant).toEqual(true);
                expect(responseRow.performanceByKpiId[lastKpiId].met).toEqual(0);
                expect(responseRow.performanceByKpiId[lastKpiId].failed).toEqual(0.003802530722);
                expect(responseRow.performanceByKpiId[lastKpiId].percentLift).toEqual(null);
                expect(responseRow.performanceByKpiId[lastKpiId].isStatisticallySignificant).toEqual(true);
            });
        },
    },
];

testScenarios.forEach((scenario) => {
    test(`API test POST Criteria Performance report: DV360 - ${scenario.case}, 
        type: ${scenario.type}, 
        tags: @apiRegressionTest @apitest @criteriaPerformance @dv360`,
        async ({ request }) => {
            const { getURL, getLoginToken } = createPageInstances();

            const accessToken = await getLoginToken.getAccessToken(request);
            const baseURL = `${getURL.returnURL("bff-" + env)}/v1/criteria-group-report/organization/${organizationIdPerEnv[env]}`;

            const requestOptions = {
                headers: {
                    'Content-Type': 'application/json',
                    Authorization: `Bearer ${accessToken}`,
                },
                data: scenario.payload,
            }

            const response = await request.post(baseURL, requestOptions);
            await scenario.verify(response);
        });
});*/
