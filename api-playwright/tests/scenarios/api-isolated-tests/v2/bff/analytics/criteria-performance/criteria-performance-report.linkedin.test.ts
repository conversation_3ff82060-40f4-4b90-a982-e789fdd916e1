/*import {APIResponse, expect, test} from '@playwright/test';
import {createPageInstances} from '../../../../../../utils/helper';
import {API_RESPONSE_STATUS, TEST_TYPES} from '../../../../../../utils/util-constants';
// @ts-ignore
import * as getReport from '../../../../../../payload/bff/criteria-performance/getReportLinkedIn';
import {SuccessResponseBody} from '../../../../../../utils/scoring.types';
import {CriteriaPerformanceReportRow} from '../../../../../../utils/analytics.types';
import {assertCriteriaPerformanceResultShape} from "./verifyUtils";

const env = process.env.TESTENV || 'dev';

const organizationIdPerEnv = {
    dev: '27a7e882-43de-4bfa-8f53-3b62875b8432',
}

const KPI_CLICK_THROUGH_RATE_ID_DEV = '160';
const KPI_VTR_25_ID_DEV = '145';
const KPI_RETURN_ON_AD_SPEND_ID_DEV = '586';

const singleKpiIdPerEnv = {
    dev: KPI_CLICK_THROUGH_RATE_ID_DEV,
};

const singleKpiIdNoDataPerEnv = {
    dev: KPI_RETURN_ON_AD_SPEND_ID_DEV,
};

const multipleKpiIdsPerEnv = {
    dev: [KPI_CLICK_THROUGH_RATE_ID_DEV, KPI_VTR_25_ID_DEV, KPI_RETURN_ON_AD_SPEND_ID_DEV],
};

const campaignIdPerEnv = {
    dev: '196329843',
};

const objectiveIdPerEnv = {
    dev: 'WEBSITE_VISIT'
};

const adIdPerEnv = {
    dev: '201262873',
};

const adPlacementsPerEnv = {
    dev: 'OFF_SITE',
};

const testScenarios = [
    {
        case: 'no KPI: success 201 response, actual values',
        type: TEST_TYPES.POSITIVE,
        payload: getReport[env],
        verify: async (response: APIResponse) => {
            const responseBody: SuccessResponseBody = await response.json();
            expect(response.status()).toBe(API_RESPONSE_STATUS.CREATED);
            assertCriteriaPerformanceResultShape(responseBody.result, getReport[env].filters);
            responseBody.result.forEach((responseRow) => {
                expect(responseRow).toBeDefined();
                expect(responseRow.performanceByKpiId).toEqual({});
                expect(responseRow.mediaCount.met).toEqual(14);
                expect(responseRow.mediaCount.failed).toEqual(7);
                expect(responseRow.mediaCount.percentageMet).toEqual(66.66666666666666);
                expect(responseRow.impressions.met).toEqual(289661);
                expect(responseRow.impressions.failed).toEqual(432024);
                expect(responseRow.impressions.total).toEqual(721685);
            });
        },
    },
    {
        case: 'no KPI: success 201, no data',
        type: TEST_TYPES.POSITIVE,
        payload: {
            ...getReport[env],
            "filters": {
                "criteriaGroupIds": ['unknown']
            },
        },
        verify: async (response: APIResponse) => {
            const responseBody: SuccessResponseBody = await response.json();
            expect(response.status()).toBe(API_RESPONSE_STATUS.CREATED);
            expect(responseBody.result).toEqual([]);
        },
    },
    {
        case: '1 KPI: success 201 response, actual values',
        type: TEST_TYPES.POSITIVE,
        payload: {
            ...getReport[env],
            kpiIds: [singleKpiIdPerEnv[env]],
        },
        verify: async (response: APIResponse) => {
            const responseBody: SuccessResponseBody = await response.json();
            expect(response.status()).toBe(API_RESPONSE_STATUS.CREATED);
            assertCriteriaPerformanceResultShape(responseBody.result, getReport[env].filters);

            responseBody.result.forEach((responseRow) => {
                const kpiId = singleKpiIdPerEnv[env];
                expect(responseRow).toBeDefined();
                expect(Object.keys(responseRow.performanceByKpiId[kpiId])).toHaveLength(4);
                expect(responseRow.performanceByKpiId[kpiId].met).toEqual(0.432229399194);
                expect(responseRow.performanceByKpiId[kpiId].failed).toEqual(0.269892413384);
                expect(responseRow.performanceByKpiId[kpiId].percentLift).toEqual(60.14877697915454);
                expect(responseRow.performanceByKpiId[kpiId].isStatisticallySignificant).toEqual(true);
            });
        },
    },
    {
        case: '1 KPI: success 201 response, no data',
        type: TEST_TYPES.POSITIVE,
        payload: {
            ...getReport[env],
            kpiIds: [singleKpiIdNoDataPerEnv[env]],
        },
        verify: async (response: APIResponse) => {
            const responseBody: SuccessResponseBody = await response.json();
            expect(response.status()).toBe(API_RESPONSE_STATUS.CREATED);
            assertCriteriaPerformanceResultShape(responseBody.result, getReport[env].filters);

            responseBody.result.forEach((responseRow) => {
                const kpiId = singleKpiIdNoDataPerEnv[env];
                expect(responseRow).toBeDefined();
                expect(Object.keys(responseRow.performanceByKpiId[kpiId])).toHaveLength(4);
                expect(responseRow.performanceByKpiId[kpiId].met).toEqual(null);
                expect(responseRow.performanceByKpiId[kpiId].failed).toEqual(null);
                expect(responseRow.performanceByKpiId[kpiId].percentLift).toEqual(null);
                expect(responseRow.performanceByKpiId[kpiId].isStatisticallySignificant).toEqual(false);
            });
        },
    },
    {
        case: 'multiple KPIs: success 201 response',
        type: TEST_TYPES.POSITIVE,
        payload: {
            ...getReport[env],
            kpiIds: multipleKpiIdsPerEnv[env],
        },
        verify: async (response: APIResponse) => {
            const responseBody: SuccessResponseBody = await response.json();
            expect(response.status()).toBe(API_RESPONSE_STATUS.CREATED);
            assertCriteriaPerformanceResultShape(responseBody.result, getReport[env].filters);

            responseBody.result.forEach((responseRow) => {
                expect(responseRow).toBeDefined();
                expect(Object.keys(responseRow.performanceByKpiId)).toHaveLength(3);
            });
        },
    },
    {
        case: 'Campaign Identifier filter: success 201 response, actual values',
        type: TEST_TYPES.POSITIVE,
        payload: {
            ...getReport[env],
            kpiIds: multipleKpiIdsPerEnv[env],
            advancedFilters: {
                campaignIdentifiers: [campaignIdPerEnv[env]],
            },
        },
        verify: async (response: APIResponse) => {
            const responseBody: SuccessResponseBody = await response.json();
            expect(response.status()).toBe(API_RESPONSE_STATUS.CREATED);
            assertCriteriaPerformanceResultShape(responseBody.result, getReport[env].filters);
            const firstKpiId = multipleKpiIdsPerEnv[env][0];
            const lastKpiId = multipleKpiIdsPerEnv[env][multipleKpiIdsPerEnv[env].length - 1];

            responseBody.result.forEach((responseRow) => {
                expect(responseRow).toBeDefined();
                expect(responseRow.mediaCount.met).toEqual(10);
                expect(responseRow.mediaCount.failed).toEqual(3);
                expect(responseRow.mediaCount.percentageMet).toEqual(76.92307692307693);
                expect(responseRow.impressions.met).toEqual(243135);
                expect(responseRow.impressions.failed).toEqual(0);
                expect(responseRow.impressions.total).toEqual(243135);
                expect(responseRow.performanceByKpiId[firstKpiId].met).toEqual(0.424455549386);
                expect(responseRow.performanceByKpiId[firstKpiId].failed).toEqual(null);
                expect(responseRow.performanceByKpiId[firstKpiId].percentLift).toEqual(null);
                expect(responseRow.performanceByKpiId[firstKpiId].isStatisticallySignificant).toEqual(true);
                expect(responseRow.performanceByKpiId[lastKpiId].met).toEqual(null);
                expect(responseRow.performanceByKpiId[lastKpiId].failed).toEqual(null);
                expect(responseRow.performanceByKpiId[lastKpiId].percentLift).toEqual(null);
                expect(responseRow.performanceByKpiId[lastKpiId].isStatisticallySignificant).toEqual(false);
            });
        },
    },
    {
        case: 'Campaign Objective filter: success 201 response, actual values',
        type: TEST_TYPES.POSITIVE,
        payload: {
            ...getReport[env],
            kpiIds: multipleKpiIdsPerEnv[env],
            advancedFilters: {
                campaignObjectives: [objectiveIdPerEnv[env]],
            },
        },
        verify: async (response: APIResponse) => {
            const responseBody: SuccessResponseBody = await response.json();
            expect(response.status()).toBe(API_RESPONSE_STATUS.CREATED);
            assertCriteriaPerformanceResultShape(responseBody.result, getReport[env].filters);
            const firstKpiId = multipleKpiIdsPerEnv[env][0];
            const lastKpiId = multipleKpiIdsPerEnv[env][multipleKpiIdsPerEnv[env].length - 1];

            responseBody.result.forEach((responseRow) => {
                expect(responseRow).toBeDefined();
                expect(responseRow.mediaCount.met).toEqual(4);
                expect(responseRow.mediaCount.failed).toEqual(2);
                expect(responseRow.mediaCount.percentageMet).toEqual(66.66666666666666);
                expect(responseRow.impressions.met).toEqual(37230);
                expect(responseRow.impressions.failed).toEqual(432024);
                expect(responseRow.impressions.total).toEqual(469254);
                expect(responseRow.performanceByKpiId[firstKpiId].met).toEqual(0.470051034112);
                expect(responseRow.performanceByKpiId[firstKpiId].failed).toEqual(0.269892413384);
                expect(responseRow.performanceByKpiId[firstKpiId].percentLift).toEqual(74.16237389496996);
                expect(responseRow.performanceByKpiId[firstKpiId].isStatisticallySignificant).toEqual(true);
                expect(responseRow.performanceByKpiId[lastKpiId].met).toEqual(null);
                expect(responseRow.performanceByKpiId[lastKpiId].failed).toEqual(null);
                expect(responseRow.performanceByKpiId[lastKpiId].percentLift).toEqual(null);
                expect(responseRow.performanceByKpiId[lastKpiId].isStatisticallySignificant).toEqual(false);
            });
        },
    },
    {
        case: 'Ad Impression filter: success 201 response, actual values',
        type: TEST_TYPES.POSITIVE,
        payload: {
            ...getReport[env],
            kpiIds: multipleKpiIdsPerEnv[env],
            advancedFilters: {
                adImpressions: {greaterThanOrEqualTo: 1000, lessThanOrEqualTo: 100000},
            },
        },
        verify: async (response: APIResponse) => {
            const responseBody: SuccessResponseBody = await response.json();
            expect(response.status()).toBe(API_RESPONSE_STATUS.CREATED);
            assertCriteriaPerformanceResultShape(responseBody.result, getReport[env].filters);
            const firstKpiId = multipleKpiIdsPerEnv[env][0];
            const lastKpiId = multipleKpiIdsPerEnv[env][multipleKpiIdsPerEnv[env].length - 1];

            responseBody.result.forEach((responseRow) => {
                expect(responseRow).toBeDefined();
                expect(responseRow.mediaCount.met).toEqual(12);
                expect(responseRow.mediaCount.failed).toEqual(2);
                expect(responseRow.mediaCount.percentageMet).toEqual(85.71428571428571);
                expect(responseRow.impressions.met).toEqual(287240);
                expect(responseRow.impressions.failed).toEqual(69613);
                expect(responseRow.impressions.total).toEqual(356853);
                expect(responseRow.performanceByKpiId[firstKpiId].met).toEqual(0.433087313745);
                expect(responseRow.performanceByKpiId[firstKpiId].failed).toEqual(0.281556605806);
                expect(responseRow.performanceByKpiId[firstKpiId].percentLift).toEqual(53.818914141694364);
                expect(responseRow.performanceByKpiId[firstKpiId].isStatisticallySignificant).toEqual(true);
                expect(responseRow.performanceByKpiId[lastKpiId].met).toEqual(null);
                expect(responseRow.performanceByKpiId[lastKpiId].failed).toEqual(null);
                expect(responseRow.performanceByKpiId[lastKpiId].percentLift).toEqual(null);
                expect(responseRow.performanceByKpiId[lastKpiId].isStatisticallySignificant).toEqual(false);
            });
        },
    },
    {
        case: 'Ad Identifier filter: success 201 response, actual values',
        type: TEST_TYPES.POSITIVE,
        payload: {
            ...getReport[env],
            kpiIds: multipleKpiIdsPerEnv[env],
            advancedFilters: {
                adIdentifiers: [adIdPerEnv[env]],
            },
        },
        verify: async (response: APIResponse) => {
            const responseBody: SuccessResponseBody = await response.json();
            expect(response.status()).toBe(API_RESPONSE_STATUS.CREATED);
            assertCriteriaPerformanceResultShape(responseBody.result, getReport[env].filters);
            const firstKpiId = multipleKpiIdsPerEnv[env][0];
            const secondKpiId = multipleKpiIdsPerEnv[env][1];
            const lastKpiId = multipleKpiIdsPerEnv[env][multipleKpiIdsPerEnv[env].length - 1];

            responseBody.result.forEach((responseRow) => {
                expect(responseRow).toBeDefined();
                expect(responseRow.mediaCount.met).toEqual(1);
                expect(responseRow.mediaCount.failed).toEqual(0);
                expect(responseRow.mediaCount.percentageMet).toEqual(100);
                expect(responseRow.impressions.met).toEqual(11940);
                expect(responseRow.impressions.failed).toEqual(0);
                expect(responseRow.impressions.total).toEqual(11940);
                expect(responseRow.performanceByKpiId[firstKpiId].met).toEqual(0.36850921273);
                expect(responseRow.performanceByKpiId[firstKpiId].failed).toEqual(null);
                expect(responseRow.performanceByKpiId[firstKpiId].percentLift).toEqual(null);
                expect(responseRow.performanceByKpiId[firstKpiId].isStatisticallySignificant).toEqual(true);
                expect(responseRow.performanceByKpiId[secondKpiId].met).toEqual(19.597989949749);
                expect(responseRow.performanceByKpiId[secondKpiId].failed).toEqual(null);
                expect(responseRow.performanceByKpiId[secondKpiId].percentLift).toEqual(null);
                expect(responseRow.performanceByKpiId[secondKpiId].isStatisticallySignificant).toEqual(true);
                expect(responseRow.performanceByKpiId[lastKpiId].met).toEqual(null);
                expect(responseRow.performanceByKpiId[lastKpiId].failed).toEqual(null);
                expect(responseRow.performanceByKpiId[lastKpiId].percentLift).toEqual(null);
                expect(responseRow.performanceByKpiId[lastKpiId].isStatisticallySignificant).toEqual(false);
            });
        },
    },
    {
        case: 'Ad Placement filter: success 201 response, actual values',
        type: TEST_TYPES.POSITIVE,
        payload: {
            ...getReport[env],
            kpiIds: multipleKpiIdsPerEnv[env],
            advancedFilters: {
                adPlacements: [adPlacementsPerEnv[env]],
            },
        },
        verify: async (response: APIResponse) => {
            const responseBody: SuccessResponseBody = await response.json();
            expect(response.status()).toBe(API_RESPONSE_STATUS.CREATED);
            assertCriteriaPerformanceResultShape(responseBody.result, getReport[env].filters);
            const firstKpiId = multipleKpiIdsPerEnv[env][0];
            const secondKpiId = multipleKpiIdsPerEnv[env][1];
            const lastKpiId = multipleKpiIdsPerEnv[env][multipleKpiIdsPerEnv[env].length - 1];

            responseBody.result.forEach((responseRow) => {
                expect(responseRow).toBeDefined();
                expect(responseRow.mediaCount.met).toEqual(0);
                expect(responseRow.mediaCount.failed).toEqual(2);
                expect(responseRow.mediaCount.percentageMet).toEqual(0);
                expect(responseRow.impressions.met).toEqual(0);
                expect(responseRow.impressions.failed).toEqual(431468);
                expect(responseRow.impressions.total).toEqual(431468);
                expect(responseRow.performanceByKpiId[firstKpiId].met).toEqual(null);
                expect(responseRow.performanceByKpiId[firstKpiId].failed).toEqual(0.269776669417);
                expect(responseRow.performanceByKpiId[firstKpiId].percentLift).toEqual(null);
                expect(responseRow.performanceByKpiId[firstKpiId].isStatisticallySignificant).toEqual(true);
                expect(responseRow.performanceByKpiId[secondKpiId].met).toEqual(null);
                expect(responseRow.performanceByKpiId[secondKpiId].failed).toEqual(81.111924870442);
                expect(responseRow.performanceByKpiId[secondKpiId].percentLift).toEqual(null);
                expect(responseRow.performanceByKpiId[secondKpiId].isStatisticallySignificant).toEqual(true);
                expect(responseRow.performanceByKpiId[lastKpiId].met).toEqual(null);
                expect(responseRow.performanceByKpiId[lastKpiId].failed).toEqual(null);
                expect(responseRow.performanceByKpiId[lastKpiId].percentLift).toEqual(null);
                expect(responseRow.performanceByKpiId[lastKpiId].isStatisticallySignificant).toEqual(false);
            });
        },
    },
    {
        case: 'Created by Vidmob filter (true): success 201 response, actual values',
        type: TEST_TYPES.POSITIVE,
        payload: {
            ...getReport[env],
            kpiIds: multipleKpiIdsPerEnv[env],
            startDate: '2018-01-01',
            advancedFilters: {
                createdByVidmob: true,
            },
        },
        verify: async (response: APIResponse) => {
            const responseBody: SuccessResponseBody = await response.json();
            expect(response.status()).toBe(API_RESPONSE_STATUS.CREATED);
            assertCriteriaPerformanceResultShape(responseBody.result, getReport[env].filters);
            const firstKpiId = multipleKpiIdsPerEnv[env][0];
            const secondKpiId = multipleKpiIdsPerEnv[env][1];
            const lastKpiId = multipleKpiIdsPerEnv[env][multipleKpiIdsPerEnv[env].length - 1];

            responseBody.result.forEach((responseRow) => {
                expect(responseRow).toBeDefined();
                expect(responseRow.mediaCount.met).toEqual(0);
                expect(responseRow.mediaCount.failed).toEqual(37);
                expect(responseRow.mediaCount.percentageMet).toEqual(0);
                expect(responseRow.impressions.met).toEqual(0);
                expect(responseRow.impressions.failed).toEqual(199301);
                expect(responseRow.impressions.total).toEqual(199301);
                expect(responseRow.performanceByKpiId[firstKpiId].met).toEqual(null);
                expect(responseRow.performanceByKpiId[firstKpiId].failed).toEqual(0.419466033788);
                expect(responseRow.performanceByKpiId[firstKpiId].percentLift).toEqual(null);
                expect(responseRow.performanceByKpiId[firstKpiId].isStatisticallySignificant).toEqual(true);
                expect(responseRow.performanceByKpiId[secondKpiId].met).toEqual(null);
                expect(responseRow.performanceByKpiId[secondKpiId].failed).toEqual(31.540233114736);
                expect(responseRow.performanceByKpiId[secondKpiId].percentLift).toEqual(null);
                expect(responseRow.performanceByKpiId[secondKpiId].isStatisticallySignificant).toEqual(true);
                expect(responseRow.performanceByKpiId[lastKpiId].met).toEqual(null);
                expect(responseRow.performanceByKpiId[lastKpiId].failed).toEqual(null);
                expect(responseRow.performanceByKpiId[lastKpiId].percentLift).toEqual(null);
                expect(responseRow.performanceByKpiId[lastKpiId].isStatisticallySignificant).toEqual(false);
            });
        },
    },
    {
        case: 'Created by Vidmob filter (false): success 201 response, actual values',
        type: TEST_TYPES.POSITIVE,
        payload: {
            ...getReport[env],
            kpiIds: multipleKpiIdsPerEnv[env],
            startDate: '2018-01-01',
            advancedFilters: {
                createdByVidmob: false,
            },
        },
        verify: async (response: APIResponse) => {
            const responseBody: SuccessResponseBody = await response.json();
            expect(response.status()).toBe(API_RESPONSE_STATUS.CREATED);
            assertCriteriaPerformanceResultShape(responseBody.result, getReport[env].filters);
            const firstKpiId = multipleKpiIdsPerEnv[env][0];
            const secondKpiId = multipleKpiIdsPerEnv[env][1];
            const lastKpiId = multipleKpiIdsPerEnv[env][multipleKpiIdsPerEnv[env].length - 1];

            responseBody.result.forEach((responseRow) => {
                expect(responseRow).toBeDefined();
                expect(responseRow.mediaCount.met).toEqual(24);
                expect(responseRow.mediaCount.failed).toEqual(71);
                expect(responseRow.mediaCount.percentageMet).toEqual(25.263157894736842);
                expect(responseRow.impressions.met).toEqual(793127);
                expect(responseRow.impressions.failed).toEqual(3029075);
                expect(responseRow.impressions.total).toEqual(3822202);
                expect(responseRow.performanceByKpiId[firstKpiId].met).toEqual(0.414687685579);
                expect(responseRow.performanceByKpiId[firstKpiId].failed).toEqual(0.320989080825);
                expect(responseRow.performanceByKpiId[firstKpiId].percentLift).toEqual(29.190589447210364);
                expect(responseRow.performanceByKpiId[firstKpiId].isStatisticallySignificant).toEqual(true);
                expect(responseRow.performanceByKpiId[secondKpiId].met).toEqual(14.169105326133);
                expect(responseRow.performanceByKpiId[secondKpiId].failed).toEqual(51.662042042538);
                expect(responseRow.performanceByKpiId[secondKpiId].percentLift).toEqual(-72.5734702579772);
                expect(responseRow.performanceByKpiId[secondKpiId].isStatisticallySignificant).toEqual(true);
                expect(responseRow.performanceByKpiId[lastKpiId].met).toEqual(null);
                expect(responseRow.performanceByKpiId[lastKpiId].failed).toEqual(null);
                expect(responseRow.performanceByKpiId[lastKpiId].percentLift).toEqual(null);
                expect(responseRow.performanceByKpiId[lastKpiId].isStatisticallySignificant).toEqual(false);
            });
        },
    },
    {
        case: 'Creative Impressions filter: success 201 response, actual values',
        type: TEST_TYPES.POSITIVE,
        payload: {
            ...getReport[env],
            kpiIds: multipleKpiIdsPerEnv[env],
            advancedFilters: {
                creativeImpressions: {
                    greaterThanOrEqualTo: 50000
                },
            },
        },
        verify: async (response: APIResponse) => {
            const responseBody: SuccessResponseBody = await response.json();
            expect(response.status()).toBe(API_RESPONSE_STATUS.CREATED);
            assertCriteriaPerformanceResultShape(responseBody.result, getReport[env].filters);
            const firstKpiId = multipleKpiIdsPerEnv[env][0];
            const secondKpiId = multipleKpiIdsPerEnv[env][1];
            const lastKpiId = multipleKpiIdsPerEnv[env][multipleKpiIdsPerEnv[env].length - 1];

            responseBody.result.forEach((responseRow) => {
                expect(responseRow).toBeDefined();
                expect(responseRow.mediaCount.met).toEqual(2);
                expect(responseRow.mediaCount.failed).toEqual(2);
                expect(responseRow.mediaCount.percentageMet).toEqual( 50);
                expect(responseRow.impressions.met).toEqual( 117957);
                expect(responseRow.impressions.failed).toEqual(432024);
                expect(responseRow.impressions.total).toEqual( 549981);
                expect(responseRow.performanceByKpiId[firstKpiId].met).toEqual( 0.460337241537);
                expect(responseRow.performanceByKpiId[firstKpiId].failed).toEqual(0.269892413384);
                expect(responseRow.performanceByKpiId[firstKpiId].percentLift).toEqual(70.56323879768979);
                expect(responseRow.performanceByKpiId[firstKpiId].isStatisticallySignificant).toEqual(true);
                expect(responseRow.performanceByKpiId[secondKpiId].met).toEqual(20.375221479014);
                expect(responseRow.performanceByKpiId[secondKpiId].failed).toEqual(81.028368794326);
                expect(responseRow.performanceByKpiId[secondKpiId].percentLift).toEqual( -74.85421244165443);
                expect(responseRow.performanceByKpiId[secondKpiId].isStatisticallySignificant).toEqual(true);
                expect(responseRow.performanceByKpiId[lastKpiId].met).toEqual(null);
                expect(responseRow.performanceByKpiId[lastKpiId].failed).toEqual(null);
                expect(responseRow.performanceByKpiId[lastKpiId].percentLift).toEqual(null);
                expect(responseRow.performanceByKpiId[lastKpiId].isStatisticallySignificant).toEqual(false);
            });
        },
    },
    {
        case: 'Media Type[IMAGE]: success 201 response, no data',
        type: TEST_TYPES.POSITIVE,
        payload: {
            ...getReport[env],
            kpiIds: multipleKpiIdsPerEnv[env],
            filters: {
                ...getReport[env].filters,
                creativeMediaType: [
                    "IMAGE"
                ]
            }
        },
        verify: async (response: APIResponse) => {
            const responseBody: SuccessResponseBody = await response.json();
            expect(response.status()).toBe(API_RESPONSE_STATUS.CREATED);
            // The criteria is in these tests is sound on which is video onl
            expect(responseBody.result).toEqual([]);
        },
    },
    {
        case: 'Media Type[VIDEO]: success 201 response, actual values',
        type: TEST_TYPES.POSITIVE,
        payload: {
            ...getReport[env],
            kpiIds: multipleKpiIdsPerEnv[env],
            filters: {
                ...getReport[env].filters,
                creativeMediaType: [
                    "VIDEO"
                ]
            }
        },
        verify: async (response: APIResponse) => {
            const responseBody: SuccessResponseBody = await response.json();
            expect(response.status()).toBe(API_RESPONSE_STATUS.CREATED);
            assertCriteriaPerformanceResultShape(responseBody.result, getReport[env].filters);
            const firstKpiId = multipleKpiIdsPerEnv[env][0];
            const secondKpiId = multipleKpiIdsPerEnv[env][1];
            const lastKpiId = multipleKpiIdsPerEnv[env][multipleKpiIdsPerEnv[env].length - 1];

            responseBody.result.forEach((responseRow) => {
                expect(responseRow).toBeDefined();
                expect(responseRow.mediaCount.met).toEqual(14);
                expect(responseRow.mediaCount.failed).toEqual(7);
                expect(responseRow.mediaCount.percentageMet).toEqual(  66.66666666666666);
                expect(responseRow.impressions.met).toEqual(  289661);
                expect(responseRow.impressions.failed).toEqual(432024);
                expect(responseRow.impressions.total).toEqual( 721685);
                expect(responseRow.performanceByKpiId[firstKpiId].met).toEqual(  0.432229399194);
                expect(responseRow.performanceByKpiId[firstKpiId].failed).toEqual(0.269892413384);
                expect(responseRow.performanceByKpiId[firstKpiId].percentLift).toEqual(60.14877697915454);
                expect(responseRow.performanceByKpiId[firstKpiId].isStatisticallySignificant).toEqual(true);
                expect(responseRow.performanceByKpiId[secondKpiId].met).toEqual(15.228491236307);
                expect(responseRow.performanceByKpiId[secondKpiId].failed).toEqual(  81.028368794326);
                expect(responseRow.performanceByKpiId[secondKpiId].percentLift).toEqual( -81.20597580464515);
                expect(responseRow.performanceByKpiId[secondKpiId].isStatisticallySignificant).toEqual(true);
                expect(responseRow.performanceByKpiId[lastKpiId].met).toEqual(null);
                expect(responseRow.performanceByKpiId[lastKpiId].failed).toEqual(null);
                expect(responseRow.performanceByKpiId[lastKpiId].percentLift).toEqual(null);
                expect(responseRow.performanceByKpiId[lastKpiId].isStatisticallySignificant).toEqual(false);
            });
        },
    }
];

testScenarios.forEach((scenario) => {
    test(`API test POST Criteria Performance report: LinkedIn - ${scenario.case}, 
        type: ${scenario.type}, 
        tags: @apiRegressionTest @apitest @criteriaPerformance @linkedin`,
        async ({ request }) => {
            const { getURL, getLoginToken } = createPageInstances();
            const accessToken = await getLoginToken.getAccessToken(request);
            const baseURL = `${getURL.returnURL("bff-" + env)}/v1/criteria-group-report/organization/${organizationIdPerEnv[env]}`;

            const requestOptions = {
                headers: {
                    'Content-Type': 'application/json',
                    Authorization: `Bearer ${accessToken}`,
                },
                data: scenario.payload,
            }

            const response = await request.post(baseURL, requestOptions);
            await scenario.verify(response);
        });
});*/
