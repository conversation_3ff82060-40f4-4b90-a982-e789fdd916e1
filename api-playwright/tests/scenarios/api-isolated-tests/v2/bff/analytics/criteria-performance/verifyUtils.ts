import {CriteriaPerformanceReportRow} from "../../../../../../utils/analytics.types";
import {expect} from "@playwright/test";

export const organizationIdPerEnv = {
    dev: '27a7e882-43de-4bfa-8f53-3b62875b8432',
}

export const assertCriteriaPerformanceResultShape = (
    criteriaPerformanceReport: CriteriaPerformanceReportRow[],
    filters: { criteriaGroupIds: string[] }
): void => {
    expect(criteriaPerformanceReport).toBeDefined();
    expect(Array.isArray(criteriaPerformanceReport)).toBeTruthy();
    expect(criteriaPerformanceReport.length).toBeGreaterThan(0);
    criteriaPerformanceReport.forEach((criteriaPerformanceReportRow, index) => {
        expect(typeof criteriaPerformanceReportRow.id).toBe('string');
        expect(criteriaPerformanceReportRow.id).toEqual(filters.criteriaGroupIds[index]);
        expect(typeof criteriaPerformanceReportRow.identifier).toBe('string');
        expect(criteriaPerformanceReportRow.id).toEqual(filters.criteriaGroupIds[index]);
        const onlyResultParameters = JSON.stringify(criteriaPerformanceReportRow.parameters);
        const criteriaGroupId = `${criteriaPerformanceReportRow.identifier}:${onlyResultParameters}`;
        expect(criteriaGroupId).toContain(filters.criteriaGroupIds[index]);
        expect(typeof criteriaPerformanceReportRow.parameters).toBe('object');
        expect(typeof criteriaPerformanceReportRow.performanceByKpiId).toBe('object');
        expect(typeof criteriaPerformanceReportRow.mediaCount).toBe('object');
        expect(typeof criteriaPerformanceReportRow.impressions).toBe('object');
    });
};