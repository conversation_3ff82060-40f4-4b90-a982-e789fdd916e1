/*import {APIResponse, expect, test} from "@playwright/test";
import {createPageInstances} from "../../../../../../utils/helper";
import {API_RESPONSE_STATUS, TEST_TYPES} from "../../../../../../utils/util-constants";

// @ts-ignore
import * as getReport from '../../../../../../payload/bff/criteria-performance/getReportReddit';
import {SuccessResponseBody} from "../../../../../../utils/scoring.types";
import {assertCriteriaPerformanceResultShape, organizationIdPerEnv} from "./verifyUtils";

const env = process.env.TESTENV || 'dev';

const KPI_CLICK_THROUGH_RATE_ID_DEV = '562';
const KPI_IMPRESSIONS_ID_DEV = '557';
const KPI_ECP_SIGN_UP_ID_DEV = '585';
//
const singleKpiIdPerEnv = {
    dev: KPI_CLICK_THROUGH_RATE_ID_DEV,
};

const singleKpiIdNoDataPerEnv = {
    dev: KPI_ECP_SIGN_UP_ID_DEV,
};

const multipleKpiIdsPerEnv = {
    dev: [KPI_CLICK_THROUGH_RATE_ID_DEV, KPI_IMPRESSIONS_ID_DEV, KPI_ECP_SIGN_UP_ID_DEV],
}

const campaignIdPerEnv = {
    dev: '1552528915488439199'
}

const adIdentifierPerEnv = {
    dev: '1553817906782180073'
}

const testScenarios = [
    {
        case: 'no KPI: success 201 response, actual values',
        type: TEST_TYPES.POSITIVE,
        payload: getReport[env],
        verify: async (response: APIResponse) => {
            const responseBody: SuccessResponseBody = await response.json();
            expect(response.status()).toBe(API_RESPONSE_STATUS.CREATED);
            assertCriteriaPerformanceResultShape(responseBody.result, getReport[env].filters);
            responseBody.result.forEach((responseRow) => {
                expect(responseRow).toBeDefined();
                expect(responseRow.performanceByKpiId).toEqual({});
                expect(responseRow.mediaCount.met).toEqual(1);
                expect(responseRow.mediaCount.failed).toEqual(1);
                expect(responseRow.mediaCount.percentageMet).toEqual(50);
                expect(responseRow.impressions.met).toEqual(17146);
                expect(responseRow.impressions.failed).toEqual(17148);
                expect(responseRow.impressions.total).toEqual(34294);
            });
        }
    },
    {
        case: 'no KPI: success 201 response, no data',
        type: TEST_TYPES.POSITIVE,
        payload: {
            ...getReport[env],
            "filters": {
                "criteriaGroupIds": ['unknown']
            },
        },
        verify: async (response: APIResponse) => {
            const responseBody: SuccessResponseBody = await response.json();
            expect(response.status()).toBe(API_RESPONSE_STATUS.CREATED);
            expect(responseBody.result).toEqual([]);
        }
    },
    {
        case: '1 KPI: success 201 response, actual values',
        type: TEST_TYPES.POSITIVE,
        payload: {
            ...getReport[env],
            kpiIds: [singleKpiIdPerEnv[env]],
        },
        verify: async (response: APIResponse) => {
            const responseBody: SuccessResponseBody = await response.json();
            expect(response.status()).toBe(API_RESPONSE_STATUS.CREATED);
            assertCriteriaPerformanceResultShape(responseBody.result, getReport[env].filters );

            responseBody.result.forEach((responseRow) => {
                const kpiId = singleKpiIdPerEnv[env];
                expect(responseRow).toBeDefined();
                expect(Object.keys(responseRow.performanceByKpiId[kpiId])).toHaveLength(4);
                expect(responseRow.performanceByKpiId[kpiId].met).toEqual(0.075819433104);
                expect(responseRow.performanceByKpiId[kpiId].failed).toEqual(0.110800093305);
                expect(responseRow.performanceByKpiId[kpiId].percentLift).toEqual( -31.570966375189375);
                expect(responseRow.performanceByKpiId[kpiId].isStatisticallySignificant).toEqual(false);
            });
        },
    },
    {
        case: '1 KPI: success 201 response, no data',
        type: TEST_TYPES.POSITIVE,
        payload: {
            ...getReport[env],
            kpiIds: [singleKpiIdNoDataPerEnv[env]],
        },
        verify: async (response: APIResponse) => {
            const responseBody: SuccessResponseBody = await response.json();
            expect(response.status()).toBe(API_RESPONSE_STATUS.CREATED);
            assertCriteriaPerformanceResultShape(responseBody.result, getReport[env].filters);

            responseBody.result.forEach((responseRow) => {
                const kpiId = singleKpiIdNoDataPerEnv[env];
                expect(responseRow).toBeDefined();
                expect(Object.keys(responseRow.performanceByKpiId[kpiId])).toHaveLength(4);
                expect(responseRow.performanceByKpiId[kpiId].met).toEqual(null);
                expect(responseRow.performanceByKpiId[kpiId].failed).toEqual(null);
                expect(responseRow.performanceByKpiId[kpiId].percentLift).toEqual(null);
                expect(responseRow.performanceByKpiId[kpiId].isStatisticallySignificant).toEqual(false);
            });
        },
    },
    {
        case: 'multiple KPIs: success 201 response',
        type: TEST_TYPES.POSITIVE,
        payload: {
            ...getReport[env],
            kpiIds: multipleKpiIdsPerEnv[env],
        },
        verify: async (response: APIResponse) => {
            const responseBody: SuccessResponseBody = await response.json();
            expect(response.status()).toBe(API_RESPONSE_STATUS.CREATED);
            assertCriteriaPerformanceResultShape(responseBody.result, getReport[env].filters);

            responseBody.result.forEach((responseRow) => {
                expect(responseRow).toBeDefined();
                expect(Object.keys(responseRow.performanceByKpiId)).toHaveLength(3);
            });
        },
    },
    {
        case: 'Campaign Identifier filter: success 201 response, actual values',
        type: TEST_TYPES.POSITIVE,
        payload: {
            ...getReport[env],
            kpiIds: [
                singleKpiIdPerEnv[env],
                singleKpiIdNoDataPerEnv[env],
            ],
            advancedFilters: {
                campaignIdentifiers: [campaignIdPerEnv[env]],
            },
        },
        verify: async (response: APIResponse) => {
            const responseBody: SuccessResponseBody = await response.json();
            expect(response.status()).toBe(API_RESPONSE_STATUS.CREATED);
            assertCriteriaPerformanceResultShape(responseBody.result, getReport[env].filters);
            const kpiId = singleKpiIdPerEnv[env];
            const kpiNoData = singleKpiIdNoDataPerEnv[env];
            responseBody.result.forEach((responseRow) => {
                expect(responseRow).toBeDefined();
                expect(responseRow.mediaCount.met).toEqual(1);
                expect(responseRow.mediaCount.failed).toEqual(0);
                expect(responseRow.mediaCount.percentageMet).toEqual(100);
                expect(responseRow.impressions.met).toEqual(17146);
                expect(responseRow.impressions.failed).toEqual(0);
                expect(responseRow.impressions.total).toEqual(17146);
                expect(responseRow.performanceByKpiId[kpiId].met).toEqual(0.075819433104);
                expect(responseRow.performanceByKpiId[kpiId].failed).toEqual(null);
                expect(responseRow.performanceByKpiId[kpiId].percentLift).toEqual(null);
                expect(responseRow.performanceByKpiId[kpiId].isStatisticallySignificant).toEqual(true);
                expect(responseRow.performanceByKpiId[kpiNoData].met).toEqual(null);
                expect(responseRow.performanceByKpiId[kpiNoData].failed).toEqual(null);
                expect(responseRow.performanceByKpiId[kpiNoData].percentLift).toEqual(null);
                expect(responseRow.performanceByKpiId[kpiNoData].isStatisticallySignificant).toEqual(false);
            });
        },
    },
    {
        case: 'Ad Impression filter: success 201 response, actual values',
        type: TEST_TYPES.POSITIVE,
        payload: {
            ...getReport[env],
            kpiIds: [
                singleKpiIdPerEnv[env],
                singleKpiIdNoDataPerEnv[env],
            ],
            advancedFilters: {
                adImpressions: {greaterThanOrEqualTo: 17147},
            },
        },
        verify: async (response: APIResponse) => {
            const responseBody: SuccessResponseBody = await response.json();
            expect(response.status()).toBe(API_RESPONSE_STATUS.CREATED);
            assertCriteriaPerformanceResultShape(responseBody.result, getReport[env].filters);
            const kpiId = singleKpiIdPerEnv[env];
            const kpiNoData = singleKpiIdNoDataPerEnv[env];
            responseBody.result.forEach((responseRow) => {
                expect(responseRow).toBeDefined();
                expect(responseRow.mediaCount.met).toEqual(0);
                expect(responseRow.mediaCount.failed).toEqual(1);
                expect(responseRow.mediaCount.percentageMet).toEqual(0);
                expect(responseRow.impressions.met).toEqual(0);
                expect(responseRow.impressions.failed).toEqual(17148);
                expect(responseRow.impressions.total).toEqual( 17148);
                expect(responseRow.performanceByKpiId[kpiId].met).toEqual(null);
                expect(responseRow.performanceByKpiId[kpiId].failed).toEqual( 0.110800093305);
                expect(responseRow.performanceByKpiId[kpiId].percentLift).toEqual(null);
                expect(responseRow.performanceByKpiId[kpiId].isStatisticallySignificant).toEqual(true);
                expect(responseRow.performanceByKpiId[kpiNoData].met).toEqual(null);
                expect(responseRow.performanceByKpiId[kpiNoData].failed).toEqual(null);
                expect(responseRow.performanceByKpiId[kpiNoData].percentLift).toEqual(null);
                expect(responseRow.performanceByKpiId[kpiNoData].isStatisticallySignificant).toEqual(false);
            });
        },
    },
    {
        case: 'Ad Identifier filter: success 201 response, actual values',
        type: TEST_TYPES.POSITIVE,
        payload: {
            ...getReport[env],
            kpiIds: [
                singleKpiIdPerEnv[env],
                singleKpiIdNoDataPerEnv[env],
            ],
            advancedFilters: {
                adIdentifiers: [adIdentifierPerEnv[env]],
            },
        },
        verify: async (response: APIResponse) => {
            const responseBody: SuccessResponseBody = await response.json();
            expect(response.status()).toBe(API_RESPONSE_STATUS.CREATED);
            assertCriteriaPerformanceResultShape(responseBody.result, getReport[env].filters);
            const kpiId = singleKpiIdPerEnv[env];
            const kpiNoData = singleKpiIdNoDataPerEnv[env];
            responseBody.result.forEach((responseRow) => {
                expect(responseRow).toBeDefined();
                expect(responseRow.mediaCount.met).toEqual(0);
                expect(responseRow.mediaCount.failed).toEqual(1);
                expect(responseRow.mediaCount.percentageMet).toEqual(0);
                expect(responseRow.impressions.met).toEqual(0);
                expect(responseRow.impressions.failed).toEqual(17148);
                expect(responseRow.impressions.total).toEqual(17148);
                expect(responseRow.performanceByKpiId[kpiId].met).toEqual(null);
                expect(responseRow.performanceByKpiId[kpiId].failed).toEqual(0.110800093305);
                expect(responseRow.performanceByKpiId[kpiId].percentLift).toEqual(null);
                expect(responseRow.performanceByKpiId[kpiId].isStatisticallySignificant).toEqual(true);
                expect(responseRow.performanceByKpiId[kpiNoData].met).toEqual(null);
                expect(responseRow.performanceByKpiId[kpiNoData].failed).toEqual(null);
                expect(responseRow.performanceByKpiId[kpiNoData].percentLift).toEqual(null);
                expect(responseRow.performanceByKpiId[kpiNoData].isStatisticallySignificant).toEqual(false);
            });
        },
    },
    {
        case: 'Created By Vidmob[true]: success 201 response, actual values',
        type: TEST_TYPES.POSITIVE,
        payload: {
            ...getReport[env],
            kpiIds: [
                singleKpiIdPerEnv[env],
                singleKpiIdNoDataPerEnv[env],
            ],
            advancedFilters: {
                createdByVidmob: true,
            },
        },
        verify: async (response: APIResponse) => {
            const responseBody: SuccessResponseBody = await response.json();
            expect(response.status()).toBe(API_RESPONSE_STATUS.CREATED);
            // This test is a little weird, both records in dev are not created by vidmob
            // so they both get filtered out
            expect(responseBody.result).toHaveLength(0)
        },
    },
    {
        case: 'Created By Vidmob[false]: success 201 response, actual values',
        type: TEST_TYPES.POSITIVE,
        payload: {
            ...getReport[env],
            kpiIds: [
                singleKpiIdPerEnv[env],
                singleKpiIdNoDataPerEnv[env],
            ],
            advancedFilters: {
                createdByVidmob: false,
            },
        },
        verify: async (response: APIResponse) => {
            const responseBody: SuccessResponseBody = await response.json();
            expect(response.status()).toBe(API_RESPONSE_STATUS.CREATED);
            assertCriteriaPerformanceResultShape(responseBody.result, getReport[env].filters);
            const kpiId = singleKpiIdPerEnv[env];
            const kpiNoData = singleKpiIdNoDataPerEnv[env];
            responseBody.result.forEach((responseRow) => {
                // This test is a little weird, both records in dev are not created by vidmob
                // so the results here should be the same as if no filter was applied
                expect(responseRow).toBeDefined();
                expect(responseRow.mediaCount.met).toEqual(1);
                expect(responseRow.mediaCount.failed).toEqual(1);
                expect(responseRow.mediaCount.percentageMet).toEqual(50);
                expect(responseRow.impressions.met).toEqual(17146);
                expect(responseRow.impressions.failed).toEqual(17148);
                expect(responseRow.impressions.total).toEqual( 34294);
                expect(responseRow.performanceByKpiId[kpiId].met).toEqual(0.075819433104);
                expect(responseRow.performanceByKpiId[kpiId].failed).toEqual( 0.110800093305);
                expect(responseRow.performanceByKpiId[kpiId].percentLift).toEqual(-31.570966375189375);
                expect(responseRow.performanceByKpiId[kpiId].isStatisticallySignificant).toEqual(false);
                expect(responseRow.performanceByKpiId[kpiNoData].met).toEqual(null);
                expect(responseRow.performanceByKpiId[kpiNoData].failed).toEqual(null);
                expect(responseRow.performanceByKpiId[kpiNoData].percentLift).toEqual(null);
                expect(responseRow.performanceByKpiId[kpiNoData].isStatisticallySignificant).toEqual(false);
            });
        },
    },
    {
        case: 'Creative Impressions: success 201 response, actual values',
        type: TEST_TYPES.POSITIVE,
        payload: {
            ...getReport[env],
            kpiIds: [
                singleKpiIdPerEnv[env],
                singleKpiIdNoDataPerEnv[env],
            ],
            advancedFilters: {
                creativeImpressions: {
                    greaterThanOrEqualTo: 17147
                },
            },
        },
        verify: async (response: APIResponse) => {
            const responseBody: SuccessResponseBody = await response.json();
            expect(response.status()).toBe(API_RESPONSE_STATUS.CREATED);
            assertCriteriaPerformanceResultShape(responseBody.result, getReport[env].filters);
            const kpiId = singleKpiIdPerEnv[env];
            const kpiNoData = singleKpiIdNoDataPerEnv[env];
            responseBody.result.forEach((responseRow) => {
                expect(responseRow).toBeDefined();
                expect(responseRow.mediaCount.met).toEqual(0);
                expect(responseRow.mediaCount.failed).toEqual(1);
                expect(responseRow.mediaCount.percentageMet).toEqual(0);
                expect(responseRow.impressions.met).toEqual(0);
                expect(responseRow.impressions.failed).toEqual(17148);
                expect(responseRow.impressions.total).toEqual( 17148);
                expect(responseRow.performanceByKpiId[kpiId].met).toEqual( null);
                expect(responseRow.performanceByKpiId[kpiId].failed).toEqual( 0.110800093305);
                expect(responseRow.performanceByKpiId[kpiId].percentLift).toEqual(null);
                expect(responseRow.performanceByKpiId[kpiId].isStatisticallySignificant).toEqual(true);
                expect(responseRow.performanceByKpiId[kpiNoData].met).toEqual(null);
                expect(responseRow.performanceByKpiId[kpiNoData].failed).toEqual(null);
                expect(responseRow.performanceByKpiId[kpiNoData].percentLift).toEqual(null);
                expect(responseRow.performanceByKpiId[kpiNoData].isStatisticallySignificant).toEqual(false);
            });
        }
    },
    {
        case: 'Media Type[IMAGE]: success 201 response, actual values',
        type: TEST_TYPES.POSITIVE,
        payload: {
            ...getReport[env],
            kpiIds: [
                singleKpiIdPerEnv[env],
                singleKpiIdNoDataPerEnv[env],
            ],
            filters: {
                ...getReport[env].filters,
                creativeMediaType: [
                    "IMAGE"
                ]
            }
        },
        verify: async (response: APIResponse) => {
            const responseBody: SuccessResponseBody = await response.json();
            expect(response.status()).toBe(API_RESPONSE_STATUS.CREATED);
            assertCriteriaPerformanceResultShape(responseBody.result, getReport[env].filters);
            const singleKpi = singleKpiIdPerEnv[env];
            const singleKpiNoData = singleKpiIdNoDataPerEnv[env];

            responseBody.result.forEach((responseRow) => {
                expect(responseRow).toBeDefined();
                expect(responseRow.mediaCount.met).toEqual(0);
                expect(responseRow.mediaCount.failed).toEqual(1);
                expect(responseRow.mediaCount.percentageMet).toEqual( 0);
                expect(responseRow.impressions.met).toEqual(  0);
                expect(responseRow.impressions.failed).toEqual(17148);
                expect(responseRow.impressions.total).toEqual( 17148);
                expect(responseRow.performanceByKpiId[singleKpi].met).toEqual( null);
                expect(responseRow.performanceByKpiId[singleKpi].failed).toEqual(0.110800093305);
                expect(responseRow.performanceByKpiId[singleKpi].percentLift).toEqual(null);
                expect(responseRow.performanceByKpiId[singleKpi].isStatisticallySignificant).toEqual(true);
                expect(responseRow.performanceByKpiId[singleKpiNoData].met).toEqual(null);
                expect(responseRow.performanceByKpiId[singleKpiNoData].failed).toEqual(null);
                expect(responseRow.performanceByKpiId[singleKpiNoData].percentLift).toEqual(null);
                expect(responseRow.performanceByKpiId[singleKpiNoData].isStatisticallySignificant).toEqual(false);
            });
        }
        },
    {
        case: 'Media Type[VIDEO]: success 201 response, actual values',
        type: TEST_TYPES.POSITIVE,
        payload: {
            ...getReport[env],
            kpiIds: [
                singleKpiIdPerEnv[env],
                singleKpiIdNoDataPerEnv[env],
            ],
            filters: {
                ...getReport[env].filters,
                creativeMediaType: [
                    "VIDEO"
                ]
            }
        },
        verify: async (response: APIResponse) => {
            const responseBody: SuccessResponseBody = await response.json();
            expect(response.status()).toBe(API_RESPONSE_STATUS.CREATED);
            assertCriteriaPerformanceResultShape(responseBody.result, getReport[env].filters);
            const singleKpi = singleKpiIdPerEnv[env];
            const singleKpiNoData = singleKpiIdNoDataPerEnv[env];


            responseBody.result.forEach((responseRow) => {
                expect(responseRow).toBeDefined();
                expect(responseRow.mediaCount.met).toEqual(1);
                expect(responseRow.mediaCount.failed).toEqual(0);
                expect(responseRow.mediaCount.percentageMet).toEqual(100);
                expect(responseRow.impressions.met).toEqual( 17146);
                expect(responseRow.impressions.failed).toEqual(0);
                expect(responseRow.impressions.total).toEqual( 17146);
                expect(responseRow.performanceByKpiId[singleKpi].met).toEqual(0.075819433104);
                expect(responseRow.performanceByKpiId[singleKpi].failed).toEqual(null);
                expect(responseRow.performanceByKpiId[singleKpi].percentLift).toEqual(null);
                expect(responseRow.performanceByKpiId[singleKpi].isStatisticallySignificant).toEqual(true);
                expect(responseRow.performanceByKpiId[singleKpiNoData].met).toEqual(null);
                expect(responseRow.performanceByKpiId[singleKpiNoData].failed).toEqual(null);
                expect(responseRow.performanceByKpiId[singleKpiNoData].percentLift).toEqual(null);
                expect(responseRow.performanceByKpiId[singleKpiNoData].isStatisticallySignificant).toEqual(false);
            });
        },
    }
];

testScenarios.forEach((scenario) => {
    test(`API test POST Criteria Performance report: Reddit - ${scenario.case}, 
        type: ${scenario.type}, 
        tags: @apiRegressionTest @apitest @criteriaPerformance @reddit`,
        async ({ request }) => {
            const { getURL, getLoginToken } = createPageInstances();
            const accessToken = await getLoginToken.getAccessToken(request);
            const baseURL = `${getURL.returnURL("bff-" + env)}/v1/criteria-group-report/organization/${organizationIdPerEnv[env]}`;

            const requestOptions = {
                headers: {
                    'Content-Type': 'application/json',
                    Authorization: `Bearer ${accessToken}`,
                },
                data: scenario.payload,
            }

            const response = await request.post(baseURL, requestOptions);
            await scenario.verify(response);

        });
});*/