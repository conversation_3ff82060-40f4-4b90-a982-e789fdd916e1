import { test, expect } from "@playwright/test";
import { createPageInstances } from "../../../../../utils/helper";
// @ts-ignore
import * as payload from "../../../../../payload/bff/getAnalyticsNormativePerformancePayload.json";

const env = process.env.TESTENV || "";

test("API test - Verify the success of analytics normative performance request @apiRegressionTest @apitest @analyticsNormativePerformance", async ({
  request,
}) => {
  const { getURL, getLoginToken } = createPageInstances();
  const accessToken = await getLoginToken.getAccessToken(request);

  const baseURL: string =
    getURL.returnURL("bff-" + env) + "/v1/normative-performance?perPage=1000";
  const requestOptions = {
    data: payload[env],
    headers: {
      "Content-Type": "application/json",
      Authorization: "Bearer " + accessToken,
    },
  };

  const response = await request.post(baseURL, requestOptions);
  const responseBody = await response.json();

  expect(response.status()).toBe(201);
  expect(responseBody.status).toBe("OK");
  expect(responseBody.result.length).toBe(payload[env].kpiIds.length);
  expect(responseBody.pagination.offset).toBe(0);
  expect(responseBody.pagination.perPage).toBe(1000);
  expect(responseBody.pagination.totalSize).toBe(payload[env].kpiIds.length);
});

test("API test - negative - Including invalid KPI id in payload should fail @apiRegressionTest @apitest @analyticsNormativePerformance", async ({
  request,
}) => {
  const { getURL, getLoginToken } = createPageInstances();
  const accessToken = await getLoginToken.getAccessToken(request);

  const baseURL: string =
    getURL.returnURL("bff-" + env) + "/v1/normative-performance?perPage=1000";
  const payloadWithIncorrectKpiId = {
    ...payload[env],
    kpiIds: ["invalidKpiId", ...payload[env].kpiIds],
  };
  const requestOptions = {
    data: payloadWithIncorrectKpiId,
    headers: {
      "Content-Type": "application/json",
      Authorization: "Bearer " + accessToken,
    },
  };

  const response = await request.post(baseURL, requestOptions);
  const responseBody = await response.json();

  expect(response.status()).toBe(500);
  expect(responseBody.status).toBe("ERROR");
  expect(responseBody.result).toBeUndefined();
  expect(responseBody.pagination).toBeUndefined();
  expect(responseBody.error.message).toBe("KPI with id invalidKpiId not found");
  expect(responseBody.error.identifier).toBe("vidmob.acs-bff-api.error");
});

test("API test - negative - invalid platform in payload should fail @apiRegressionTest @apitest @analyticsNormativePerformance", async ({
  request,
}) => {
  const { getURL, getLoginToken } = createPageInstances();
  const accessToken = await getLoginToken.getAccessToken(request);

  const baseURL: string =
    getURL.returnURL("bff-" + env) + "/v1/normative-performance?perPage=1000";
  const payloadWithInvalidPlatform = {
    ...payload[env],
    platform: "invalidPlatform",
  };
  const requestOptions = {
    data: payloadWithInvalidPlatform,
    headers: {
      "Content-Type": "application/json",
      Authorization: "Bearer " + accessToken,
    },
  };

  const response = await request.post(baseURL, requestOptions);
  const responseBody = await response.json();

  expect(response.status()).toBe(400);
  expect(responseBody.status).toBe("ERROR");
  expect(responseBody.result).toBeUndefined();
  expect(responseBody.pagination).toBeUndefined();
  expect(responseBody.error.message).toStrictEqual([
    "platform must be one of the following values: facebook, snapchat, tiktok, pinterest, dv360, linkedin, twitter, reddit, adwords, amazonadvertising, amazonadvertisingdsp, instagrampage, facebookpage",
  ]);
  expect(responseBody.error.identifier).toBe(
    "vidmob.acs-bff-api.badrequestexception"
  );
  expect(responseBody.error.type).toBe("BADREQUESTEXCEPTION");
});

test("API test - negative - platform does not support objectives  @apiRegressionTest @apitest @analyticsNormativePerformance", async ({
  request,
}) => {
  const { getURL, getLoginToken } = createPageInstances();
  const accessToken = await getLoginToken.getAccessToken(request);

  const baseURL: string =
    getURL.returnURL("bff-" + env) + "/v1/normative-performance?perPage=1000";
  const payloadWithInvalidPlatform = {
    ...payload[env],
    platform: "ADWORDS",
    objectiveGroupIds: [1, 2, 3],
  };
  const requestOptions = {
    data: payloadWithInvalidPlatform,
    headers: {
      "Content-Type": "application/json",
      Authorization: "Bearer " + accessToken,
    },
  };

  const response = await request.post(baseURL, requestOptions);
  const responseBody = await response.json();

  expect(response.status()).toBe(500);
  expect(responseBody.status).toBe("ERROR");
  expect(responseBody.result).toBeUndefined();
  expect(responseBody.pagination).toBeUndefined();
  expect(responseBody.error.message).toBe(
    "Platform does not support objectives"
  );
  expect(responseBody.error.identifier).toBe("vidmob.acs-bff-api.Error");
  expect(responseBody.error.type).toBe("ERROR");
});
