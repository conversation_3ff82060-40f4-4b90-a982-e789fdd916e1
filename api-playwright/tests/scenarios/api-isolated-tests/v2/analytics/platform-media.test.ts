/* eslint-disable @typescript-eslint/no-unused-vars */
import { test, expect } from "@playwright/test";
import { createPageInstances } from "../../../../utils/helper";
// @ts-ignore
import * as payload from "../../../../payload/analytics/platformMedia";
// @ts-ignore
import * as workspace from "../../../../feeder/workspace";

const env = process.env.TESTENV || "dev";

  test(
    "API test - Verify the success of platformMedia request @apiRegressionTest @apitest @platformMedia",
    async ({ request }) => {
      const { getURL, getLoginToken } = createPageInstances();
      const accessToken = await getLoginToken.getAccessToken(request);

      const baseURL: string =
        getURL.returnURL("anApiUrl-" + env) +
          "/api/v2/platformMedia";

      const requestOptions = {
        data: payload[env],
        headers: {
          'Content-Type': 'application/json',
          Authorization: 'Bearer '+accessToken
        },
      };
      const response = await request.post(baseURL, requestOptions)

      expect(response.status()).toBe(200);

      const responseBody = await response.json();
      expect(responseBody.status).toBe('ok');
      expect(responseBody.result.platformMedias).not.toBeNull();
      expect(responseBody.result.platformMedias.media).not.toBeNull();
    });

type getPlatformMedia = {
  workspaceIds: number;
  organizationId: string;
  testScenario: string;
  platformMediaIds: string;

};
const notFound: getPlatformMedia[] = [];
notFound.push({ workspaceIds: workspace[env].workspaceIds, organizationId:workspace[env].organizationId,  testScenario: "platformMediaIds not found - numbers", platformMediaIds:"77777398662667005103:a89cd60aa182e34200bee1592d896aa5" });
notFound.push({ workspaceIds: workspace[env].workspaceIds, organizationId:workspace[env].organizationId,  testScenario: "platformMediaIds not found - special chars", platformMediaIds:"@$#5%ˆ&*:a89cd60aa182e34200bee1592d896aa5" });

notFound.forEach((scenario) => {
  test(
    "API test - negative " +
      scenario.testScenario +
      "-  @apiRegressionTest @apitest @platformMedia",
    async ({ request }) => {
      const { getURL, getLoginToken } = createPageInstances();
      const accessToken = await getLoginToken.getAccessToken(request);
      const baseURL: string =
        getURL.returnURL("anApiUrl-" + env) +
          "/api/v2/platformMedia";
      const platformMediaIds = scenario.platformMediaIds

      const requestOptions = {
        data: {
          organizationId: scenario.organizationId,
          workspaceIds: [scenario.workspaceIds],
          platformMediaIds: [platformMediaIds]
        },
        headers: {
          'Content-Type': 'application/json',
          Authorization: 'Bearer '+accessToken
        },
      };
      const response = await request.post(baseURL, requestOptions)

      expect(response.status()).toBe(200);

      const responseBody = await response.json();
      expect(responseBody.status).toBe('ok');
      expect(responseBody.result.platformMedias).not.toBeNull();
      expect(responseBody.result.platformMedias[platformMediaIds].error).toContain('Platform media not found');

    },
  );
});

const badRequest: getPlatformMedia[] = []
badRequest.push({ workspaceIds: 123456, organizationId: workspace[env].organizationId, testScenario: "invalid workspaceIds AND correct organizationId",platformMediaIds: "398662667005103:a89cd60aa182e34200bee1592d896aa5" });
badRequest.push({ workspaceIds: workspace[env].workspaceIds, organizationId:"@$#5%ˆ&*", testScenario: "correct workspaceIds AND special chars only on organizationId" ,platformMediaIds: "398662667005103:a89cd60aa182e34200bee1592d896aa5" });
badRequest.push({ workspaceIds: workspace[env].workspaceIds,  organizationId:"nothing", testScenario: "correct workspaceIds AND string only on organizationId" ,platformMediaIds: "398662667005103:a89cd60aa182e34200bee1592d896aa5" });
badRequest.push({ workspaceIds: workspace[env].workspaceIds,  organizationId:"!@#29u832teat", testScenario: "correct workspaceIds AND misc chars on correct organizationId" ,platformMediaIds: "398662667005103:a89cd60aa182e34200bee1592d896aa5" });
badRequest.push({ workspaceIds: workspace[env].workspaceIds,  organizationId:"' '", testScenario: "correct workspaceIds AND null on organizationId" ,platformMediaIds: "398662667005103:a89cd60aa182e34200bee1592d896aa5" });

badRequest.forEach((scenario) => {
  test(
    "API test - negative - Bad Request" +
      scenario.testScenario +
      " error returned  @apiRegressionTest @apitest @platformMedia",
    async ({ request }) => {
      const { getURL, getLoginToken } = createPageInstances();
      const accessToken = await getLoginToken.getAccessToken(request);
      const baseURL: string =
        getURL.returnURL("anApiUrl-" + env) +
          "/api/v2/platformMedia";
      const platformMediaIds = scenario.platformMediaIds

      const requestOptions = {
        data: {
          organizationId: scenario.organizationId,
          workspaceIds: [scenario.workspaceIds],
          platformMediaIds: [platformMediaIds]
        },
        headers: {
          'Content-Type': 'application/json',
          Authorization: 'Bearer '+accessToken
        },
      };
      const response = await request.post(baseURL, requestOptions)

      expect(response.status()).toBe(400);

      // Parse the response body as JSON
      const responseBody = await response.json();
      expect(responseBody.status).toBe("error");
      expect(responseBody.error.message).toContain("Error getting platform media bulk");
    },
  );
});

