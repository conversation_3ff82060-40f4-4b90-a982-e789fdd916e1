export class GetURL {
  constructor() {}

  public returnURL(key: string) {
    switch (key) {
      case "soa-dev":
        return "https://soa-internal-dev.vidmob.com";
      case "soa-stage":
        return "https://soa-internal-stage.vidmob.com";
      case "soa-prod":
        return "https://soa-internal.vidmob.com";
      case "bff-dev":
        return "https://bff-dev.vidmob.com";
      case "bff-stage":
        return "https://bff-stage.vidmob.com";
      case "bff-prod":
        return "https://bff.vidmob.com";
      case "acs-login-stage":
        return "https://acs-stage.vidmob.com";
      case "acs-login-dev":
        return "https://acs-dev.vidmob.com";
      case "acs-login-prod":
        return "https://acs.vidmob.com";
      case "anApiUrl-dev":
        return "https://api-analytics-dev.vidmob.com";
      case "anApiUrl-stage":
        return "https://api-analytics-stage.vidmob.com";
      case "anApiUrl-prod":
          return "https://api-analytics.vidmob.com";
      case "basicApi-dev":
        return "https://api-dev.vidmob.com";
      case "basicApi-stage":
          return "https://api-stage.vidmob.com";
      case "basicApi-prod":
          return "https://api.vidmob.com";
      case "api-bff-dev":
        return "https://api-bff-dev.vidmob.com";
      case "api-bff-stage":
        return "https://api-bff-stage.vidmob.com";
      case "api-bff-prod":
        return "https://api-bff.vidmob.com";
      case "api-public-dev":
        return "https://api-public-dev.vidmob.com";
      case "api-public-stage":
        return "https://api-public-stage.vidmob.com";
      case "api-public-prod":
        return "https://api-public.vidmob.com";
    }
  }

  public appendQueryParams(url: string, queryParams: {key: string; value: string}[]): string {
    let urlWithQueryParams = url;
    if (queryParams && queryParams.length) {
      urlWithQueryParams += "?";
      queryParams.forEach((queryParam, index) => {
        if (index === queryParams.length - 1) {
          urlWithQueryParams += `${queryParam.key}=${queryParam.value}`;
        } else {
          urlWithQueryParams += `${queryParam.key}=${queryParam.value}&`;
        }
      });
    }
    return urlWithQueryParams;
  }
}
