import {expect} from "@playwright/test";
import {createPageInstances} from "./helper";

export class GetLoginToken {
  async getAccessToken(request) {
    const { getURL } = createPageInstances();
    const env = process.env.TESTENV || "dev";
    const baseURL: string = getURL.returnURL("acs-login-" + env) + "/api/login";

    const username: string = process.env.EMAIL || "";
    const password: string = process.env.PASSWORD || "";

    const response = await request.post(baseURL, {
      headers: {
        "Content-Type": "application/json",
      },
      data: {
        username: username,
        password: password,
        //"twoFactorAuthPassword": "140666" - prod
      },
    });

    try {
      expect(response.status()).toBe(200);
      const responseBody = await response.json();

      const access_token = responseBody.result.accessToken;
      return access_token;
    } catch (error) {
      console.error("Error on getting access_token:", error);
    }
  }

  async getAccessTokenForOrgAdmin(request) {
    const { getURL } = createPageInstances();
    const env = process.env.TESTENV || "dev";
    const baseURL: string = getURL.returnURL("acs-login-" + env) + "/api/login";

    const username: string = process.env.ORG_ADMIN_EMAIL || "";
    const password: string = process.env.ORG_ADMIN_PASSWORD || "";
    const response = await request.post(baseURL, {
      headers: {
        "Content-Type": "application/json",
      },
      data: {
        username: username,
        password: password,
        //"twoFactorAuthPassword": "140666" - prod
      },
    });

    try {
      expect(response.status()).toBe(200);
      const responseBody = await response.json();
      return responseBody.result.accessToken;
    } catch (error) {
      console.error("Error on getting access_token:", error);
    }
  }

  async getPluginAccessToken(request) {
    const { getURL } = createPageInstances();
    const env = process.env.TESTENV || "dev";
    const baseURL: string = getURL.returnURL("api-bff-" + env) + "/v1/login";

    const username: string = process.env.PLUGIN_EMAIL || "";
    const password: string = process.env.PLUGIN_PASSWORD || "";

    const response = await request.post(baseURL, {
      headers: {
        "Content-Type": "application/json",
      },
      data: {
        username: username,
        password: password,
      },
    });

    try {
      expect(response.status()).toBe(200);
      const responseBody = await response.json();

      const access_token = responseBody.accessToken;
      return access_token;
    } catch (error) {
      console.error("Error on getting access_token:", error);
    }
  }

  async getPluginRefreshToken(request) {
    const { getURL } = createPageInstances();
    const env = process.env.TESTENV || "dev";
    const baseURL: string = getURL.returnURL("api-bff-" + env) + "/v1/login";

    const username: string = process.env.PLUGIN_EMAIL || "";
    const password: string = process.env.PLUGIN_PASSWORD || "";

    const response = await request.post(baseURL, {
      headers: {
        "Content-Type": "application/json",
      },
      data: {
        username: username,
        password: password,
      },
    });

    try {
      expect(response.status()).toBe(200);
      const responseBody = await response.json();

      const refresh_token = responseBody.refreshToken;
      return refresh_token;
    } catch (error) {
      console.error("Error on getting refresh_token:", error);
    }
  }
}
