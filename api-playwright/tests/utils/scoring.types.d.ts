/* eslint-disable @typescript-eslint/no-explicit-any */
export type Pagination = {
  offset: number;
  perPage: number;
  nextOffset: number;
  totalSize: number;
};

export type SuccessResponseBody = {
  status: string;
  result: any;
  pagination?: Pagination;
};

export type ErrorResponseBody = {
  status: string;
  traceId: string;
  error: {
    identifier: string;
    code: number;
    type: string;
    system: string;
    message: string;
  }
};

export type ForbiddenResponseBody = {
  statusCode: number;
  message: string;
  error: string;
};

export interface CriteriaResult {
  name: string;
  identifier: string;
  dateCreated: string;
  id: number;
  criteriaSetId: number;
  criteriaSet: {
    id: number;
    isGlobal: boolean;
  };
  parameters: object;
  mediaTypes: string[];
  platform: string;
  isBestPractice: boolean;
  isOptional: boolean;
  owner: {
    id: number;
    firstName: string;
    lastName: string;
    username: string;
    photo: string;
  };
  custom: null | object;
  rule: string;
  description: string;
  defaultDisplayName: string;
}

export interface IndividualScoreResult {
  id: number;
  identifier: string;
  parameters: string;
  name: string;
  platformIdentifier: string;
  result: string;
  isOptional: boolean;
  custom?: {
    customValues: string[];
  }
}
