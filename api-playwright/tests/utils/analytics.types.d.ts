export interface CriteriaPerformanceReportRow {
    id: string;
    identifier: string;
    parameters: Record<string, any>;
    performanceByKpiId: {
        [key: string]: {
            met: number | null;
            failed: number | null;
            percentLift: number | null;
            isStatisticallySignificant: boolean;
        }
    }
    mediaCount: {
        met: number | null;
        failed: number | null;
        percentageMet: number | null;
    }
    impressions: {
        met: number | null;
        failed: number | null;
        total: number | null;
    }
}
