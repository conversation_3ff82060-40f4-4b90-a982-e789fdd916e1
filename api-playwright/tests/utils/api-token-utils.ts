import { APIRequestContext, APIResponse, expect } from '@playwright/test';
import * as Api<PERSON><PERSON> from './../feeder/apiKey.json';
import { ApiKeyInfo, PermissionScope } from './api-key.types';
import { createPageInstances } from './helper';

export const scopes: { [key: string]: PermissionScope[] } = {
  'all-rw': [
    {
      scope: 'scoring',
      permission: 'read_write',
    },
    {
      scope: 'platform',
      permission: 'read_write',
    },
    {
      scope: 'analytics',
      permission: 'read_write',
    },
    {
      scope: 'studio',
      permission: 'read_write',
    }
  ],
  'platform-rw': [
    {
      scope: 'platform',
      permission: 'read_write',
    }
  ],
  'scoring-rw': [
    {
      scope: 'scoring',
      permission: 'read_write',
    },
  ],
  'scoring-ro': [
    {
      scope: 'scoring',
      permission: 'read',
    },
  ],
}

export const deleteApiKey = async (request: APIRequestContext, apiKeyId: string) => {
  const {getURL} = createPageInstances();
  const env: string = process.env.TESTENV || "dev";
  const baseUrl: string = getURL.returnURL(`soa-${env}`);
  const userId: number = ApiKey[env].userId
  const orgId: string = ApiKey[env].organizationId
  const fullUrl: string = `${baseUrl}/v1/api-key/user/${userId}/organization/${orgId}/${apiKeyId}`

  const response: APIResponse = await request.delete(fullUrl, {
    headers: {
      "Content-Type": "application/json",
      "vidmob-nestjs-service": "vidmob-authorization-service"
    }
  })

  expect(response.status()).toBe(200);
}

export const getApiKeyInfo = async (request: APIRequestContext, scopes: PermissionScope[]): Promise<ApiKeyInfo> => {
  const {getURL} = createPageInstances();
  const env: string = process.env.TESTENV || "dev";
  const baseUrl: string = getURL.returnURL(`soa-${env}`);
  const userId: number = ApiKey[env].userId
  const orgId: string = ApiKey[env].organizationId
  const fullUrl: string = `${baseUrl}/v1/api-key/user/${userId}/organization/${orgId}`;

  const body = {
    name: `Vidmob ${env} Test API Key`,
    scopes: scopes,
  }

  const response: APIResponse = await request.post(fullUrl, {
    headers: {
      "Content-Type": "application/json",
      "vidmob-nestjs-service": "vidmob-authorization-service"
    },
    data: body
  })

  const responseBody = await response.json();

  return {
    id: responseBody['result']['id'],
    apiKey: responseBody['result']['apiKey'],
    organizationId: responseBody['result']['organizationId'],
  };
}
