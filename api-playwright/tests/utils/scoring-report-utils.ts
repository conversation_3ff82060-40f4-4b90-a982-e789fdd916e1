export const getPayloadWithoutAnalyticsFilters = (payload, batchType) => {
  const {analyticsFilters, ...payloadWithoutAnalyticsFilters} = payload;
  return {
    ...payloadWithoutAnalyticsFilters,
    filters: [
      ...payloadWithoutAnalyticsFilters.filters,
      {
        "fieldName": "batchType",
        "operator": "equals",
        "value": batchType
      },
    ]
  }
}

export const getPayloadWithAnalyticsFilters = (payload, batchType) => {
  return {
    ...payload,
    filters: [
      ...payload.filters.filter(item => item.fieldName !== "batchType"),
      {
        "fieldName": "batchType",
        "operator": "equals",
        "value": batchType
      },
    ]
  }
}
