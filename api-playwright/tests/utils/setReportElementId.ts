import { expect } from "@playwright/test";
import { createPageInstances } from "./helper";
// @ts-ignore
import * as workspace from '../feeder/workspace.json'
// @ts-ignore
import * as reportElementPayload from '../payload/analytics/createReportElement.json'
export class SetReportElementId {
    async ReportElementId ( request ) {
        const { getURL, getLoginToken } = createPageInstances();
        const accessToken = await getLoginToken.getAccessToken(request);
        const env: string = process.env.TESTENV || "dev";
        const orgId = workspace[env].organizationId

        const baseURL: string =
        getURL.returnURL("bff-" + env) +
        "/v1/analytics-report/organization/"+ orgId

        const response = await request.post(baseURL, {
          headers: {
              'Content-Type': 'application/json',
               Authorization: 'Bearer '+accessToken
            },
          data: reportElementPayload[env],
        });

    try {
        expect(response.status()).toBe(201);
        const responseBody = await response.json();

        const report_element_id = responseBody.result.id;
        return report_element_id;

    } catch (error) {
        console.error('Error on getting report_element_id:', error);
    }
  }
}
