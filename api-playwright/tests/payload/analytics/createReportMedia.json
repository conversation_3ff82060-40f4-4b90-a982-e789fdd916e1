{"dev": {"description": "", "filters": {"columnSelections": {"__average": {"id": "__average", "campaigns": []}}, "adAccountIds": ["***************"], "advancedAdAccounts": {"brands": [], "markets": [], "adAccounts": []}, "dateRange": {"startDate": "2024-01-03", "endDate": "2024-04-03"}, "isDefaultKPITimeRangeFilterActive": true, "isShowAppAdsOnlyActive": false, "isShowSparkAdsOnlyActive": false, "kpiId": "", "mediaTypes": ["VIDEO"], "organizationId": "1754c0a9-c620-4d34-8243-9c8b49cbcaa6", "platform": "FACEBOOK", "selectedRows": [], "statSettings": {"average": "element", "baseComparison": "impressions", "direction": "horizontal", "minimumTagConfidenceLevel": 75, "statConfidence": 95}, "viewBy": "duration", "workspaces": [{"id": 28750, "isEnterprise": true, "publicAccountTypeName": "<EMAIL>", "accountTypeIdentifier": "ENTERPRISE", "isPersonal": false, "name": "<EMAIL>", "logoUrl": "https://vidmob-storage-dev.s3.amazonaws.com/partner/28750/logo5.png", "color": "#0161f2", "isFindMyTeamEnabled": false, "organizationId": "1754c0a9-c620-4d34-8243-9c8b49cbcaa6", "organizationName": "VidMob", "permissions": {"permissionsKeySet": {}}, "hasAdminRole": false, "hasManagerRole": false, "featureList": {"INVITE-INTERNAL-CREATORS": false, "ANALYTICS-API": true, "CREATIVE-INTELLIGENCE": true, "BRAND-GOVERNANCE": true}}], "workspaceIds": [28750], "isOnlyAssetsCreatedWithVidMobActive": false}, "filtersVersion": 1, "groupBy": {"columns": "duration", "rows": "kpi"}, "name": "TEST 1303", "reportType": "MEDIA_IMPACT", "sortBy": {"sortBy": "account_average", "sortOrder": "ASC"}}, "stage": {"description": "", "filters": {"columnSelections": {"__average": {"id": "__average", "campaigns": []}}, "adAccountIds": ["***************", "****************"], "dateRange": {"startDate": "2023-12-12", "endDate": "2024-03-12"}, "isDefaultKPITimeRangeFilterActive": true, "isShowAppAdsOnlyActive": false, "isShowSparkAdsOnlyActive": false, "kpiId": "101", "mediaTypes": ["VIDEO"], "organizationId": "cb90fd8c-4740-43c6-9def-98d9f8eb5a0b", "platform": "FACEBOOK", "selectedRows": [], "statSettings": {"average": "element", "baseComparison": "impressions", "direction": "horizontal", "minimumTagConfidenceLevel": 75, "statConfidence": 95}, "viewBy": "campaign", "workspaces": [4433], "workspaceIds": [4433], "isOnlyAssetsCreatedWithVidMobActive": false}, "filtersVersion": 1, "groupBy": {"columns": "campaign", "rows": "kpi"}, "name": "TEST 1303", "reportType": "MEDIA_IMPACT", "sortBy": {"sortBy": "account_average", "sortOrder": "ASC"}}, "prod": {"description": "", "filters": {"columnSelections": {}, "adAccountIds": ["****************", "***************"], "dateRange": {"startDate": "2024-01-03", "endDate": "2024-04-03"}, "isDefaultKPITimeRangeFilterActive": true, "isShowAppAdsOnlyActive": false, "isShowSparkAdsOnlyActive": false, "kpiId": "", "mediaTypes": ["VIDEO", "IMAGE"], "organizationId": "421fb903-40bc-4e59-beb5-6b026813752c", "platform": "FACEBOOK", "selectedRows": [], "statSettings": {"average": "element", "baseComparison": "impressions", "direction": "horizontal", "minimumTagConfidenceLevel": 75, "statConfidence": 95}, "viewBy": "duration", "workspaces": [31869], "workspaceIds": [31869], "isOnlyAssetsCreatedWithVidMobActive": false}, "filtersVersion": 1, "groupBy": {"columns": "duration", "rows": "kpi"}, "name": "TEST 1303", "reportType": "MEDIA_IMPACT", "sortBy": {"sortBy": "account_average", "sortOrder": "ASC"}}}