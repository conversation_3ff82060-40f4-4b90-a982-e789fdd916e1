{"dev": {"campaignObjectives": {"TWITTER": {"Awareness": ["AWARENESS", "REACH"], "Followers": ["FOLLOWERS"], "Video Views": ["VIDEO_VIEWS"], "In-Stream Video Views": ["PREROLL_VIEWS", "VIDEO_VIEWS_PREROLL"], "Website Clicks": ["WEBSITE_CLICKS"], "Tweet Engagements": ["ENGAGEMENTS", "TWEET_ENGAGEMENTS"], "App Installs": ["APP_INSTALLS"], "App Engagements": ["APP_ENGAGEMENTS"]}, "SNAPCHAT": {"Awareness": ["BRAND_AWARENESS"], "Consideration": ["APP_INSTALL", "APP_REENGAGEMENT", "ENGAGEMENT", "LEAD_GENERATION", "VIDEO_VIEW", "WEB_VIEW"], "Conversions": ["APP_CONVERSION", "CATALOG_SALES", "WEB_CONVERSION"]}}, "adPlacement": {"PINTEREST": {"Category": ["CATEGORY"], "Related Pins": ["RELATED_PINS"], "Home Feed": ["HOME_FEED"], "News Hub": ["NEWS_HUB"], "Search": ["SEARCH"]}, "LINKEDIN": {"LinkedIn": ["ON_SITE"], "Audience Network": ["OFF_SITE"], "Unknown": ["UNKNOWN"]}}, "adType": {"FACEBOOK": {"News Feed": ["desktop:facebook:feed", "desktop:instagram:feed", "mobile:facebook:feed", "mobile:instagram:feed", "mobile_app:facebook:feed", "mobile_app:instagram:feed", "mobile_app:instagram:instagram_profile_feed", "mobile_web:facebook:feed", "mobile_web:instagram:feed", "unknown:facebook:feed", "unknown:instagram:feed", "unknown:instagram:instagram_profile_feed"], "Stories": ["desktop:instagram:instagram_stories", "desktop:messenger:story", "mobile:facebook:facebook_stories", "mobile:instagram:instagram_stories", "mobile:messenger:messenger_stories", "mobile:messenger:story", "mobile_app:facebook:facebook_stories", "mobile_app:instagram:instagram_stories", "mobile_app:messenger:messenger_stories", "mobile_web:instagram:instagram_stories", "unknown:facebook:facebook_stories", "unknown:instagram:instagram_stories", "unknown:messenger:messenger_stories"], "In-Stream": ["desktop:audience_network:instream_video", "desktop:facebook:instream_video", "mobile:audience_network:instream_video", "mobile:facebook:instream_video", "mobile_app:facebook:instream_video", "tv:audience_network:instream_video", "unknown:facebook:instream_video"], "Marketplace": ["desktop:facebook:marketplace", "mobile:facebook:marketplace", "mobile_app:facebook:marketplace", "unknown:facebook:marketplace"], "Right-Hand Column": ["desktop:facebook:right_hand_column"], "Instant Article": ["mobile:facebook:instant_article", "mobile_app:facebook:instant_article", "unknown:facebook:instant_article"], "Suggested Video": ["desktop:facebook:suggested_video", "mobile:facebook:suggested_video", "mobile_app:facebook:suggested_video"], "An Classic": ["desktop:audience_network:an_classic", "mobile:audience_network:an_classic", "mobile_app:audience_network:an_classic", "unknown:audience_network:an_classic"], "Rewarded Video": ["desktop:audience_network:rewarded_video", "mobile:audience_network:rewarded_video", "mobile_app:audience_network:rewarded_video", "unknown:audience_network:rewarded_video"], "All Placements": ["desktop:audience_network:all_placements", "mobile:audience_network:all_placements", "mobile:messenger:all_placements", "mobile_app:messenger:all_placements"], "Explore": ["mobile:instagram:instagram_explore", "mobile_app:instagram:instagram_explore", "unknown:instagram:instagram_explore", "unknown:instagram:instagram_explore_grid_home"], "Groups": ["mobile_app:facebook:facebook_groups", "mobile_app:facebook:facebook_groups_feed", "unknown:facebook:facebook_groups_feed"], "Inbox": ["mobile:messenger:messenger_inbox", "mobile_app:messenger:messenger_inbox", "unknown:messenger:messenger_inbox"], "Search": ["desktop:facebook:search", "mobile:facebook:search_serp", "mobile_app:facebook:search", "mobile_app:facebook:search_serp", "mobile_app:instagram:instagram_search", "unknown:facebook:search"], "Video Feeds": ["desktop:facebook:video_feeds", "mobile:facebook:video_feeds", "mobile_app:facebook:video_feeds", "unknown:facebook:video_feeds"], "Sponsored Messages": ["desktop:messenger:sponsored_messages", "mobile:messenger:sponsored_messages"], "IGTV": ["mobile_app:instagram:instagram_igtv", "unknown:instagram:instagram_igtv"], "Unknown": ["desktop:facebook:unknown", "mobile_app:facebook:unknown", "mobile_app:unknown:unknown", "unknown:unknown:unknown"], "Instagram Reels": ["mobile_app:instagram:instagram_reels", "unknown:instagram:instagram_reels"], "Messenger Home": ["desktop:messenger:messenger_home", "mobile:messenger:messenger_home"], "Biz Disco Feed": ["mobile_app:facebook:biz_disco_feed", "unknown:facebook:biz_disco_feed"], "Facebook Reels Overlay": ["mobile_app:facebook:facebook_reels_overlay", "unknown:facebook:facebook_reels_overlay"], "Instagram Shop": ["mobile_app:instagram:instagram_shop", "unknown:instagram:instagram_shop"], "Instagram Shop Tab": ["mobile_app:instagram:instagram_shop_tab"], "Facebook Reels": ["mobile_app:facebook:ads_on_facebook_reels", "mobile_app:facebook:facebook_reels", "unknown:facebook:facebook_reels"], "Jobs Browser": ["mobile_app:facebook:jobs_browser"], "Instagram Explore Grid Home": ["mobile_app:instagram:instagram_explore_grid_home"], "Instagram Reels Overlay": ["mobile_app:instagram:instagram_reels_overlay"], "Instagram Search": ["unknown:instagram:instagram_search"]}, "ADWORDS": {"app_ad_type": ["APP_AD"], "normal_ad_type": ["APP_ENGAGEMENT_AD", "DYNAMIC_HTML5_AD", "HTML5_UPLOAD_AD", "IMAGE_AD", "IN_FEED_VIDEO_AD", "LEGACY_APP_INSTALL_AD", "LEGACY_RESPONSIVE_DISPLAY_AD", "SHOPPING_PRODUCT_AD", "VIDEO_AD", "VIDEO_BUMPER_AD", "VIDEO_NON_SKIPPABLE_IN_STREAM_AD", "VIDEO_OUTSTREAM_AD", "VIDEO_RESPONSIVE_AD", "VIDEO_TRUEVIEW_DISCOVERY_AD", "VIDEO_TRUEVIEW_IN_STREAM_AD"]}}, "facebookKpisLength": 163, "facebookAdSetIdentifier": 16, "instagramPageAdIdentifier": {"code": 201, "status": "OK", "resultLength": 10, "totalSize": 445}, "tiktokCampaignIdentifier": {"code": 201, "status": "OK", "resultLength": 7, "totalSize": 7}, "invalidFilterTypeErrorMessage": "Error fetching filter adSetIdentifier for user 20293 in organization 27a7e882-43de-4bfa-8f53-3b62875b8432. Platform tiktok does not support adSetIdentifier filters", "missingScopeErrorMessage": "Error fetching filter campaignIdentifier for user 20293 in organization 27a7e882-43de-4bfa-8f53-3b62875b8432. Ad account ids are required for ad identifier filter type campaignIdentifier"}, "stage": {"campaignObjectives": {"TWITTER": {"Awareness": ["AWARENESS", "REACH"], "Followers": ["FOLLOWERS"], "Video Views": ["VIDEO_VIEWS"], "In-Stream Video Views": ["PREROLL_VIEWS", "VIDEO_VIEWS_PREROLL"], "Website Clicks": ["WEBSITE_CLICKS"], "Tweet Engagements": ["ENGAGEMENTS", "TWEET_ENGAGEMENTS"], "App Installs": ["APP_INSTALLS"], "App Engagements": ["APP_ENGAGEMENTS"]}, "SNAPCHAT": {"Awareness": ["BRAND_AWARENESS"], "Consideration": ["APP_INSTALL", "APP_REENGAGEMENT", "ENGAGEMENT", "LEAD_GENERATION", "VIDEO_VIEW", "WEB_VIEW"], "Conversions": ["APP_CONVERSION", "CATALOG_SALES", "WEB_CONVERSION"]}}, "adPlacement": {"PINTEREST": {"Category": ["CATEGORY"], "Related Pins": ["RELATED_PINS"], "Home Feed": ["HOME_FEED"], "News Hub": ["NEWS_HUB"], "Search": ["SEARCH"]}, "LINKEDIN": {"LinkedIn": ["ON_SITE"], "Audience Network": ["OFF_SITE"], "Unknown": ["UNKNOWN"]}}, "adType": {"FACEBOOK": {"News Feed": ["desktop:facebook:feed", "desktop:instagram:feed", "mobile:facebook:feed", "mobile:instagram:feed", "mobile_app:facebook:feed", "mobile_app:instagram:feed", "mobile_app:instagram:instagram_profile_feed", "mobile_web:facebook:feed", "mobile_web:instagram:feed", "unknown:facebook:feed", "unknown:instagram:feed", "unknown:instagram:instagram_profile_feed"], "Stories": ["desktop:instagram:instagram_stories", "desktop:messenger:story", "mobile:facebook:facebook_stories", "mobile:instagram:instagram_stories", "mobile:messenger:messenger_stories", "mobile:messenger:story", "mobile_app:facebook:facebook_stories", "mobile_app:instagram:instagram_stories", "mobile_app:messenger:messenger_stories", "mobile_web:instagram:instagram_stories", "unknown:facebook:facebook_stories", "unknown:instagram:instagram_stories", "unknown:messenger:messenger_stories"], "In-Stream": ["desktop:audience_network:instream_video", "desktop:facebook:instream_video", "mobile:audience_network:instream_video", "mobile:facebook:instream_video", "mobile_app:facebook:instream_video", "tv:audience_network:instream_video", "unknown:facebook:instream_video"], "Marketplace": ["desktop:facebook:marketplace", "mobile:facebook:marketplace", "mobile_app:facebook:marketplace", "unknown:facebook:marketplace"], "Right-Hand Column": ["desktop:facebook:right_hand_column"], "Instant Article": ["mobile:facebook:instant_article", "mobile_app:facebook:instant_article", "unknown:facebook:instant_article"], "Suggested Video": ["desktop:facebook:suggested_video", "mobile:facebook:suggested_video", "mobile_app:facebook:suggested_video"], "An Classic": ["desktop:audience_network:an_classic", "mobile:audience_network:an_classic", "mobile_app:audience_network:an_classic", "unknown:audience_network:an_classic"], "Rewarded Video": ["desktop:audience_network:rewarded_video", "mobile:audience_network:rewarded_video", "mobile_app:audience_network:rewarded_video", "unknown:audience_network:rewarded_video"], "All Placements": ["desktop:audience_network:all_placements", "mobile:audience_network:all_placements", "mobile:messenger:all_placements", "mobile_app:messenger:all_placements"], "Explore": ["mobile:instagram:instagram_explore", "mobile_app:instagram:instagram_explore", "unknown:instagram:instagram_explore", "unknown:instagram:instagram_explore_grid_home"], "Groups": ["mobile_app:facebook:facebook_groups", "mobile_app:facebook:facebook_groups_feed", "unknown:facebook:facebook_groups_feed"], "Inbox": ["mobile:messenger:messenger_inbox", "mobile_app:messenger:messenger_inbox", "unknown:messenger:messenger_inbox"], "Search": ["desktop:facebook:search", "mobile:facebook:search_serp", "mobile_app:facebook:search", "mobile_app:facebook:search_serp", "mobile_app:instagram:instagram_search", "unknown:facebook:search"], "Video Feeds": ["desktop:facebook:video_feeds", "mobile:facebook:video_feeds", "mobile_app:facebook:video_feeds", "unknown:facebook:video_feeds"], "Sponsored Messages": ["desktop:messenger:sponsored_messages", "mobile:messenger:sponsored_messages"], "IGTV": ["mobile_app:instagram:instagram_igtv", "unknown:instagram:instagram_igtv"], "Unknown": ["desktop:facebook:unknown", "mobile_app:facebook:unknown", "mobile_app:unknown:unknown", "unknown:unknown:unknown"], "Instagram Reels": ["mobile_app:instagram:instagram_reels", "unknown:instagram:instagram_reels"], "Messenger Home": ["desktop:messenger:messenger_home", "mobile:messenger:messenger_home"], "Biz Disco Feed": ["mobile_app:facebook:biz_disco_feed", "unknown:facebook:biz_disco_feed"], "Facebook Reels Overlay": ["mobile_app:facebook:facebook_reels_overlay", "unknown:facebook:facebook_reels_overlay"], "Instagram Shop": ["mobile_app:instagram:instagram_shop", "unknown:instagram:instagram_shop"], "Instagram Shop Tab": ["mobile_app:instagram:instagram_shop_tab"], "Facebook Reels": ["mobile_app:facebook:ads_on_facebook_reels", "mobile_app:facebook:facebook_reels", "unknown:facebook:facebook_reels"], "Jobs Browser": ["mobile_app:facebook:jobs_browser"], "Instagram Explore Grid Home": ["mobile_app:instagram:instagram_explore_grid_home"], "Instagram Reels Overlay": ["mobile_app:instagram:instagram_reels_overlay"], "Instagram Search": ["unknown:instagram:instagram_search"]}, "ADWORDS": {"app_ad_type": ["APP_AD"], "normal_ad_type": ["APP_ENGAGEMENT_AD", "DYNAMIC_HTML5_AD", "HTML5_UPLOAD_AD", "IMAGE_AD", "IN_FEED_VIDEO_AD", "LEGACY_APP_INSTALL_AD", "LEGACY_RESPONSIVE_DISPLAY_AD", "SHOPPING_PRODUCT_AD", "VIDEO_AD", "VIDEO_BUMPER_AD", "VIDEO_NON_SKIPPABLE_IN_STREAM_AD", "VIDEO_OUTSTREAM_AD", "VIDEO_RESPONSIVE_AD", "VIDEO_TRUEVIEW_DISCOVERY_AD", "VIDEO_TRUEVIEW_IN_STREAM_AD"]}}, "facebookKpisLength": 166, "facebookAdSetIdentifier": 20, "instagramPageAdIdentifier": {"code": 201, "status": "OK", "resultLength": 10, "totalSize": 469}, "tiktokCampaignIdentifier": {"code": 201, "status": "OK", "resultLength": 1, "totalSize": 1}, "invalidFilterTypeErrorMessage": "Error fetching filter adSetIdentifier for user 6279 in organization cb90fd8c-4740-43c6-9def-98d9f8eb5a0b. Platform tiktok does not support adSetIdentifier filters", "missingScopeErrorMessage": "Error fetching filter campaignIdentifier for user 6279 in organization cb90fd8c-4740-43c6-9def-98d9f8eb5a0b. Ad account ids are required for ad identifier filter type campaignIdentifier"}, "prod": {"campaignObjectives": {"TWITTER": {"Awareness": ["AWARENESS", "REACH"], "Followers": ["FOLLOWERS"], "Video Views": ["VIDEO_VIEWS"], "In-Stream Video Views": ["PREROLL_VIEWS", "VIDEO_VIEWS_PREROLL"], "Website Clicks": ["WEBSITE_CLICKS"], "Tweet Engagements": ["ENGAGEMENTS", "TWEET_ENGAGEMENTS"], "App Installs": ["APP_INSTALLS"], "App Engagements": ["APP_ENGAGEMENTS"]}, "SNAPCHAT": {"Awareness": ["BRAND_AWARENESS"], "Consideration": ["APP_INSTALL", "APP_REENGAGEMENT", "ENGAGEMENT", "LEAD_GENERATION", "VIDEO_VIEW", "WEB_VIEW"], "Conversions": ["APP_CONVERSION", "CATALOG_SALES", "WEB_CONVERSION"]}}, "adPlacement": {"PINTEREST": {"Category": ["CATEGORY"], "Related Pins": ["RELATED_PINS"], "Home Feed": ["HOME_FEED"], "News Hub": ["NEWS_HUB"], "Search": ["SEARCH"]}, "LINKEDIN": {"LinkedIn": ["ON_SITE"], "Audience Network": ["OFF_SITE"], "Unknown": ["UNKNOWN"]}}, "adType": {"FACEBOOK": {"News Feed": ["desktop:facebook:feed", "desktop:instagram:feed", "mobile:facebook:feed", "mobile:instagram:feed", "mobile_app:facebook:feed", "mobile_app:instagram:feed", "mobile_app:instagram:instagram_profile_feed", "mobile_web:facebook:feed", "mobile_web:instagram:feed", "unknown:facebook:feed", "unknown:instagram:feed", "unknown:instagram:instagram_profile_feed"], "Stories": ["desktop:instagram:instagram_stories", "desktop:messenger:story", "mobile:facebook:facebook_stories", "mobile:instagram:instagram_stories", "mobile:messenger:messenger_stories", "mobile:messenger:story", "mobile_app:facebook:facebook_stories", "mobile_app:instagram:instagram_stories", "mobile_app:messenger:messenger_stories", "mobile_web:instagram:instagram_stories", "unknown:facebook:facebook_stories", "unknown:instagram:instagram_stories", "unknown:messenger:messenger_stories"], "In-Stream": ["desktop:audience_network:instream_video", "desktop:facebook:instream_video", "mobile:audience_network:instream_video", "mobile:facebook:instream_video", "mobile_app:facebook:instream_video", "tv:audience_network:instream_video", "unknown:facebook:instream_video"], "Marketplace": ["desktop:facebook:marketplace", "mobile:facebook:marketplace", "mobile_app:facebook:marketplace", "unknown:facebook:marketplace"], "Right-Hand Column": ["desktop:facebook:right_hand_column"], "Instant Article": ["mobile:facebook:instant_article", "mobile_app:facebook:instant_article", "unknown:facebook:instant_article"], "Suggested Video": ["desktop:facebook:suggested_video", "mobile:facebook:suggested_video", "mobile_app:facebook:suggested_video"], "An Classic": ["desktop:audience_network:an_classic", "mobile:audience_network:an_classic", "mobile_app:audience_network:an_classic", "unknown:audience_network:an_classic"], "Rewarded Video": ["desktop:audience_network:rewarded_video", "mobile:audience_network:rewarded_video", "mobile_app:audience_network:rewarded_video", "unknown:audience_network:rewarded_video"], "All Placements": ["desktop:audience_network:all_placements", "mobile:audience_network:all_placements", "mobile:messenger:all_placements", "mobile_app:messenger:all_placements"], "Explore": ["mobile:instagram:instagram_explore", "mobile_app:instagram:instagram_explore", "unknown:instagram:instagram_explore", "unknown:instagram:instagram_explore_grid_home"], "Groups": ["mobile_app:facebook:facebook_groups", "mobile_app:facebook:facebook_groups_feed", "unknown:facebook:facebook_groups_feed"], "Inbox": ["mobile:messenger:messenger_inbox", "mobile_app:messenger:messenger_inbox", "unknown:messenger:messenger_inbox"], "Search": ["desktop:facebook:search", "mobile:facebook:search_serp", "mobile_app:facebook:search", "mobile_app:facebook:search_serp", "mobile_app:instagram:instagram_search", "unknown:facebook:search"], "Video Feeds": ["desktop:facebook:video_feeds", "mobile:facebook:video_feeds", "mobile_app:facebook:video_feeds", "unknown:facebook:video_feeds"], "Sponsored Messages": ["desktop:messenger:sponsored_messages", "mobile:messenger:sponsored_messages"], "IGTV": ["mobile_app:instagram:instagram_igtv", "unknown:instagram:instagram_igtv"], "Unknown": ["desktop:facebook:unknown", "mobile_app:facebook:unknown", "mobile_app:unknown:unknown", "unknown:unknown:unknown"], "Instagram Reels": ["mobile_app:instagram:instagram_reels", "unknown:instagram:instagram_reels"], "Messenger Home": ["desktop:messenger:messenger_home", "mobile:messenger:messenger_home"], "Biz Disco Feed": ["mobile_app:facebook:biz_disco_feed", "unknown:facebook:biz_disco_feed"], "Facebook Reels Overlay": ["mobile_app:facebook:facebook_reels_overlay", "unknown:facebook:facebook_reels_overlay"], "Instagram Shop": ["mobile_app:instagram:instagram_shop", "unknown:instagram:instagram_shop"], "Instagram Shop Tab": ["mobile_app:instagram:instagram_shop_tab"], "Facebook Reels": ["mobile_app:facebook:ads_on_facebook_reels", "mobile_app:facebook:facebook_reels", "unknown:facebook:facebook_reels"], "Jobs Browser": ["mobile_app:facebook:jobs_browser"], "Instagram Explore Grid Home": ["mobile_app:instagram:instagram_explore_grid_home"], "Instagram Reels Overlay": ["mobile_app:instagram:instagram_reels_overlay"], "Instagram Search": ["unknown:instagram:instagram_search"]}, "ADWORDS": {"app_ad_type": ["APP_AD"], "normal_ad_type": ["APP_ENGAGEMENT_AD", "DYNAMIC_HTML5_AD", "HTML5_UPLOAD_AD", "IMAGE_AD", "IN_FEED_VIDEO_AD", "LEGACY_APP_INSTALL_AD", "LEGACY_RESPONSIVE_DISPLAY_AD", "SHOPPING_PRODUCT_AD", "VIDEO_AD", "VIDEO_BUMPER_AD", "VIDEO_NON_SKIPPABLE_IN_STREAM_AD", "VIDEO_OUTSTREAM_AD", "VIDEO_RESPONSIVE_AD", "VIDEO_TRUEVIEW_DISCOVERY_AD", "VIDEO_TRUEVIEW_IN_STREAM_AD"]}}, "facebookKpisLength": 166, "facebookAdSetIdentifier": 20, "instagramPageAdIdentifier": {"code": 400, "status": "ERROR", "resultLength": null, "totalSize": null}, "tiktokCampaignIdentifier": {"code": 400, "status": "ERROR", "resultLength": null, "totalSize": null}, "invalidFilterTypeErrorMessage": "Error fetching filter adSetIdentifier for user 45019 in organization 421fb903-40bc-4e59-beb5-6b026813752c. Ad account ids are not valid for organization 421fb903-40bc-4e59-beb5-6b026813752c and workspaces 31869", "missingScopeErrorMessage": "Error fetching filter campaignIdentifier for user 45019 in organization 421fb903-40bc-4e59-beb5-6b026813752c. Ad account ids are required for ad identifier filter type campaignIdentifier"}}