{"dev": {"workspaceId": "23245", "preFlightPayload": {"filters": [{"fieldName": "batchType", "operator": "equals", "value": "PRE_FLIGHT"}, {"fieldName": "mediaCreateDate", "operator": "between", "value": ["2023-01-01T06:00:00.000Z", "2023-12-31T06:00:00.000Z"]}, {"fieldName": "creativeType", "operator": "in", "value": ["VIDEO", "IMAGE"]}, {"fieldName": "workspaceId", "operator": "equals", "value": 23142}, {"fieldName": "channel", "operator": "in", "value": ["AMAZON", "DV360", "ADWORDS", "LINKEDIN", "FACEBOOK", "PINTEREST", "REDDIT", "SNAPCHAT", "TIKTOK", "TWITTER"]}, {"fieldName": "criteriaGroupId", "operator": "in", "value": ["c511ab12-26f3-4c6f-9713-458564867bc3", "81743880-0a2a-4901-8456-3592e6964809"]}, {"fieldName": "criteriaIsOptional", "operator": "equals", "value": false}, {"fieldName": "criteriaIsOrganizationCriteria", "operator": "equals", "value": false}], "analyticsFilters": {}, "groupBy": {"columns": ["criteria", "batchType"], "rows": ["workspace"]}}, "inFlightPayload": {"filters": [{"fieldName": "market", "operator": "equals", "value": "usa"}, {"fieldName": "mediaCreateDate", "operator": "between", "value": ["2022-01-01T06:00:00.000Z", "2024-09-08T23:12:16.800Z"]}, {"fieldName": "creativeType", "operator": "in", "value": ["VIDEO", "IMAGE"]}, {"fieldName": "criteriaGroupId", "operator": "in", "value": ["c511ab12-26f3-4c6f-9713-458564867bc3", "81743880-0a2a-4901-8456-3592e6964809"]}, {"fieldName": "criteriaIsOptional", "operator": "equals", "value": false}, {"fieldName": "criteriaIsOrganizationCriteria", "operator": "equals", "value": false}, {"fieldName": "workspaceId", "operator": "equals", "value": 23142}, {"fieldName": "batchType", "operator": "equals", "value": "IN_FLIGHT"}, {"fieldName": "channel", "operator": "in", "value": ["AMAZON", "DV360", "ADWORDS", "LINKEDIN", "FACEBOOK", "PINTEREST", "REDDIT", "SNAPCHAT", "TIKTOK", "TWITTER"]}, {"fieldName": "platformAccountId", "operator": "in", "value": ["*********", "*********", "**********", "*********", "***************", "***************", "***************", "************", "t2_t5vae3ih", "1992fa19-c79b-4ec4-9771-1069c1cb9ba5", "243c4f1e-ef86-4b61-a345-83ed00b34134", "092aa95e-3ff2-47d1-a9f7-319e25c5568a", "6807608752410198022", "7190025940280344577", "18ce541p4za"]}], "analyticsFilters": {"LINKEDIN": [{"type": "campaign_identifier", "operator": "in", "values": ["*********", "*********", "*********", "*********", "*********", "*********", "*********", "*********", "*********", "*********", "*********", "*********", "*********", "*********", "*********", "*********", "*********", "*********", "*********", "*********"]}], "FACEBOOK": [{"type": "campaign_objective", "operator": "in", "values": ["BRAND_AWARENESS", "LOCAL_AWARENESS", "OUTCOME_AWARENESS", "REACH", "APP_INSTALLS", "CANVAS_APP_INSTALLS", "EVENT_RESPONSES", "LEAD_GENERATION", "LINK_CLICKS", "MESSAGES", "MOBILE_APP_ENGAGEMENT", "MOBILE_APP_INSTALLS", "OFFER_CLAIMS", "OUTCOME_APP_PROMOTION", "OUTCOME_ENGAGEMENT", "OUTCOME_LEADS", "OUTCOME_TRAFFIC", "PAGE_LIKES", "POST_ENGAGEMENT", "TRAFFIC", "VIDEO_VIEWS"]}], "REDDIT": [{"type": "ad_identifier", "operator": "in", "values": ["1552529063524946129", "1601673784239851882", "1553817906782180073", "1553817906782180073"]}]}, "groupBy": {"columns": ["criteria", "batchType"], "rows": ["workspace", "market"]}}, "emptyPayload": {"filters": [{"fieldName": "mediaCreateDate", "operator": "between", "value": ["2024-01-01T06:00:00.000Z", "2024-02-08T23:12:16.800Z"]}, {"fieldName": "workspaceId", "operator": "equals", "value": 23245}, {"fieldName": "batchType", "operator": "equals", "value": "PRE_FLIGHT"}, {"fieldName": "channel", "operator": "in", "value": ["AMAZON"]}], "analyticsFilters": {}, "groupBy": {"columns": ["criteria", "batchType"], "rows": ["workspace", "market"]}}}, "stage": {"workspaceId": "2735", "preFlightPayload": {"filters": [{"fieldName": "batchType", "operator": "equals", "value": "PRE_FLIGHT"}, {"fieldName": "mediaCreateDate", "operator": "between", "value": ["2023-01-01T06:00:00.000Z", "2024-09-05T17:39:29.601Z"]}, {"fieldName": "creativeType", "operator": "in", "value": ["VIDEO", "IMAGE"]}, {"fieldName": "criteriaGroupId", "operator": "in", "value": ["2547ed9c-c948-4953-ac7b-49e6787f434e", "db4887ea-aa4b-4e26-98e0-3fc85797fa94", "86c4af5d-ec21-4c60-80da-c8afd38f3152"]}, {"fieldName": "criteriaIsOptional", "operator": "equals", "value": false}, {"fieldName": "criteriaIsOrganizationCriteria", "operator": "equals", "value": false}, {"fieldName": "workspaceId", "operator": "equals", "value": 2735}, {"fieldName": "channel", "operator": "in", "value": ["FACEBOOK", "PINTEREST", "REDDIT", "TIKTOK"]}, {"fieldName": "brandId", "operator": "in", "value": ["2ce45d68-ff7e-4a2f-9360-027cbced4f75", "1ee1cdf5-d5f2-433d-bba8-fefdc6a723b6", "7d3e0943-2e8f-4b0f-9c0a-dc188268d90f", "090c8998-1f16-4f8a-9b26-bc7ac9457ccc", "b4ddb19a-f7b3-4319-9b77-39e0cedf6508", "a0b86b3c-a9a0-47b0-a344-2f8282b88a70", "b586a812-c0ed-4f3a-aba9-79b08110e870", "54664bb8-1f3e-48f1-a96d-7022b969b261", "f66863ad-6ee8-4de4-8abf-cac7b19d4c3d", "ec29eeb5-2d58-11ee-a262-12e7c69eb75f", "c5346daf-725e-486e-9c97-086e3adccb76"]}, {"fieldName": "market", "operator": "in", "value": ["afg", "ala", "alb", "dza", "asm", "and", "ago", "aia", "ata", "atg", "arg", "arm", "abw", "aus", "aut", "aze", "bhs", "bhr", "bgd", "brb", "blr", "bel", "blz", "ben", "bmu", "btn", "bol", "bih", "bwa", "bvt", "bra", "iot", "vgb", "brn", "bgr", "bfa", "bdi", "khm", "cmr", "can", "cpv", "cym", "caf", "tcd", "chl", "chn", "cxr", "cck", "col", "com", "cok", "cri", "civ", "hrv", "cub", "cyp", "cze", "prk", "dnk", "dji", "dma", "dom", "ecu", "egy", "slv", "gnq", "eri", "est", "swz", "eth", "flk", "fro", "fsm", "fji", "fin", "fra", "guf", "pyf", "atf", "gab", "gmb", "geo", "deu", "gha", "gib", "grc", "grl", "grd", "glp", "gum", "gtm", "gin", "gnb", "guy", "hti", "hmd", "hnd", "hkg", "hun", "isl", "ind", "idn", "irq", "irl", "irn", "imn", "isr", "ita", "jam", "jpn", "jor", "kaz", "ken", "kir", "kwt", "kgz", "lao", "lva", "lbn", "lso", "lbr", "lby", "lie", "ltu", "lux", "mac", "mdg", "mwi", "mys", "mdv", "mli", "mlt", "mhl", "mtq", "mrt", "mus", "myt", "mex", "mco", "mng", "msr", "mar", "moz", "mmr", "nam", "nru", "npl", "nld", "ant", "ncl", "nzl", "nic", "ner", "nga", "niu", "nfk", "mnp", "nor", "pse", "omn", "pak", "plw", "pan", "png", "pry", "per", "phl", "pcn", "pol", "prt", "pri", "qat", "kor", "mda", "cog", "reu", "rou", "rus", "rwa", "shn", "kna", "lca", "vct", "spm", "wsm", "smr", "stp", "sau", "sen", "srb", "scg", "syc", "sle", "sgp", "svk", "svn", "slb", "som", "zaf", "sgs", "esp", "lka", "sdn", "sur", "sjm", "swe", "che", "syr", "twn", "tjk", "tha", "cod", "mkd", "tls", "tgo", "tkl", "ton", "tto", "tun", "tur", "tkm", "tca", "tuv", "vir", "uga", "ukr", "are", "gbr", "tza", "usa", "umi", "ury", "uzb", "vut", "vat", "ven", "vnm", "wlf", "esh", "yem", "zmb", "zwe"]}], "analyticsFilters": {}, "groupBy": {"columns": ["criteria", "batchType"], "rows": ["workspace"]}}, "inFlightPayload": {"filters": [{"fieldName": "batchType", "operator": "equals", "value": "IN_FLIGHT"}, {"fieldName": "mediaCreateDate", "operator": "between", "value": ["2023-01-01T06:00:00.000Z", "2024-09-05T17:39:29.601Z"]}, {"fieldName": "creativeType", "operator": "in", "value": ["VIDEO", "IMAGE"]}, {"fieldName": "workspaceId", "operator": "equals", "value": 2735}, {"fieldName": "criteriaGroupId", "operator": "in", "value": ["2547ed9c-c948-4953-ac7b-49e6787f434e", "db4887ea-aa4b-4e26-98e0-3fc85797fa94", "86c4af5d-ec21-4c60-80da-c8afd38f3152"]}, {"fieldName": "channel", "operator": "in", "value": ["ADWORDS", "AMAZON", "DV360", "FACEBOOK", "LINKEDIN", "PINTEREST", "REDDIT", "SNAPCHAT", "TIKTOK", "TWITTER"]}, {"fieldName": "brandId", "operator": "in", "value": ["2ce45d68-ff7e-4a2f-9360-027cbced4f75", "1ee1cdf5-d5f2-433d-bba8-fefdc6a723b6", "7d3e0943-2e8f-4b0f-9c0a-dc188268d90f", "090c8998-1f16-4f8a-9b26-bc7ac9457ccc", "b4ddb19a-f7b3-4319-9b77-39e0cedf6508", "a0b86b3c-a9a0-47b0-a344-2f8282b88a70", "b586a812-c0ed-4f3a-aba9-79b08110e870", "54664bb8-1f3e-48f1-a96d-7022b969b261", "f66863ad-6ee8-4de4-8abf-cac7b19d4c3d", "ec29eeb5-2d58-11ee-a262-12e7c69eb75f", "c5346daf-725e-486e-9c97-086e3adccb76"]}, {"fieldName": "market", "operator": "in", "value": ["afg", "ala", "alb", "dza", "asm", "and", "ago", "aia", "ata", "atg", "arg", "arm", "abw", "aus", "aut", "aze", "bhs", "bhr", "bgd", "brb", "blr", "bel", "blz", "ben", "bmu", "btn", "bol", "bih", "bwa", "bvt", "bra", "iot", "vgb", "brn", "bgr", "bfa", "bdi", "khm", "cmr", "can", "cpv", "cym", "caf", "tcd", "chl", "chn", "cxr", "cck", "col", "com", "cok", "cri", "civ", "hrv", "cub", "cyp", "cze", "prk", "dnk", "dji", "dma", "dom", "ecu", "egy", "slv", "gnq", "eri", "est", "swz", "eth", "flk", "fro", "fsm", "fji", "fin", "fra", "guf", "pyf", "atf", "gab", "gmb", "geo", "deu", "gha", "gib", "grc", "grl", "grd", "glp", "gum", "gtm", "gin", "gnb", "guy", "hti", "hmd", "hnd", "hkg", "hun", "isl", "ind", "idn", "irq", "irl", "irn", "imn", "isr", "ita", "jam", "jpn", "jor", "kaz", "ken", "kir", "kwt", "kgz", "lao", "lva", "lbn", "lso", "lbr", "lby", "lie", "ltu", "lux", "mac", "mdg", "mwi", "mys", "mdv", "mli", "mlt", "mhl", "mtq", "mrt", "mus", "myt", "mex", "mco", "mng", "msr", "mar", "moz", "mmr", "nam", "nru", "npl", "nld", "ant", "ncl", "nzl", "nic", "ner", "nga", "niu", "nfk", "mnp", "nor", "pse", "omn", "pak", "plw", "pan", "png", "pry", "per", "phl", "pcn", "pol", "prt", "pri", "qat", "kor", "mda", "cog", "reu", "rou", "rus", "rwa", "shn", "kna", "lca", "vct", "spm", "wsm", "smr", "stp", "sau", "sen", "srb", "scg", "syc", "sle", "sgp", "svk", "svn", "slb", "som", "zaf", "sgs", "esp", "lka", "sdn", "sur", "sjm", "swe", "che", "syr", "twn", "tjk", "tha", "cod", "mkd", "tls", "tgo", "tkl", "ton", "tto", "tun", "tur", "tkm", "tca", "tuv", "vir", "uga", "ukr", "are", "gbr", "tza", "usa", "umi", "ury", "uzb", "vut", "vat", "ven", "vnm", "wlf", "esh", "yem", "zmb", "zwe"]}], "analyticsFilters": {}, "groupBy": {"columns": ["criteria", "batchType"], "rows": ["workspace"]}}, "emptyPayload": {"filters": [{"fieldName": "mediaCreateDate", "operator": "between", "value": ["2024-06-08T22:43:09.621Z", "2024-09-08T22:43:09.621Z"]}, {"fieldName": "creativeType", "operator": "in", "value": ["VIDEO", "IMAGE"]}, {"fieldName": "workspaceId", "operator": "equals", "value": 2735}, {"fieldName": "batchType", "operator": "equals", "value": "IN_FLIGHT"}, {"fieldName": "channel", "operator": "in", "value": ["DV360"]}, {"fieldName": "platformAccountId", "operator": "in", "value": ["*********", "*********"]}], "analyticsFilters": {}, "groupBy": {"columns": ["criteria", "batchType"], "rows": ["workspace"]}}}, "prod": {"workspaceId": "31869", "preFlightPayload": {"filters": [{"fieldName": "batchType", "operator": "equals", "value": "PRE_FLIGHT"}, {"fieldName": "mediaCreateDate", "operator": "between", "value": ["2022-06-08T22:06:15.276Z", "2024-09-08T22:06:15.276Z"]}, {"fieldName": "creativeType", "operator": "in", "value": ["VIDEO", "IMAGE"]}, {"fieldName": "workspaceId", "operator": "in", "value": [31869]}, {"fieldName": "channel", "operator": "in", "value": ["AMAZON", "DV360", "ADWORDS", "LINKEDIN", "FACEBOOK", "PINTEREST", "REDDIT", "SNAPCHAT", "TIKTOK", "TWITTER"]}, {"fieldName": "market", "operator": "equals", "value": "usa"}, {"fieldName": "criteriaIsOptional", "operator": "equals", "value": false}, {"fieldName": "criteriaIsOrganizationCriteria", "operator": "equals", "value": false}, {"fieldName": "brandId", "operator": "equals", "value": "ea73a944-e77a-43af-a9ae-cf255c8037b5"}], "analyticsFilters": {}, "groupBy": {"columns": ["criteria", "batchType"], "rows": ["brand", "market"]}}, "inFlightPayload": {"filters": [{"fieldName": "mediaCreateDate", "operator": "between", "value": ["2023-01-01T06:00:00.000Z", "2024-09-08T22:06:15.276Z"]}, {"fieldName": "creativeType", "operator": "in", "value": ["VIDEO", "IMAGE"]}, {"fieldName": "workspaceId", "operator": "in", "value": [31869]}, {"fieldName": "batchType", "operator": "equals", "value": "IN_FLIGHT"}, {"fieldName": "channel", "operator": "in", "value": ["AMAZON", "DV360", "ADWORDS", "LINKEDIN", "FACEBOOK", "PINTEREST", "REDDIT", "SNAPCHAT", "TIKTOK", "TWITTER"]}, {"fieldName": "platformAccountId", "operator": "in", "value": ["*********", "***************", "****************", "************"]}], "analyticsFilters": {"FACEBOOK": [{"type": "adset_identifier", "operator": "in", "values": ["*************", "*************", "*************", "*************", "*************", "*************"]}], "LINKEDIN": [{"type": "creative_impressions", "values": {"operator": ">", "value": 100}}], "PINTEREST": [{"type": "campaign_objective", "operator": "in", "values": ["AWARENESS", "AWARENESS_PREMIUM_RESERVED", "AWARENESS_RESERVED", "ENGAGEMENTS", "ENGAGEMENT", "UNKNOWN", "TRAFFIC", "APP_INSTALLS", "APP_INSTALL", "VIDEO_VIEWS", "VIDEO_VIEW", "SHOPPING", "CONVERSIONS", "WEB_CONVERSION", "CATALOG_SALES", "WEB_SESSIONS", "CONSIDERATION"]}]}, "groupBy": {"columns": ["criteria", "batchType"], "rows": ["brand", "market"]}}, "emptyPayload": {"filters": [{"fieldName": "batchType", "operator": "equals", "value": "IN_FLIGHT"}, {"fieldName": "mediaCreateDate", "operator": "between", "value": ["2022-06-08T22:06:15.276Z", "2024-09-08T22:06:15.276Z"]}, {"fieldName": "creativeType", "operator": "in", "value": ["VIDEO", "IMAGE"]}, {"fieldName": "workspaceId", "operator": "in", "value": [31869]}, {"fieldName": "channel", "operator": "in", "value": ["AMAZON", "DV360", "ADWORDS", "LINKEDIN", "FACEBOOK", "PINTEREST", "REDDIT", "SNAPCHAT", "TIKTOK", "TWITTER"]}, {"fieldName": "market", "operator": "equals", "value": "usa"}, {"fieldName": "brandId", "operator": "equals", "value": "ea73a944-e77a-43af-a9ae-cf255c8037b5"}], "analyticsFilters": {}, "groupBy": {"columns": ["criteria", "batchType"], "rows": ["brand", "market"]}}}}