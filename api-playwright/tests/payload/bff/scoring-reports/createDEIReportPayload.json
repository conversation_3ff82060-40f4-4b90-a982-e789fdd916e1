{"dev": {"workspaceId": "23245", "payload": {"filters": [{"fieldName": "mediaCreateDate", "operator": "between", "value": ["2021-01-01T00:00:00.000Z", "2023-12-31T00:00:00.000Z"]}, {"fieldName": "channel", "operator": "in", "value": ["ADWORDS", "AMAZON", "DV360", "FACEBOOK", "LINKEDIN", "PINTEREST", "REDDIT", "SNAPCHAT", "TIKTOK", "TWITTER"]}, {"fieldName": "market", "operator": "in", "value": ["afg", "ala", "alb", "dza", "asm", "and", "ago", "aia", "ata", "atg", "arg", "arm", "abw", "aus", "aut", "aze", "bhs", "bhr", "bgd", "brb", "blr", "bel", "blz", "ben", "bmu", "btn", "bol", "bih", "bwa", "bvt", "bra", "iot", "vgb", "brn", "bgr", "bfa", "bdi", "khm", "cmr", "can", "cpv", "cym", "caf", "tcd", "chl", "chn", "cxr", "cck", "col", "com", "cok", "cri", "civ", "hrv", "cub", "cyp", "cze", "prk", "dnk", "dji", "dma", "dom", "ecu", "egy", "slv", "gnq", "eri", "est", "swz", "eth", "flk", "fro", "fsm", "fji", "fin", "fra", "guf", "pyf", "atf", "gab", "gmb", "geo", "deu", "gha", "gib", "grc", "grl", "grd", "glp", "gum", "gtm", "gin", "gnb", "guy", "hti", "hmd", "hnd", "hkg", "hun", "isl", "ind", "idn", "irq", "irl", "irn", "imn", "isr", "ita", "jam", "jpn", "jor", "kaz", "ken", "kir", "kwt", "kgz", "lao", "lva", "lbn", "lso", "lbr", "lby", "lie", "ltu", "lux", "mac", "mdg", "mwi", "mys", "mdv", "mli", "mlt", "mhl", "mtq", "mrt", "mus", "myt", "mex", "mco", "mng", "msr", "mar", "moz", "mmr", "nam", "nru", "npl", "nld", "ant", "ncl", "nzl", "nic", "ner", "nga", "niu", "nfk", "mnp", "nor", "pse", "omn", "pak", "plw", "pan", "png", "pry", "per", "phl", "pcn", "pol", "prt", "pri", "qat", "kor", "mda", "cog", "reu", "rou", "rus", "rwa", "shn", "kna", "lca", "vct", "spm", "wsm", "smr", "stp", "sau", "sen", "srb", "scg", "syc", "sle", "sgp", "svk", "svn", "slb", "som", "zaf", "sgs", "esp", "lka", "sdn", "sur", "sjm", "swe", "che", "syr", "twn", "tjk", "tha", "cod", "mkd", "tls", "tgo", "tkl", "ton", "tto", "tun", "tur", "tkm", "tca", "tuv", "vir", "uga", "ukr", "are", "gbr", "tza", "usa", "umi", "ury", "uzb", "vut", "vat", "ven", "vnm", "wlf", "esh", "yem", "zmb", "zwe", "NOT_SPECIFIED"]}, {"fieldName": "workspaceId", "operator": "in", "value": ["23245", "23142"]}, {"fieldName": "brandId", "operator": "in", "value": ["af2bb426-1452-4681-bf3d-4cfcbd639431", "9ae2b432-3642-4272-a054-f363cde33e80", "3181f8d4-0572-4f8a-ae55-ee626cce2133", "7cdc3558-8cdf-4096-be1b-2e8c0292a3b2", "c8e27bf9-b7f7-427f-8d2e-ac4d56822d4f", "fa6e0efe-4c9a-4dcc-9b16-9a02a2f1400e", "0667d5fb-ee8d-49f4-a58b-f90cf8435da5", "62e52542-ed23-49c2-9380-0365820170dc", "e555bbb2-6087-40ef-83e9-86c62fbb4a91", "b343e371-db33-46a7-8774-5a296c8af119", "15f4a7e2-77be-4a2c-b2fd-32012acdbe9a", "547f7e4f-af49-4122-b092-e8a1475293e8", "24a758f2-ca40-4775-86fa-d84f3e34f4c7", "df226c28-b078-42f5-91b1-f502e5965133", "3a98dd8d-9428-4509-a2b0-dfcb9ad86b9d", "416167a7-573f-459b-b91b-62d9eb04992c", "35f2bbfb-d92f-4daa-8e6f-b0c067a9ccc9", "b4464db1-beb4-4ba9-a554-2fce12711914", "74d48b58-d1b7-447d-bf2d-f462afa5fe6f", "d739278d-3673-44a8-aa26-10cadc3f4aa8", "18dc798f-4c08-41b8-a4e6-6735eec1c649", "3f9dfb2f-72a2-4c6b-9e9a-04e85bbd5473", "63030d12-7d06-4ba8-9f3a-69bc5bfc1855", "960c6813-7d59-43bd-81f5-d7e75062fb23", "aaf22b10-db8d-4094-8440-546dbebc727a", "5b935600-4d8d-48bd-9aef-090ce2a5b878", "b703b040-3800-4cb5-a22d-ec15ad58071e", "ccf4b2ab-f38f-4cd1-92de-cf53de3e3af0", "f47ac10b-58cc-4372-a567-0e02b2c3d479", "0d64420b-fe3a-4147-a401-2f3ec9765262", "54083074-de64-4f81-8db4-c736abf577ee", "3c01e70f-93b2-40b7-aa4f-46ee3dc204f3", "68f79a2b-32be-4027-8415-327b09c74395", "399754a6-1cfa-4333-8306-58435fc7f9c6", "4a99d022-8cc6-4512-b1a4-62566ca5d92c", "5f70330f-1c46-4a0e-a5d2-e6f42d75bcb2", "af70e4e8-d490-475e-86ce-7850bae18640", "NOT_SPECIFIED"]}, {"fieldName": "creativeType", "operator": "in", "value": ["VIDEO", "IMAGE", "HTML", "ANIMATED_IMAGE"]}], "groupBy": {"columns": [], "rows": []}, "analyticsFilters": {"FACEBOOK": [{"type": "campaign_identifier", "values": ["6258195535002", "6259944136202", "6258197676402", "6259943054602", "6266530813402", "6279194148802", "6291137663002", "6300666198002"]}, {"type": "adset_identifier", "values": ["6258195535402", "6258197449602", "6258196910202", "6258196677202", "6259944142202", "6259943056802", "6259944135002", "6259943053602", "6259945485202", "6258199689002", "6262672752602", "6263346800402", "6259944141802", "6260878438602", "6266530816402", "6266530816202", "6267073788402", "6267072999602", "6268077064402", "6268076231602", "6279194147002", "6279194149002", "6285591628002", "6291137663202", "6294868207002", "6300666198402", "6259944142002", "6258197677002", "6272259189602", "6274890998002"]}, {"type": "media_type", "values": ["VIDEO", "IMAGE"]}, {"type": "created_by_vidmob", "values": ["true"]}], "ADWORDS": [{"type": "media_type", "values": ["VIDEO"]}]}}}, "stage": {"workspaceId": "4433", "payload": {"groupBy": {"columns": [], "rows": []}, "filters": [{"fieldName": "mediaCreateDate", "operator": "between", "value": ["2021-01-01T00:00:00.000Z", "2023-12-31T00:00:00.000Z"]}, {"fieldName": "channel", "operator": "in", "value": ["ADWORDS", "ALL_PLATFORMS", "AMAZON", "DV360", "FACEBOOK", "LINKEDIN", "PINTEREST", "REDDIT", "SNAPCHAT", "TIKTOK", "TWITTER"]}, {"fieldName": "market", "operator": "in", "value": ["afg", "ala", "alb", "dza", "asm", "and", "ago", "aia", "ata", "atg", "arg", "arm", "abw", "aus", "aut", "aze", "bhs", "bhr", "bgd", "brb", "blr", "bel", "blz", "ben", "bmu", "btn", "bol", "bih", "bwa", "bvt", "bra", "iot", "vgb", "brn", "bgr", "bfa", "bdi", "khm", "cmr", "can", "cpv", "cym", "caf", "tcd", "chl", "chn", "cxr", "cck", "col", "com", "cok", "cri", "civ", "hrv", "cub", "cyp", "cze", "prk", "dnk", "dji", "dma", "dom", "ecu", "egy", "slv", "gnq", "eri", "est", "swz", "eth", "flk", "fro", "fsm", "fji", "fin", "fra", "guf", "pyf", "atf", "gab", "gmb", "geo", "deu", "gha", "gib", "grc", "grl", "grd", "glp", "gum", "gtm", "gin", "gnb", "guy", "hti", "hmd", "hnd", "hkg", "hun", "isl", "ind", "idn", "irq", "irl", "irn", "imn", "isr", "ita", "jam", "jpn", "jor", "kaz", "ken", "kir", "kwt", "kgz", "lao", "lva", "lbn", "lso", "lbr", "lby", "lie", "ltu", "lux", "mac", "mdg", "mwi", "mys", "mdv", "mli", "mlt", "mhl", "mtq", "mrt", "mus", "myt", "mex", "mco", "mng", "msr", "mar", "moz", "mmr", "nam", "nru", "npl", "nld", "ant", "ncl", "nzl", "nic", "ner", "nga", "niu", "nfk", "mnp", "nor", "pse", "omn", "pak", "plw", "pan", "png", "pry", "per", "phl", "pcn", "pol", "prt", "pri", "qat", "kor", "mda", "cog", "reu", "rou", "rus", "rwa", "shn", "kna", "lca", "vct", "spm", "wsm", "smr", "stp", "sau", "sen", "srb", "scg", "syc", "sle", "sgp", "svk", "svn", "slb", "som", "zaf", "sgs", "esp", "lka", "sdn", "sur", "sjm", "swe", "che", "syr", "twn", "tjk", "tha", "cod", "mkd", "tls", "tgo", "tkl", "ton", "tto", "tun", "tur", "tkm", "tca", "tuv", "vir", "uga", "ukr", "are", "gbr", "tza", "usa", "umi", "ury", "uzb", "vut", "vat", "ven", "vnm", "wlf", "esh", "yem", "zmb", "zwe", "NOT_SPECIFIED"]}, {"fieldName": "workspaceId", "operator": "in", "value": ["4433", "2735"]}, {"fieldName": "brandId", "operator": "in", "value": ["2ce45d68-ff7e-4a2f-9360-027cbced4f75", "7d3e0943-2e8f-4b0f-9c0a-dc188268d90f", "090c8998-1f16-4f8a-9b26-bc7ac9457ccc", "b4ddb19a-f7b3-4319-9b77-39e0cedf6508", "a0b86b3c-a9a0-47b0-a344-2f8282b88a70", "b586a812-c0ed-4f3a-aba9-79b08110e870", "ec29eeb5-2d58-11ee-a262-12e7c69eb75f", "c5346daf-725e-486e-9c97-086e3adccb76", "NOT_SPECIFIED"]}, {"fieldName": "creativeType", "operator": "in", "value": ["VIDEO", "IMAGE", "HTML", "ANIMATED_IMAGE"]}], "analyticsFilters": {"SNAPCHAT": [{"type": "creative_impressions", "values": {"operator": ">", "value": 15000000}}], "TIKTOK": [{"type": "campaign_objective", "values": ["RF_TRAFFIC", "RF_VIDEO_VIEW"]}], "TWITTER": [{"type": "ad_identifier", "values": ["iu37p", "jxkvu", "k7r3o"]}]}}}, "prod": {"workspaceId": "31869", "payload": {"filters": [{"fieldName": "mediaCreateDate", "operator": "between", "value": ["2017-01-01T00:00:00.000Z", "2023-12-31T00:00:00.000Z"]}, {"fieldName": "channel", "operator": "in", "value": ["ADWORDS", "AMAZON", "DV360", "FACEBOOK", "LINKEDIN", "PINTEREST", "REDDIT", "SNAPCHAT", "TIKTOK", "TWITTER"]}, {"fieldName": "market", "operator": "in", "value": ["afg", "ala", "alb", "dza", "asm", "and", "ago", "aia", "ata", "atg", "arg", "arm", "abw", "aus", "aut", "aze", "bhs", "bhr", "bgd", "brb", "blr", "bel", "blz", "ben", "bmu", "btn", "bol", "bih", "bwa", "bvt", "bra", "iot", "vgb", "brn", "bgr", "bfa", "bdi", "khm", "cmr", "can", "cpv", "cym", "caf", "tcd", "chl", "chn", "cxr", "cck", "col", "com", "cok", "cri", "civ", "hrv", "cub", "cyp", "cze", "prk", "dnk", "dji", "dma", "dom", "ecu", "egy", "slv", "gnq", "eri", "est", "swz", "eth", "flk", "fro", "fsm", "fji", "fin", "fra", "guf", "pyf", "atf", "gab", "gmb", "geo", "deu", "gha", "gib", "grc", "grl", "grd", "glp", "gum", "gtm", "gin", "gnb", "guy", "hti", "hmd", "hnd", "hkg", "hun", "isl", "ind", "idn", "irq", "irl", "irn", "imn", "isr", "ita", "jam", "jpn", "jor", "kaz", "ken", "kir", "kwt", "kgz", "lao", "lva", "lbn", "lso", "lbr", "lby", "lie", "ltu", "lux", "mac", "mdg", "mwi", "mys", "mdv", "mli", "mlt", "mhl", "mtq", "mrt", "mus", "myt", "mex", "mco", "mng", "msr", "mar", "moz", "mmr", "nam", "nru", "npl", "nld", "ant", "ncl", "nzl", "nic", "ner", "nga", "niu", "nfk", "mnp", "nor", "pse", "omn", "pak", "plw", "pan", "png", "pry", "per", "phl", "pcn", "pol", "prt", "pri", "qat", "kor", "mda", "cog", "reu", "rou", "rus", "rwa", "shn", "kna", "lca", "vct", "spm", "wsm", "smr", "stp", "sau", "sen", "srb", "scg", "syc", "sle", "sgp", "svk", "svn", "slb", "som", "zaf", "sgs", "esp", "lka", "sdn", "sur", "sjm", "swe", "che", "syr", "twn", "tjk", "tha", "cod", "mkd", "tls", "tgo", "tkl", "ton", "tto", "tun", "tur", "tkm", "tca", "tuv", "vir", "uga", "ukr", "are", "gbr", "tza", "usa", "umi", "ury", "uzb", "vut", "vat", "ven", "vnm", "wlf", "esh", "yem", "zmb", "zwe", "NOT_SPECIFIED"]}, {"fieldName": "workspaceId", "operator": "in", "value": ["31869"]}, {"fieldName": "brandId", "operator": "in", "value": ["ccb066da-75b0-4aac-9426-6cc8a71d9d22", "91ba287c-cf89-465e-9a25-178d2dd93270", "9615f69d-2ab9-4924-af1c-7bf402f36e62", "223cdb41-a68d-4f15-9cf9-5f8eee399e5e", "def0303d-2ec5-4c38-b819-076906bf58f2", "bf7af016-9f0b-4941-91d3-0bb2c0cbc6c6", "fe1b6c13-39bd-403f-b204-f656e6c56bd2", "0cdcd0ce-3de5-4fff-9ac0-d24bc89a51c4", "66db9040-ff35-43be-945c-10d1046009b2", "96267426-bea4-41af-9090-5e5d3cc2cb1c", "NOT_SPECIFIED"]}, {"fieldName": "creativeType", "operator": "in", "value": ["VIDEO", "IMAGE", "HTML", "ANIMATED_IMAGE"]}], "groupBy": {"columns": [], "rows": []}, "analyticsFilters": {"FACEBOOK": [{"type": "campaign_identifier", "values": ["6067231161602"]}, {"type": "adset_identifier", "values": ["6067712405002"]}, {"type": "media_type", "values": ["VIDEO", "IMAGE"]}, {"type": "created_by_vidmob", "values": ["false"]}], "LINKEDIN": [{"type": "campaign_objective", "values": ["LEAD_GENERATION", "WEBSITE_TRAFFIC", "ENGAGEMENT", "VIDEO_VIEWS", "BRAND_AWARENESS", "CREATIVE_ENGAGEMENT"]}], "PINTEREST": [{"type": "creative_impressions", "values": {"operator": ">", "value": 1000}}]}}}}