{"dev": {"idsOfCriteriaToTest": ["SNAPCHAT:SOUND_ON:{}:MANDATORY", "REDDIT:CTA_DETECTION:{}:MANDATORY", "PINTEREST:BRAND_NAME_OR_LOGO_ANYTIME:{}:MANDATORY"], "preFlight": {"columnsLength": 138, "rowsLength": 81, "columnsWithNormsDataLength": 0, "expectedFirstCriteria": {"id": "SNAPCHAT:SOUND_ON:{}:MANDATORY", "displayName": "Audio Present", "criteriaPlatform": "SNAPCHAT", "criteriaIsOptional": false, "criteriaIsBestPractice": true, "criteriaIdentifier": "SOUND_ON", "criteriaParameters": "{}", "inFlight": null, "preFlight": 27, "inFlightPassed": 0, "inFlightTotal": 0, "preFlightPassed": 22, "preFlightTotal": 81, "totalNorms": null, "totalNormsPassed": 0, "totalNormsTotal": 0}, "expectedSecondCriteria": null, "expectedThirdCriteria": {"id": "PINTEREST:BRAND_NAME_OR_LOGO_ANYTIME:{}:MANDATORY", "displayName": "Brand Name or Logo Present", "criteriaPlatform": "PINTEREST", "criteriaIsOptional": false, "criteriaIsBestPractice": true, "criteriaIdentifier": "BRAND_NAME_OR_LOGO_ANYTIME", "criteriaParameters": "{}", "inFlight": null, "preFlight": 1, "inFlightPassed": 0, "inFlightTotal": 0, "preFlightPassed": 3, "preFlightTotal": 285, "totalNorms": null, "totalNormsPassed": 0, "totalNormsTotal": 0}}, "inFlightNoAnalyticsFilters": {"columnsLength": 45, "rowsLength": 16, "columnsWithNormsDataLength": 0, "expectedFirstCriteria": {"id": "SNAPCHAT:SOUND_ON:{}:MANDATORY", "displayName": "Audio Present", "criteriaPlatform": "SNAPCHAT", "criteriaIsOptional": false, "criteriaIsBestPractice": true, "criteriaIdentifier": "SOUND_ON", "criteriaParameters": "{}", "inFlight": 0, "preFlight": null, "inFlightPassed": 0, "inFlightTotal": 32, "preFlightPassed": 0, "preFlightTotal": 0, "totalNorms": null, "totalNormsPassed": 0, "totalNormsTotal": 0}, "expectedSecondCriteria": null, "expectedThirdCriteria": {"id": "PINTEREST:BRAND_NAME_OR_LOGO_ANYTIME:{}:MANDATORY", "displayName": "Brand Name or Logo Present", "criteriaPlatform": "PINTEREST", "criteriaIsOptional": false, "criteriaIsBestPractice": true, "criteriaIdentifier": "BRAND_NAME_OR_LOGO_ANYTIME", "criteriaParameters": "{}", "inFlight": 13, "preFlight": null, "inFlightPassed": 104, "inFlightTotal": 800, "preFlightPassed": 0, "preFlightTotal": 0, "totalNorms": null, "totalNormsPassed": 0, "totalNormsTotal": 0}}, "inFlightWithAnalyticsFilters": {"columnsLength": 38, "rowsLength": 16, "columnsWithNormsDataLength": 0, "expectedFirstCriteria": {"id": "SNAPCHAT:SOUND_ON:{}:MANDATORY", "displayName": "Audio Present", "criteriaPlatform": "SNAPCHAT", "criteriaIsOptional": false, "criteriaIsBestPractice": true, "criteriaIdentifier": "SOUND_ON", "criteriaParameters": "{}", "inFlight": 0, "preFlight": null, "inFlightPassed": 0, "inFlightTotal": 32, "preFlightPassed": 0, "preFlightTotal": 0, "totalNorms": null, "totalNormsPassed": 0, "totalNormsTotal": 0}, "expectedSecondCriteria": null, "expectedThirdCriteria": null}}, "stage": {"idsOfCriteriaToTest": ["SNAPCHAT:ABI_EARLY_CTA:{\"maxFirstEarlyCTAPresence\":5}:MANDATORY", "TIKTOK:BRAND_NAME_OR_LOGO:{\"maxFirstBrandAppearance\":6}:MANDATORY", "TWITTER:BRAND_NAME_OR_LOGO_ANYTIME:{}:MANDATORY"], "preFlight": {"columnsLength": 80, "rowsLength": 726, "columnsWithNormsDataLength": 0, "expectedFirstCriteria": null, "expectedSecondCriteria": {"id": "TIKTOK:BRAND_NAME_OR_LOGO:{\"maxFirstBrandAppearance\":6}:MANDATORY", "displayName": "Brand Name or Logo Early", "criteriaPlatform": "TIKTOK", "criteriaIsOptional": false, "criteriaIsBestPractice": true, "criteriaIdentifier": "BRAND_NAME_OR_LOGO", "criteriaParameters": "{\"maxFirstBrandAppearance\":6}", "inFlight": null, "preFlight": 0, "inFlightPassed": 0, "inFlightTotal": 0, "preFlightPassed": 0, "preFlightTotal": 10, "totalNorms": null, "totalNormsPassed": 0, "totalNormsTotal": 0}, "expectedThirdCriteria": null}, "inFlightNoAnalyticsFilters": {"columnsLength": 90, "rowsLength": 15, "columnsWithNormsDataLength": 0, "expectedFirstCriteria": null, "expectedSecondCriteria": {"id": "TIKTOK:BRAND_NAME_OR_LOGO:{\"maxFirstBrandAppearance\":6}:MANDATORY", "displayName": "Brand Name or Logo Early", "criteriaPlatform": "TIKTOK", "criteriaIsOptional": false, "criteriaIsBestPractice": true, "criteriaIdentifier": "BRAND_NAME_OR_LOGO", "criteriaParameters": "{\"maxFirstBrandAppearance\":6}", "inFlight": 3, "preFlight": null, "inFlightPassed": 22, "inFlightTotal": 736, "preFlightPassed": 0, "preFlightTotal": 0, "totalNorms": null, "totalNormsPassed": 0, "totalNormsTotal": 0}, "expectedThirdCriteria": {"id": "TWITTER:BRAND_NAME_OR_LOGO_ANYTIME:{}:MANDATORY", "displayName": "Brand Name or Logo Present", "criteriaPlatform": "TWITTER", "criteriaIsOptional": false, "criteriaIsBestPractice": false, "criteriaIdentifier": "BRAND_NAME_OR_LOGO_ANYTIME", "criteriaParameters": "{}", "inFlight": 4, "preFlight": null, "inFlightPassed": 2, "inFlightTotal": 56, "preFlightPassed": 0, "preFlightTotal": 0, "totalNorms": null, "totalNormsPassed": 0, "totalNormsTotal": 0}}, "inFlightWithAnalyticsFilters": {"columnsLength": 90, "rowsLength": 15, "columnsWithNormsDataLength": 0, "expectedFirstCriteria": null, "expectedSecondCriteria": {"id": "TIKTOK:BRAND_NAME_OR_LOGO:{\"maxFirstBrandAppearance\":6}:MANDATORY", "displayName": "Brand Name or Logo Early", "criteriaPlatform": "TIKTOK", "criteriaIsOptional": false, "criteriaIsBestPractice": true, "criteriaIdentifier": "BRAND_NAME_OR_LOGO", "criteriaParameters": "{\"maxFirstBrandAppearance\":6}", "inFlight": 0, "preFlight": null, "inFlightPassed": 0, "inFlightTotal": 314, "preFlightPassed": 0, "preFlightTotal": 0, "totalNorms": null, "totalNormsPassed": 0, "totalNormsTotal": 0}, "expectedThirdCriteria": {"id": "TWITTER:BRAND_NAME_OR_LOGO_ANYTIME:{}:MANDATORY", "displayName": "Brand Name or Logo Present", "criteriaPlatform": "TWITTER", "criteriaIsOptional": false, "criteriaIsBestPractice": false, "criteriaIdentifier": "BRAND_NAME_OR_LOGO_ANYTIME", "criteriaParameters": "{}", "inFlight": 4, "preFlight": null, "inFlightPassed": 2, "inFlightTotal": 56, "preFlightPassed": 0, "preFlightTotal": 0, "totalNorms": null, "totalNormsPassed": 0, "totalNormsTotal": 0}}}, "prod": {"idsOfCriteriaToTest": ["FACEBOOK:ABI_DYNAMIC_START:{\"maxMotionPresenceTime\":1}:MANDATORY", "LINKEDIN:SOUND_OFF:{}:MANDATORY", "PINTEREST:VIDEO_DURATION:{\"maxDuration\":10,\"minDuration\":0}:MANDATORY"], "preFlight": {"columnsLength": 109, "rowsLength": 484, "columnsWithNormsDataLength": 0, "expectedFirstCriteria": {"id": "FACEBOOK:ABI_DYNAMIC_START:{\"maxMotionPresenceTime\":1}:MANDATORY", "displayName": "Motion Early", "criteriaPlatform": "FACEBOOK", "criteriaIsOptional": false, "criteriaIsBestPractice": true, "criteriaIdentifier": "ABI_DYNAMIC_START", "criteriaParameters": "{\"maxMotionPresenceTime\":1}", "inFlight": null, "preFlight": 69, "inFlightPassed": 0, "inFlightTotal": 0, "preFlightPassed": 543, "preFlightTotal": 792, "totalNorms": null, "totalNormsPassed": 0, "totalNormsTotal": 0}, "expectedSecondCriteria": {"id": "LINKEDIN:SOUND_OFF:{}:MANDATORY", "displayName": "Text Present", "criteriaPlatform": "LINKEDIN", "criteriaIsOptional": false, "criteriaIsBestPractice": true, "criteriaIdentifier": "SOUND_OFF", "criteriaParameters": "{}", "inFlight": null, "preFlight": 68, "inFlightPassed": 0, "inFlightTotal": 0, "preFlightPassed": 517, "preFlightTotal": 764, "totalNorms": null, "totalNormsPassed": 0, "totalNormsTotal": 0}, "expectedThirdCriteria": {"id": "PINTEREST:VIDEO_DURATION:{\"maxDuration\":10,\"minDuration\":0}:MANDATORY", "displayName": "Video Length Within Range", "criteriaPlatform": "PINTEREST", "criteriaIsOptional": false, "criteriaIsBestPractice": false, "criteriaIdentifier": "VIDEO_DURATION", "criteriaParameters": "{\"maxDuration\":10,\"minDuration\":0}", "inFlight": null, "preFlight": 1, "inFlightPassed": 0, "inFlightTotal": 0, "preFlightPassed": 4, "preFlightTotal": 760, "totalNorms": null, "totalNormsPassed": 0, "totalNormsTotal": 0}}, "inFlightWithAnalyticsFilters": {"columnsLength": 17, "rowsLength": 2, "columnsWithNormsDataLength": 0, "expectedFirstCriteria": {"id": "FACEBOOK:ABI_DYNAMIC_START:{\"maxMotionPresenceTime\":1}:MANDATORY", "displayName": "Motion Early", "criteriaPlatform": "FACEBOOK", "criteriaIsOptional": false, "criteriaIsBestPractice": true, "criteriaIdentifier": "ABI_DYNAMIC_START", "criteriaParameters": "{\"maxMotionPresenceTime\":1}", "inFlight": 93, "preFlight": null, "inFlightPassed": 13, "inFlightTotal": 14, "preFlightPassed": 0, "preFlightTotal": 0, "totalNorms": null, "totalNormsPassed": 0, "totalNormsTotal": 0}, "expectedSecondCriteria": null, "expectedThirdCriteria": null}, "inFlightNoAnalyticsFilters": {"columnsLength": 19, "rowsLength": 2, "columnsWithNormsDataLength": 0, "expectedFirstCriteria": {"id": "FACEBOOK:ABI_DYNAMIC_START:{\"maxMotionPresenceTime\":1}:MANDATORY", "displayName": "Motion Early", "criteriaPlatform": "FACEBOOK", "criteriaIsOptional": false, "criteriaIsBestPractice": true, "criteriaIdentifier": "ABI_DYNAMIC_START", "criteriaParameters": "{\"maxMotionPresenceTime\":1}", "inFlight": 85, "preFlight": null, "inFlightPassed": 57, "inFlightTotal": 67, "preFlightPassed": 0, "preFlightTotal": 0, "totalNorms": null, "totalNormsPassed": 0, "totalNormsTotal": 0}, "expectedSecondCriteria": null, "expectedThirdCriteria": null}}}