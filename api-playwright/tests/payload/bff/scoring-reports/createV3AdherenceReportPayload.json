{"dev": {"workspaceId": "23142", "payload": {"filters": [{"fieldName": "mediaCreateDate", "operator": "between", "value": ["2022-12-18", "2025-03-18"]}, {"fieldName": "workspaceId", "operator": "in", "value": [23142]}, {"fieldName": "batchType", "operator": "equals", "value": "IN_FLIGHT"}, {"fieldName": "creativeType", "operator": "in", "value": ["VIDEO", "IMAGE", "ANIMATED_IMAGE", "HTML"]}, {"fieldName": "channel", "operator": "in", "value": ["LINKEDIN"]}, {"fieldName": "entityType", "operator": "equals", "value": "AD_ASSET"}], "analyticsFilters": {"LINKEDIN": [{"type": "campaign_identifier", "operator": "in", "values": ["196329843", "207857434", "217806793", "219096133", "219096743", "219493303", "220164763", "223889643", "225257243", "227298593", "274676863", "275966413", "275983133", "276027763", "276044953"]}]}, "groupBy": {"columns": ["brand", "market"], "rows": ["criteriaGroup", "criteria"]}, "entityType": "AD_ASSET"}}, "stage": {"workspaceId": "2735", "payload": {"filters": [{"fieldName": "mediaCreateDate", "operator": "between", "value": ["2020-03-11", "2025-04-10"]}, {"fieldName": "workspaceId", "operator": "in", "value": [2735]}, {"fieldName": "creativeType", "operator": "in", "value": ["VIDEO", "IMAGE", "ANIMATED_IMAGE", "HTML"]}, {"fieldName": "batchType", "operator": "equals", "value": "PRE_FLIGHT"}, {"fieldName": "channel", "operator": "in", "value": ["FACEBOOK", "DV360", "LINKEDIN"]}], "analyticsFilters": {}, "groupBy": {"columns": ["market", "workspace"], "rows": ["channel", "criteria"]}, "entityType": "AD"}}, "prod": {"workspaceId": "31869", "payload": {"filters": [{"fieldName": "mediaCreateDate", "operator": "between", "value": ["2020-01-01", "2025-01-01"]}, {"fieldName": "workspaceId", "operator": "in", "value": [31869]}, {"fieldName": "creativeType", "operator": "in", "value": ["VIDEO", "IMAGE", "ANIMATED_IMAGE", "HTML"]}, {"fieldName": "batchType", "operator": "equals", "value": "IN_FLIGHT"}, {"fieldName": "channel", "operator": "in", "value": ["LINKEDIN", "TWITTER", "TIKTOK"]}, {"fieldName": "entityType", "operator": "equals", "value": "AD_ASSET"}], "analyticsFilters": {}, "groupBy": {"columns": ["workspace"], "rows": ["criteriaGroup", "criteria"]}, "entityType": "AD_ASSET"}}}