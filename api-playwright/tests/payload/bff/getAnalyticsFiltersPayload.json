{"dev": {"organizationId": "27a7e882-43de-4bfa-8f53-3b62875b8432", "facebookIdentifierFilterPayload": {"filterType": "adSetIdentifier", "workspaceIds": [23245], "platform": "FACEBOOK", "adAccountIds": ["***************"], "dateRange": {"startDate": "2020-01-01", "endDate": "2022-01-01"}}, "instagramPageIdentifierFilterPayload": {"filterType": "adIdentifier", "workspaceIds": [23245], "platform": "INSTAGRAMPAGE", "adAccountIds": ["*****************"], "dateRange": {"startDate": "1970-01-01", "endDate": "2022-01-01"}}, "tiktokIdentifierFilterPayload": {"platform": "TIKTOK", "filterType": "campaignIdentifier", "workspaceIds": [23245, 23142], "adAccountIds": ["6807608752410198022", "7190025940280344577"], "dateRange": {"startDate": "2019-01-01", "endDate": "2020-12-31"}}}, "stage": {"organizationId": "cb90fd8c-4740-43c6-9def-98d9f8eb5a0b", "facebookIdentifierFilterPayload": {"platform": "FACEBOOK", "filterType": "adSetIdentifier", "workspaceIds": [2735, 4433], "adAccountIds": ["***************"], "dateRange": {"startDate": "2020-01-01", "endDate": "2022-01-01"}}, "instagramPageIdentifierFilterPayload": {"filterType": "adIdentifier", "workspaceIds": [2735, 4433], "platform": "INSTAGRAMPAGE", "adAccountIds": ["*****************"], "dateRange": {"startDate": "1970-01-01", "endDate": "2022-01-01"}}, "tiktokIdentifierFilterPayload": {"platform": "TIKTOK", "filterType": "campaignIdentifier", "workspaceIds": [2735, 4433], "adAccountIds": ["7203488529354063874", "7190025940280344577"], "dateRange": {"startDate": "2022-01-01", "endDate": "2023-12-31"}}}, "prod": {"organizationId": "421fb903-40bc-4e59-beb5-6b026813752c", "facebookIdentifierFilterPayload": {"platform": "FACEBOOK", "filterType": "adSetIdentifier", "workspaceIds": [31869], "adAccountIds": ["***************"], "dateRange": {"startDate": "2020-01-01", "endDate": "2022-01-01"}}, "instagramPageIdentifierFilterPayload": {"filterType": "adIdentifier", "workspaceIds": [31869], "platform": "INSTAGRAMPAGE", "adAccountIds": ["*****************"], "dateRange": {"startDate": "1970-01-01", "endDate": "2022-01-01"}}, "tiktokIdentifierFilterPayload": {"platform": "TIKTOK", "filterType": "campaignIdentifier", "workspaceIds": [31869], "adAccountIds": ["6807608752410198022", "7190025940280344577"], "dateRange": {"startDate": "2019-01-01", "endDate": "2020-12-31"}}}}