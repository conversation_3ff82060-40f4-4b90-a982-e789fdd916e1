{"dev": {"empty": {"platform": "FACEBOOK", "adAccountIds": ["***************"], "workspacesIds": [23142, 23245], "organizationId": "27a7e882-43de-4bfa-8f53-3b62875b8432", "criteria": [{"type": "tag", "value": {"type": "CTA:BY_LINE", "confidence": 75, "name": "Get started"}, "appearance": {"present": true, "type": "any"}}, {"type": "DATE", "startDate": "2023-01-01", "endDate": "2023-12-31"}]}, "notEmpty": {"platform": "FACEBOOK", "adAccountIds": ["***************"], "workspacesIds": [23142, 23245], "organizationId": "27a7e882-43de-4bfa-8f53-3b62875b8432", "criteria": [{"type": "tag", "value": {"type": "gaze and emotion", "confidence": 75, "name": "Gaze Left"}, "appearance": {"present": true, "type": "any"}}, {"type": "DATE", "startDate": "2023-01-01", "endDate": "2023-12-31"}], "advancedFilters": {"campaignIdentifiers": ["*************", "*************"], "mediaTypes": ["IMAGE", "VIDEO"], "adTypes": ["desktop:facebook:video_feeds", "mobile_app:facebook:video_feeds", "mobile:facebook:video_feeds", "unknown:facebook:video_feeds"]}}}, "stage": {"empty": {"platform": "LINKEDIN", "adAccountIds": ["*********", "*********"], "workspacesIds": [4433, 2735], "organizationId": "cb90fd8c-4740-43c6-9def-98d9f8eb5a0b", "operator": "AND", "criteria": [{"type": "tag", "value": {"type": "branding", "confidence": 75, "name": "Logo: Vidmob"}, "appearance": {"present": true, "type": "any"}}, {"type": "DATE", "startDate": "2020-01-01", "endDate": "2023-12-31"}]}, "notEmpty": {"platform": "LINKEDIN", "adAccountIds": ["*********", "*********"], "workspacesIds": [4433, 2735], "organizationId": "cb90fd8c-4740-43c6-9def-98d9f8eb5a0b", "operator": "AND", "criteria": [{"type": "tag", "value": {"type": "color", "confidence": 75, "name": "Cool Color Temperature"}, "appearance": {"present": true, "type": "any"}}, {"type": "DATE", "startDate": "2020-01-01", "endDate": "2023-12-31"}], "advancedFilters": {"mediaTypes": ["VIDEO"], "kpiId": "320", "createdByVidmob": "true", "adImpressions": {"greaterThanOrEqualTo": 1000}, "campaignObjectives": ["VIDEO_VIEWS", "CONVERSATION_STARTER", "TALENT_LEAD", "WEBSITE_CONVERSION", "LEAD_GENERATION", "VIDEO_VIEW", "BRAND_AWARENESS", "CREATIVE_ENGAGEMENT", "WEBSITE_TRAFFIC", "ENGAGEMENT"]}}}, "prod": {"empty": {"platform": "PINTEREST", "adAccountIds": ["************"], "workspacesIds": [31869], "organizationId": "421fb903-40bc-4e59-beb5-6b026813752c", "operator": "AND", "criteria": [{"type": "tag", "value": {"type": "object", "confidence": 75, "name": "Text"}, "appearance": {"present": false, "type": "any"}}, {"type": "DATE", "startDate": "2019-01-01", "endDate": "2024-01-06"}], "advancedFilters": {"mediaTypes": ["VIDEO", "IMAGE"], "kpiId": "213"}}, "notEmpty": {"platform": "PINTEREST", "adAccountIds": ["************"], "workspacesIds": [31869], "organizationId": "421fb903-40bc-4e59-beb5-6b026813752c", "operator": "AND", "criteria": [{"type": "tag", "value": {"type": "object", "confidence": 75, "name": "Text"}, "appearance": {"present": true, "type": "any"}}, {"type": "DATE", "startDate": "2019-01-01", "endDate": "2024-01-06"}], "advancedFilters": {"mediaTypes": ["VIDEO", "IMAGE"], "adIdentifiers": ["************", "************"], "creativeImpressions": {"greaterThanOrEqualTo": 10}, "kpiId": "213"}}}}